﻿@page "/view/announcements"
@page "/view/newsanevents"
@page "/view/digitalearning"
@page "/view/digitalearning/{CategoryId:int}"
@page "/view/dm/{CategoryId:int}"
@page "/view/policies"
@inject IWebHostEnvironment env

@using PakSuzuki.DTO
@using FilterType = Syncfusion.Blazor.DropDowns.FilterType
@inject AppDataService service
@inject AuthenticationStateProvider AuthenticationStateProvider
@inject NavigationManager NavigationManager

<link href="/styles/news-card.css" rel="stylesheet" />
<style>
    .news {
        height: unset;
        min-height: 180px;
        align-items: flex-start;
        background-color: white;
        margin: 6px;
        padding: 10px;
        cursor: pointer;
    }

    .anns {
        padding-left: 10px;
        padding-right: 10px;
        margin-top: 10px;
        background-color: white;
        padding-top: 10px;
        margin-left: 10px;
        margin-right: 10px;
    }

    .news img {
        max-width: 250px;
        min-width: 250px;
        max-height: unset;
    }

    .news p {
        margin-bottom: 1rem;
        font-size: 12px;
        color: black;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
        height: 90px;
    }

    .ann-card {
        display: flex;
        gap: 10px;
        margin-bottom: 15px;
        cursor: pointer;
    }

        .ann-card .ann-thumbnail {
            width: 300px;
            height: 181px;
            object-fit: cover;
        }

        .ann-card .ann-thumbnailsmall {
            width: 150px;
            height: 90px;
            object-fit: cover;
        }

        .ann-card h3 {
            font-weight: bold;
            color: #013582;
        }

        .ann-card p {
            font-size: 12px;
        }

        .ann-card span {
            font-size: 12px;
            color: darkgray;
        }

        .ann-card .ann-content {
            margin: 10px 0 10px 0;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }
</style>
<SfDialog @ref="detailForm" ShowCloseIcon="true" IsModal="true" Width="900px" CloseOnEscape="true" Visible="IsDetailVisible">
    <DialogTemplates>
        <Header>@_section - @postDetail.postName</Header>
        <Content>
            <div class="popup">

                <div class="row">
                    <div class="col">
                        <img src="@postDetail!.postHeadImagePath" width="100%" height="340px" style="object-fit: cover;object-position:top" onerror="this.onerror=null;this.src='/images/newsandevents.png'" />


                    </div>
                </div>
                <div class="row">
                    <div class="col-md">
                        <h2 class="card-title" style="font-size: 18px;">@postDetail.postName</h2>
                        <p style="padding:10px;">@postDetail.postPublishDateStr</p>
                        <div style="padding:10px;padding-left:20px;">@((MarkupString)postDetail.postContentHTML)</div>
                    </div>
                </div>
                @if (postDetail.Tags.Any())
                {
                    <div class="row mt-2">
                        <div class="col-md">
                            <h4 style="font-size:17px;margin-bottom:15px">Tags</h4>
                            <p></p>
                            @foreach (var tag in postDetail.Tags)
                            {
                                <span style="margin:5px;" class="tag">@tag</span>
                            }
                        </div>
                    </div>
                }

                @if (postDetail.Attachments.Any())
                {
                    <div class="row mt-2">
                        <div class="col-md">
                            <h4 style="font-size:17px;margin-bottom:15px">Attachments</h4>
                            <p></p>
                            @foreach (var tag in postDetail.Attachments)
                            {
                                <a target="_blank" style="margin:5px;" class="tag" href="@tag.Url">@tag.Name</a>
                            }
                        </div>
                    </div>
                }
            </div>
        </Content>
    </DialogTemplates>

</SfDialog>
<div>
    <SfBreadcrumb>
        <BreadcrumbItems>
            <BreadcrumbItem IconCss="e-icons e-home" Url="/" />
            <BreadcrumbItem Text="@_section" />
        </BreadcrumbItems>
    </SfBreadcrumb>
    <h3 style="padding-left:10px;" class="sec-title">@_section @_categoryTitle</h3>
    <div class="p-2" style="display:flex;gap:10px;justify-content:space-between;align-items:center">
        <div style="flex:1">
            <SfTextBox Placeholder="Search" FloatLabelType="FloatLabelType.Never" @bind-Value="_searchTag" />
        </div>
        @if (CategoryId != null)
        {
            <div style="width: 180px">
                <SfDropDownList AllowFiltering="true" FilterType="FilterType.Contains" Placeholder="Sub Category"
                                ShowClearButton="true" @bind-Value="_subCategoryId" DataSource="_subCategoriesList" 
                                FloatLabelType="FloatLabelType.Never">
                    <DropDownListFieldSettings Value="Id" Text="Title"></DropDownListFieldSettings>
                </SfDropDownList>
            </div>
        }
        <MudButton Size="Size.Small" Color="Color.Primary" Variant="Variant.Filled"
                   StartIcon="@Icons.Material.Filled.Search"
                   OnClick="filterSearch">
            Search
        </MudButton>
        <MudButton Size="Size.Small" Color="Color.Secondary" Variant="Variant.Filled"
                   StartIcon="@Icons.Material.Filled.Undo"
                   OnClick="resetSearch">
            Reset
        </MudButton>
    </div>
    <div class="anns">
        @foreach (var k in AnnouncementsList)
        {
            if (_section == "Policies")
            {
                var at = "#";
                var tg = $"po{k.postId}";
                if (k.Attachments != null && k.Attachments.Any())
                {
                    at = k.Attachments[0].Url;
                }
                <a class="ann-card" href="@at" target="@tg">
                    <div>
                        <img class="ann-thumbnailsmall" src="@k.postThumbnailPath" alt="" onerror="this.onerror=null;this.src='/images/newsandevents.png'" />
                    </div>
                    <div class="ann-content">
                        <h3>@k.postName</h3>
                        @if (k.postContentText.Length > 500)
                        {
                            <p>
                                @k.postContentText.Substring(0, 500) <span>more...</span>
                            </p>
                        }
                        else
                        {
                            <p>@k.postContentText</p>
                        }
                        <p></p>
                        <span>@k.postPublishDateStr</span>
                    </div>
                </a>
            }
            else
            {
                <div class="ann-card" @onclick="() => ShowDetail(k.postId)">
                    <div>
                        <img class="ann-thumbnail" src="@k.postThumbnailPath" alt="" onerror="this.onerror=null;this.src='/images/newsandevents.png'" />
                    </div>
                    <div class="ann-content">
                        <h3>@k.postName</h3>
                        @if (k.postContentText.Length > 500)
                        {
                            <p>
                                @k.postContentText.Substring(0, 500) <span>more...</span>
                            </p>
                        }
                        else
                        {
                            <p>@k.postContentText</p>
                        }
                        <p></p>
                        <span>@k.postPublishDateStr</span>
                    </div>
                </div>
            }
            <div class="separator"></div>
        }



    </div>
</div>

@code {
    private List<PostDTO> AnnouncementsList { get; set; } = new();
    private SfDialog detailForm;
    private PostDTO? postDetail;
    private readonly bool IsDetailVisible = false;
    private string _section = "";
    private string _currentUri = "";
    private string _searchTag { get; set; } = "";
    [Parameter]
    public int? CategoryId { get; set; }

    public int? _subCategoryId { get; set; }
    private List<SubCategoryDto> _subCategoriesList = new();

    private string _categoryTitle = "";



    protected override async Task OnParametersSetAsync()
    {
        await base.OnParametersSetAsync();
        _currentUri = NavigationManager.Uri;
        await DoSearch();
        if (CategoryId != null)
        {
            _categoryTitle = await service.GetCategoryTitle(CategoryId ?? 0);
            _subCategoriesList = await service.SetAllSubCategories(CategoryId ?? 0);
        }
    }


    public async Task ShowDetail(int postId)
    {
        postDetail = await service.GetPostDetail(postId);
        {
            if (postDetail != null)
            {
                await detailForm.ShowAsync();
            }
        }
    }


    private async Task resetSearch()
    {
        _searchTag = "";
        await DoSearch(_searchTag, _subCategoryId ?? -1);
    }

    private async Task filterSearch()
    {
        await DoSearch(_searchTag, _subCategoryId ?? -1);
    }

    private async Task DoSearch(string tag = "", int subCategoryId = -1)
    {
        var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        var user = authState.User;

        if (_currentUri.EndsWith("/view/announcements"))
        {
            if (user.Identity is { Name: not null })
            {
                AnnouncementsList = await service.GetUserPosts(user.Identity.Name, Section.Announcements, _searchTag);
                _section = "Announcements";
            }
        }
        //"/view/newsanevents"
        if (_currentUri.EndsWith("/view/newsanevents"))
        {
            if (user.Identity is { Name: not null })
            {
                AnnouncementsList = await service.GetUserPosts(user.Identity.Name, Section.NewsAneEvents, _searchTag);
                _section = "News and Events";
            }
        }
        if (_currentUri.Contains("/view/digitalearning"))
        {
            if (user.Identity is { Name: not null })
            {

                AnnouncementsList = await service.GetUserPosts(user.Identity.Name, Section.DigitalLearning, _searchTag, CategoryId ?? -1, _subCategoryId ?? -1);
                foreach (var al in AnnouncementsList)
                {
                    var w = env.WebRootPath + al.postThumbnailPath.Replace("/", "\\");
                    if (File.Exists(w) == false)
                    {
                        al.postThumbnailPath = "/images/newsandevents.png";
                    }
                }
                _section = "Digital Learning";
                this.StateHasChanged();
            }
        }
        if (_currentUri.Contains("/view/dm/"))
        {
            if (user.Identity is { Name: not null })
            {

                AnnouncementsList = await service.GetUserPosts(user.Identity.Name, Section.DocumentationManagement, _searchTag, CategoryId ?? -1, _subCategoryId ?? -1);
                foreach (var al in AnnouncementsList)
                {
                    var w = env.WebRootPath + al.postThumbnailPath.Replace("/", "\\");
                    if (File.Exists(w) == false)
                    {
                        al.postThumbnailPath = "/images/newsandevents.png";
                    }
                }
                _section = "Document Management";
                this.StateHasChanged();
            }
        }
        if (_currentUri.EndsWith("/view/policies"))
        {
            if (user.Identity is { Name: not null })
            {
                AnnouncementsList = await service.GetUserPosts(user.Identity.Name, Section.policy, _searchTag);
                _section = "Policies";
            }
        }
        //"/view/policies"
    }

}