﻿@page "/setup/module-sections"
@inject AppDataService Service
@inject IJSRunner js
<SfDialog @ref="dlgForm" IsModal="true" ShowCloseIcon="true" CloseOnEscape="true" Width="600px" Visible="false">
    <DialogTemplates>
        <Header>Section Detail</Header>
        <Content>
            <EditForm Model="_sectionObj" OnValidSubmit="SaveSection">
                <div class="row">
                    <div class="col">
                        <DataAnnotationsValidator />
                        <DataAnnotationsValidator />
                    </div>
                </div>
                <div class="row">
                    <div class="col mb-2">
                        <SfTextBox Placeholder="Name" FloatLabelType="FloatLabelType.Always"
                                   @bind-Value="_sectionObj.Name"></SfTextBox>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md mb-2">
                        <SfDropDownList DataSource="_postTypesList" Placeholder="Post Type"
                                        @bind-Value="_sectionObj.PostTypeId"
                                        FloatLabelType="FloatLabelType.Always">
                            <DropDownListFieldSettings Value="@nameof(PostTypeDto.Id)" Text="@nameof(PostTypeDto.Name)"></DropDownListFieldSettings>
                        </SfDropDownList>
                    </div>
                    <div class="col-md mb-2">
                        <SfNumericTextBox Placeholder="Sort Order" FloatLabelType="FloatLabelType.Always"
                                          Decimals="0" ShowClearButton="false" Min="1" Max="500"
                                          Format="####" ShowSpinButton="true"
                                          TValue="int"
                                          @bind-Value="_sectionObj.SortOrder"></SfNumericTextBox>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md mb-2">
                        <SfCheckBox @bind-Checked="_sectionObj.IsActive" Label="Active" LabelPosition="Syncfusion.Blazor.Buttons.LabelPosition.After"></SfCheckBox>
                    </div>
                </div>
                <div class="row">
                    <div class="col">
                        <MudButton ButtonType="MudBlazor.ButtonType.Submit" Color="Color.Primary" Variant="Variant.Filled" Size="Size.Small" StartIcon="@Icons.Material.Filled.Save">Save</MudButton>
                    </div>
                </div>
            </EditForm>
        </Content>
    </DialogTemplates>
</SfDialog>
<div class="container">
    <div class="row">
        <div class="col mb-2">
            <MudText Typo="Typo.h5">Module Sections</MudText>
        </div>
    </div>
    <div class="row">
        <div class="col-md mb-2">
            <SfDropDownList Placeholder="Module"
                            @bind-Value="_moduleId"
                            DataSource="ModuleList"
                            FloatLabelType="FloatLabelType.Always">
                <DropDownListFieldSettings Value="@nameof(ModuleDto.Id)" Text="@nameof(ModuleDto.Name)"></DropDownListFieldSettings>
                <DropDownListEvents TValue="int?" TItem="ModuleDto" ValueChange="OnModuleChange"></DropDownListEvents>
            </SfDropDownList>
        </div>
    </div>
    @if (_moduleId != null)
    {
        <div class="row">
            <div class="col">
                <MudButton Size="Size.Small" Variant="Variant.Filled" Color="Color.Primary"
                           OnClick="OpenNewSection"
                           StartIcon="@Icons.Material.Filled.Add">Create</MudButton>
            </div>
        </div>
        <div class="row">
            <div class="col mb-2">
                <SfGrid DataSource="moduleSections" AllowFiltering="true" AllowSorting="true">
                    <GridFilterSettings Type="Syncfusion.Blazor.Grids.FilterType.Excel"></GridFilterSettings>
                    <GridColumns>
                        <GridColumn HeaderText="Name" Field="@nameof(SectionDto.Name)" AutoFit="true"></GridColumn>
                        <GridColumn HeaderText="Post Type" Field="@nameof(SectionDto.PostType)" AutoFit="true"></GridColumn>
                        <GridColumn HeaderText="Sort Order" Field="@nameof(SectionDto.SortOrder)" AutoFit="true"></GridColumn>
                        <GridColumn HeaderText="Status" Field="@nameof(SectionDto.Status)" AutoFit="true"></GridColumn>
                        <GridColumn HeaderText="Action">
                            <Template Context="cc">
                                @{
                                    var obj = cc as SectionDto;
                                    if (obj != null)
                                    {
                                        <MudFab OnClick="@(() => EditSection(obj.Id))" Color="Color.Primary" Size="Size.Small" StartIcon="@Icons.Material.Filled.Edit"></MudFab>
                                        <MudFab OnClick="@(() => DeleteSection(obj.Id))" Color="Color.Error" Size="Size.Small" StartIcon="@Icons.Material.Filled.Delete"></MudFab>
                                    }
                                }
                            </Template>
                        </GridColumn>
                    </GridColumns>
                </SfGrid>
            </div>
        </div>
    }

</div>

@code {
    private int? _moduleId;
    private List<ModuleDto> ModuleList = new List<ModuleDto>();
    private List<SectionDto> moduleSections = new List<SectionDto>();
    private SfDialog? _dlgForm;
    private SectionDto? _sectionObj = new SectionDto();
    private List<PostTypeDto> _postTypesList = new List<PostTypeDto>();


    // on initialize
    protected override async Task OnInitializedAsync()
    {
        ModuleList = await Service.GetModules();

        _postTypesList = await Service.GetPostTypes();

    }
    private async Task OpenNewSection()
    {
        _sectionObj = new SectionDto() { IsActive = true, ModuleId = _moduleId };
        await dlgForm!.ShowAsync();
    }

    private async Task OnModuleChange(ChangeEventArgs<int?, ModuleDto> args)
    {
        _moduleId = args.Value;
        moduleSections = await Service.GetModuleSections(_moduleId ?? 0);
    }

    private async Task DeleteSection(int id)
    {
        bool confirm = true;

        if (confirm)
        {
            string msg = await Service.DeleteSection(id);
            if (msg == "Success")
            {
                moduleSections = await Service.GetModuleSections(_moduleId ?? 0);
            }
        }
    }
    private async Task EditSection(int id)
    {
        _sectionObj = await Service.GetSectionById(id);
        await dlgForm!.ShowAsync();
    }

    private SfDialog? dlgForm;

    private async Task SaveSection()
    {
        string msg = await Service.SaveSection(_sectionObj);
        await dlgForm!.HideAsync();
        moduleSections = await Service.GetModuleSections(_moduleId ?? 0);
    }

    private async Task GetSectionById(int id)
    {
        _sectionObj = await Service.GetSectionById(id);
        await dlgForm!.ShowAsync();
    }

}
