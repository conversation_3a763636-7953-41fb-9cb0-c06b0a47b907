﻿@page "/setup/shift-and-locations"
@using FilterType = Syncfusion.Blazor.Grids.FilterType
@inject LocationDataService service
@* inject javascript *@
@inject IJSRuntime js


@rendermode InteractiveServer

<SfToast @ref="@toastObj"/>

<h3>Shift and Locations</h3>

@*<MudButton StartIcon="@Icons.Material.Filled.Add" Color="Color.Primary" Size="Size.Small" Variant="Variant.Filled" @onclick="OpenCreateForm">Add New Location</MudButton>*@
<div class="row mb-2">
    <div class="col-md">
        <MudButton Size="Size.Small" Color="Color.Primary" StartIcon="@Icons.Material.Filled.Add"
                   Variant="Variant.Filled" OnClick="OpenCreateForm">Add
        </MudButton>
        <MudButton Size="Size.Small" Color="Color.Success" StartIcon="@Icons.Material.Filled.Edit"
                   Variant="Variant.Filled" OnClick="OpenEditForm">Edit
        </MudButton>
        <MudButton Size="Size.Small" Color="Color.Warning" StartIcon="@Icons.Material.Filled.Delete"
                   Variant="Variant.Filled" OnClick="ConfirmDelete">Delete
        </MudButton>
    </div>
</div>


<SfGrid @ref="@dgMain" DataSource="@locations" AllowPaging="true" PageSize="10" AllowFiltering="true"
        AllowSorting="true" AllowSelection="true">
    <GridFilterSettings Type="FilterType.Excel"></GridFilterSettings>
    <GridColumns>
        <GridColumn AutoFit="true" Field="@nameof(LocationDto.LocationCode)" HeaderText="Location Code"></GridColumn>
        <GridColumn AutoFit="true" Field="@nameof(LocationDto.LocationTxt)" HeaderText="Location Text"></GridColumn>
        <GridColumn AutoFit="true" Field="@nameof(LocationDto.EmployeeShiftCode)"
                    HeaderText="Employee Shift Code"></GridColumn>
        <GridColumn AutoFit="true" Field="@nameof(LocationDto.ShiftStart)" Format="h:mm tt"
                    HeaderText="Shift Start"></GridColumn>
        <GridColumn AutoFit="true" Field="@nameof(LocationDto.ShiftEnd)" Format="h:mm tt"
                    HeaderText="Shift End"></GridColumn>
        @*<GridColumn AutoFit="true" HeaderText="Actions">
            <Template>
                @{
                    var location = context as LocationDto;
                }
                <div>
                    <MudFab Size="Size.Small" Color="Color.Primary" StartIcon="@Icons.Material.Filled.Edit" @onclick="@(() => EditLocation(location))"></MudFab>
                    <MudFab Size="Size.Small" Color="Color.Error" StartIcon="@Icons.Material.Filled.Delete" @onclick="@(() => DeleteLocation(location.Id))"></MudFab>
                </div>
            </Template>
        </GridColumn>*@
    </GridColumns>
</SfGrid>

<SfDialog @ref="@locationDialog" Width="500px" IsModal="true" ShowCloseIcon="true" Header="Location Details"
          Visible="IsFormVisible">
    <DialogTemplates>
        <Content>
            <div class="row mb-2">
                <div class="col-md-4">
                    <SfTextBox Placeholder="Location Code" FloatLabelType="FloatLabelType.Always"
                               @bind-Value="location.LocationCode" ID="locationCode"></SfTextBox>
                </div>
                <div class="col-md">
                    <SfTextBox Placeholder="Location" FloatLabelType="FloatLabelType.Always"
                               @bind-Value="location.LocationTxt" ID="locationText"></SfTextBox>
                </div>
            </div>
            <div class="row mb-2">
                <div class="col-md">
                    <SfTextBox Placeholder="Shift Code" FloatLabelType="FloatLabelType.Always"
                               @bind-Value="location.EmployeeShiftCode" ID="shiftCode"></SfTextBox>
                </div>
                <div class="col-md">
                    <SfTimePicker Placeholder="Shift Start Time" FloatLabelType="FloatLabelType.Always"
                                  @bind-Value="location.ShiftStart" ID="shiftStart"></SfTimePicker>
                </div>
                <div class="col-md">
                    <SfTimePicker Placeholder="Shift End Time" FloatLabelType="FloatLabelType.Always"
                                  @bind-Value="location.ShiftEnd" ID="shiftEnd"></SfTimePicker>
                </div>
            </div>

        </Content>
    </DialogTemplates>
    <DialogButtons>
        <DialogButton Content="Save" IsPrimary="true" @onclick="SaveLocation"></DialogButton>
        <DialogButton Content="Cancel" @onclick="CancelLocation"></DialogButton>
    </DialogButtons>
</SfDialog>

@code {
    List<LocationDto> locations = new();
    LocationDto location = new();
    SfDialog locationDialog;
    bool IsFormVisible;
    private SfToast toastObj;
    public SfGrid<LocationDto> dgMain { get; set; }

    protected override async Task OnInitializedAsync()
    {
        locations = await service.GetAllLocations();
    }

    private async Task OpenCreateForm()
    {
        location = new LocationDto();
        IsFormVisible = true;
        await locationDialog.ShowAsync();
    }

    private async Task SaveLocation()
    {
        try
        {
            await service.SaveLocation(location);
            locations = await service.GetAllLocations();
            IsFormVisible = false;
            await locationDialog.HideAsync();
        }
        catch (Exception ex)
        {
            // show syncfusion error toast
            var mm = new ToastModel { Content = ex.Message, Title = "Error", ShowProgressBar = true, Timeout = 5000, ShowCloseButton = true };
            await toastObj.ShowAsync(mm);
        }
    }

    private async Task CancelLocation()
    {
        IsFormVisible = false;
        await locationDialog.HideAsync();
    }

    private async Task EditLocation(LocationDto loc)
    {
        location = await service.GetLocationById(loc.Id);
        IsFormVisible = true;
        await locationDialog.ShowAsync();
    }

    private async Task DeleteLocation(int id)
    {
        await service.DeleteLocation(id);
        IsFormVisible = false;
        locations = await service.GetAllLocations();
    }

    private async Task OpenEditForm(MouseEventArgs obj)
    {
        var selectedRec = dgMain.SelectedRecords;
        if (selectedRec.Any())
        {
            var id = selectedRec[0].Id;
            location = await service.GetLocationById(id);
            await locationDialog.ShowAsync();
        }
    }

    private async Task ConfirmDelete(MouseEventArgs obj)
    {
        var selectedRec = dgMain.SelectedRecords;
        if (selectedRec.Any())
        {
            // conform delete
            var cc = await js.InvokeAsync<bool>("confirm", "Are you sure you want to delete this record?");
            var id = selectedRec[0].Id;
            await DeleteLocation(id);
        }
    }

}
