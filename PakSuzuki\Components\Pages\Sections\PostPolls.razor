﻿@page "/setup/PostPolls"
@using ButtonType = MudBlazor.ButtonType
@using ChangeEventArgs = Microsoft.AspNetCore.Components.ChangeEventArgs
@using FilterType = Syncfusion.Blazor.Grids.FilterType
@using Section = PakSuzuki.DTO.Section
@inject IWebHostEnvironment env
@inject PostDataService service
@inject NavigationManager nav
@inject SfDialogService DialogService
@inject AuthenticationStateProvider AuthenticationStateProvider

<SfToast @ref="_toastObj"></SfToast>

<div id="banner">
    <MudProgressCircular Color="Color.Info" Indeterminate="@_spinner"/>
</div>

<h2>Employee Opinion</h2>

<SfDialog @ref="_dlgForm" Visible="false" Width="700px" ShowCloseIcon="true" CloseOnEscape="true" IsModal="true">
    <DialogTemplates>
        <Header>Polls</Header>
        <Content>
            <div style="width:90%;margin-left: auto;margin-right:auto">
                <div class="row mb-2">
                    <div class="col-md">
                        <SfTextBox Enabled="@_isFormEditable" Placeholder="Question" FloatLabelType="FloatLabelType.Auto" @bind-Value="_postDto.postName"/>
                    </div>
                </div>

                <div class="row mb-2">
                    <div class="col-md-10">
                        <SfTextBox Enabled="@_isFormEditable" Placeholder="Answers" FloatLabelType="FloatLabelType.Auto" @bind-Value="answers"/>
                    </div>
                    <div class="col-md-1" style="padding-top:24px;padding-left: 28px;">
                        <MudButton ButtonType="ButtonType.Button" Color="Color.Success"
                                   Disabled="@(!_isFormEditable)"
                                   Variant="Variant.Filled" Size="Size.Small" OnClick="AddAnswers">
                            Add
                        </MudButton>
                    </div>
                </div>

                <div>
                    <MudText Typo="@Typo.caption">Total Answers : @(_postPollDto.Any() ? _postPollDto.Count : 0)</MudText>
                    <MudDataGrid Items="@_postPollDto" Dense="true">
                        <Columns>
                            <PropertyColumn Property="x => x.PostPollName" Title="Answer"/>
                            <TemplateColumn CellClass="d-flex justify-end" Filterable="false">
                                <CellTemplate Context="ac">
                                    <MudIconButton Disabled="!_isFormEditable" Size="@Size.Small" Icon="@Icons.Material.Filled.Delete" Color="Color.Error" OnClick="@(() => RemoveAnswers(ac.Item.PostPollName))"/>
                                </CellTemplate>
                            </TemplateColumn>
                        </Columns>
                    </MudDataGrid>
                </div>
                <div style="height:15px;"></div>
                <div class="row mb-2">
                    <div class="col-md">
                        <SfDropDownList FloatLabelType="FloatLabelType.Auto"
                                        Enabled="@_isFormEditable"
                                        TValue="int" TItem="Category" Placeholder="Category" DataSource="@_categories" @bind-Value="_postDto.postCategoryId">
                            <DropDownListFieldSettings Text="CategoryTitle" Value="CategoryId"></DropDownListFieldSettings>
                        </SfDropDownList>
                    </div>
                </div>

                <div style="height:20px;"></div>
                <div class="row mb-2">
                    <div class="col-md-4">
                        <label>Publish Date</label>
                        <Syncfusion.Blazor.Calendars.SfDatePicker Enabled="@_isFormEditable"
                                                                  Placeholder="Publish Date" @bind-Value="_postDto.postPublishDate"/>
                    </div>
                    <div class="col-md-4">
                        <label>Expiry Date</label>
                        <Syncfusion.Blazor.Calendars.SfDatePicker Placeholder="Expiry Date" @bind-Value="_postDto.postExpiryDate"/>
                    </div>
                </div>

                <div style="height:12px;"></div>
                <div class="row">
                    <div class="mb-2 col-md">
                        <MudButton ButtonType="ButtonType.Submit" Color="Color.Primary"

                                   Variant="Variant.Filled" Size="Size.Small" StartIcon="@Icons.Material.Filled.Save" OnClick="SavePost">
                            Save
                        </MudButton>
                        <MudButton ButtonType="ButtonType.Button" Color="Color.Success"
                                   Variant="Variant.Filled" Size="Size.Small" OnClick="CloseForm">
                            Cancel
                        </MudButton>
                    </div>
                </div>
            </div>

        </Content>
    </DialogTemplates>
</SfDialog>

<div class="row mb-2">
    <div class="col-md">
        <MudButton Size="Size.Small" Color="Color.Primary" StartIcon="@Icons.Material.Filled.Add" Variant="Variant.Filled" OnClick="OpenCreateForm">Add</MudButton>
        <MudButton Size="Size.Small" Color="Color.Success" StartIcon="@Icons.Material.Filled.Edit" Variant="Variant.Filled" OnClick="OpenEditForm">Edit</MudButton>
        <MudButton Size="Size.Small" Color="Color.Warning" StartIcon="@Icons.Material.Filled.Delete" Variant="Variant.Filled" OnClick="ConfirmDelete">Delete</MudButton>
        <MudButton Size="Size.Small" Color="Color.Info" StartIcon="@Icons.Material.Filled.Group" Variant="Variant.Filled" OnClick="OpenAssignRole">Assign Roles</MudButton>
    </div>
</div>

<div class="row">
    <div class="mb-2 col-md">
        @if (_allPosts.Any())
        {
            <SfGrid @ref="_dgMain" Height="calc(100vh -220px)" DataSource="_allPosts" AllowSelection="true" AllowFiltering="true" AllowSorting="true" Width="100%">
                <GridFilterSettings Type="FilterType.Excel"></GridFilterSettings>
                <GridColumns>
                    <GridColumn AutoFit="true" Visible="false" HeaderText="ID" Field="@nameof(PostDTO.postId)"></GridColumn>
                    <GridColumn Width="500" HeaderText="Questions" Field="@nameof(PostDTO.postName)"></GridColumn>
                    <GridColumn AutoFit="true" HeaderText="Publish Date" Field="@nameof(PostDTO.postPublishDate)" Format="dd-MMM-yyyy"></GridColumn>
                    <GridColumn AutoFit="true" HeaderText="Expiry Date" Field="@nameof(PostDTO.postExpiryDate)" Format="dd-MMM-yyyy"></GridColumn>
                    <GridColumn AutoFit="true" HeaderText="Status" Field="@nameof(PostDTO.postStatus)"></GridColumn>
                    <GridColumn AutoFit="true" HeaderText="Created By" Field="@nameof(PostDTO.CreatedBy)"></GridColumn>
                    <GridColumn AutoFit="true" HeaderText="Created Date" Field="@nameof(PostDTO.CreatedDate)" Format="dd-MMM-yyyy h:mm:ss tt"></GridColumn>
                    <GridColumn AutoFit="true" HeaderText="Modified By" Field="@nameof(PostDTO.ModifiedBy)"></GridColumn>
                    <GridColumn AutoFit="true" HeaderText="Modified Date" Field="@nameof(PostDTO.ModifiedDate)" Format="dd-MMM-yyyy h:mm:ss tt"></GridColumn>
                </GridColumns>
            </SfGrid>
        }
    </div>
</div>


<SfDialog @ref="_dlgFormRole" Visible="false" Width="500px" ShowCloseIcon="true" CloseOnEscape="true" IsModal="true">
    <DialogTemplates>
        <Header>Assign Roles</Header>
        <Content>
            <div class="row" style="display:flex">
                <MudText Typo="Typo.caption">Selected Post: <b>@_postDto.postName</b></MudText>
            </div>
            <div style="height:10px;"></div>
            <div class="row" style="text-align:right;">
                <SfCheckBox @bind-Checked="_isChecked" Label="Select All" @onchange="SelectAll">Select All</SfCheckBox>
            </div>
            <div style="height:10px;"></div>
            <div class="row">
                <SfGrid @ref="dgMainRole" Height="400px" DataSource="_allRole" AllowSelection="true" AllowFiltering="true" AllowSorting="true" Width="500px">
                    <GridFilterSettings Type="FilterType.Excel"></GridFilterSettings>
                    <GridColumns>
                        <GridColumn AutoFit="true" Visible="false" HeaderText="Id" Field="@nameof(RoleDTO.Id)"></GridColumn>
                        <GridColumn Width="350" HeaderText="Roles" Field="@nameof(RoleDTO.Name)"></GridColumn>
                        <GridColumn Field="@nameof(RoleDTO.isSelect)" HeaderTextAlign="TextAlign.Center" HeaderText="Selected" Width="120" ClipMode="ClipMode.EllipsisWithTooltip">
                            <Template>
                                @{
                                    if (context is RoleDTO ctx)
                                    {
                                        <div>
                                            <SfCheckBox @bind-Checked="@ctx.isSelect"></SfCheckBox>
                                        </div>
                                    }
                                }
                            </Template>
                        </GridColumn>
                    </GridColumns>
                </SfGrid>

            </div>
            <div class="page-panel">
                <div>
                    <MudButton Size="Size.Small" Color="Color.Primary" StartIcon="@Icons.Material.Filled.Save" Variant="Variant.Filled" OnClick="SaveUserRole">Save</MudButton>
                    <MudButton Size="Size.Small" Color="Color.Success" StartIcon="@Icons.Material.Filled.Cancel" Variant="Variant.Filled" OnClick="() => _dlgFormRole!.HideAsync()">Cancel</MudButton>
                </div>
            </div>
        </Content>
    </DialogTemplates>
</SfDialog>

@code {
    SfGrid<PostDTO>? _dgMain;
    private List<PostDTO> _allPosts = new();
    SfDialog? _dlgForm, _dlgFormRole;
    private PostDTO _postDto = new();
    private List<PostPollDTO> _postPollDto = new();
    private string answers;
    SfGrid<RoleDTO>? dgMainRole;
    private SfToast? _toastObj = new();
    private bool _spinner;
    private List<RoleDTO> _allRole = new();
    private List<Category> _categories = new();
    private bool _isChecked;
    private bool _isFormEditable = true;

    private async void SelectAll(ChangeEventArgs args)
    {
        try
        {
            foreach (var item in _allRole)
            {
                item.isSelect = _isChecked;
            }

            StateHasChanged();
        }
        catch (Exception ex)
        {
            var tm = new ToastModel
            {
                Content = ex.Message, Title = "Error", ShowCloseButton = true, Timeout = 5000,
                ShowProgressBar = true
            };
            await _toastObj!.ShowAsync(tm);
        }
    }

    private async Task SaveUserRole()
    {
        try
        {
            await service.savePostRolesInPostScreen(_allRole, _postDto.postId);
            var tm = new ToastModel
            {
                Content = "Roles assigned successfully.", Title = "Info",
                ShowCloseButton = true, Timeout = 5000,
                ShowProgressBar = true
            };
            await _toastObj!.ShowAsync(tm);
            await _dlgFormRole!.HideAsync();
        }
        catch (Exception ex)
        {
            var tm = new ToastModel
            {
                Content = ex.Message, Title = "Error",
                ShowProgressBar = true,
                ShowCloseButton = true, Timeout = 5000
            };
            await _toastObj!.ShowAsync(tm);
        }
    }

    private async Task OpenAssignRole()
    {
        try
        {
            if (_dgMain!.SelectedRecords is { Count: > 0 })
            {
                var pId = _dgMain.SelectedRecords[0].postId;
                _allRole = await service.GetAllRolesAsyncWithSelectedPost(pId);
                _postDto = await service.getPostWithId(pId);
                await _dlgFormRole!.ShowAsync();
            }
            else
            {
                var tm = new ToastModel
                {
                    Content = "Please select post.", Title = "Info",
                    ShowProgressBar = true,
                    ShowCloseButton = true, Timeout = 5000
                };
                await _toastObj!.ShowAsync(tm);
            }
        }
        catch (Exception ex)
        {
            var tm = new ToastModel { Content = ex.Message, ShowProgressBar = true, Title = "Error", ShowCloseButton = true, Timeout = 5000 };
            await _toastObj!.ShowAsync(tm);
        }
    }

    private async void OpenEditForm()
    {
        if (_dgMain?.SelectedRecords.Count > 0)
        {
            if (_dgMain?.SelectedRecords[0].postStatus != "Expired")
            {
                //isPublish = false;
                var pId = _dgMain.SelectedRecords[0].postId;
                _postDto = await service.getPostWithId(pId);
                _isFormEditable = _postDto.postStatus is "Draft";
                _postPollDto = await service.getpostPollAnswerWithPostId(pId);
                await _dlgForm!.ShowAsync();
            }
            else
            {
                var tm = new ToastModel
                {
                    Content = "Post is expired, you can not edit.",
                    Title = "Info",
                    ShowCloseButton = true,
                    Timeout = 5000,
                    CssClass = "e-warning",
                    ShowProgressBar = true
                };
                await _toastObj!.ShowAsync(tm);
            }
        }
    }

    private async Task CloseForm()
    {
        await _dlgForm!.HideAsync();
    }

    private async Task OpenCreateForm()
    {
        _isFormEditable = true;

        _postDto = new PostDTO { postExpiryDate = DateTime.Now.AddDays(60) };
        _postPollDto.Clear();
        await _dlgForm!.ShowAsync();
    }

    private async void ConfirmDelete()
    {
        try
        {
            if (_dgMain!.SelectedRecords is { Count: > 0 })
            {
                var conf = await DialogService.ConfirmAsync("Are you sure want to delete this record", "Confirm");
                if (conf)
                {
                    var Id = _dgMain.SelectedRecords[0].postId;
                    service.deletePost(Id);
                    _allPosts = await service.getAllPosts((int)Section.Poll);
                    StateHasChanged();
                }
            }
        }
        catch (Exception ex)
        {
            ToastModel t = new() { Title = "Error", Content = ex.Message };
            await _toastObj!.ShowAsync(t);
        }
    }

    protected override async Task OnInitializedAsync()
    {
        _allPosts = await service.getAllPosts((int)Section.Poll);
        _categories = await service.GetCategories((int)Section.Poll);
    }

    private void AddAnswers()
    {
        if (answers is not "" and not null)
        {
            PostPollDTO p = new()
            {
                PostPollName = answers
            };
            _postPollDto.Add(p);
            answers = "";
            StateHasChanged();
        }
    }

    private void RemoveAnswers(string pollName)
    {
        var p = _postPollDto.SingleOrDefault(x => x.PostPollName == pollName)!;
        _postPollDto.Remove(p);
        StateHasChanged();
    }

    private async Task SavePost()
    {
        try
        {
            _spinner = true;
            StateHasChanged();
            var msg = await service.checkPublishExpiryDate(_postDto.postPublishDate ?? DateTime.Now, _postDto.postExpiryDate ?? DateTime.Now, _postDto.postId);
            if (msg != "")
            {
                ToastModel t1 = new()
                {
                    Title = "Information",
                    Content = msg,
                    ShowProgressBar = true,
                    ShowCloseButton = true,
                    Timeout = 5000
                };
                await _toastObj!.ShowAsync(t1);
                _spinner = false;
                return;
            }

            if (string.IsNullOrEmpty(_postDto.postName) || _postDto.postName == "")
            {
                ToastModel t1 = new() { Title = "Information", Content = "Question field is required..", ShowCloseButton = true, ShowProgressBar = true, Timeout = 5000 };
                await _toastObj!.ShowAsync(t1);
                _spinner = false;
                return;
            }

            if (_postPollDto.Count <= 0)
            {
                ToastModel t1 = new() { Title = "Information", Content = "Answers field is required..", ShowCloseButton = true, ShowProgressBar = true, Timeout = 5000 };
                await _toastObj!.ShowAsync(t1);
                _spinner = false;
                return;
            }

            var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
            var user = authState.User;
            var userId = "";
            if (user.Identity is { Name: not null })
            {
                userId = user.Identity.Name;
            }

            await service.savePostForPoll(_postDto, _postPollDto, -1, userId);

            _allPosts = await service.getAllPosts((int)Section.Poll);

            _postDto = new PostDTO();
            _postPollDto.Clear();

            _spinner = false;
            StateHasChanged();

            ToastModel t = new() { Title = "Information", Content = "Record Saved successfully." };
            await _toastObj!.ShowAsync(t);

            await _dlgForm!.HideAsync();

            //nav.NavigateTo("/");
        }
        catch (Exception ex)
        {
            ToastModel t = new() { Title = "Error", Content = ex.Message, ShowProgressBar = true, ShowCloseButton = true, Timeout = 5000 };
            await _toastObj!.ShowAsync(t);
        }
    }

}

<style>
    #banner {
        position: fixed;
        left: 50%;
        margin-left: -240px;
        width: 480px;
        height: 115px;
        border-radius: 20px;
        text-align: center;
    }


    .e-grid td.e-active {
        background: #faa601 !important;
    }
</style>