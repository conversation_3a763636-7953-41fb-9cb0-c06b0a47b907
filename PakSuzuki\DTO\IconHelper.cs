using System.Reflection;

namespace PakSuzuki.DTO;

public class IconHelper
{
    public static string? GetIconByName(string name)
    {
        try
        {
            FieldInfo field = typeof(Icons.Material.Filled).GetField(name,
                BindingFlags.Public |
                BindingFlags.Static |
                BindingFlags.FlattenHierarchy);
            return (string)field.GetValue(null);
        }
        catch (Exception e)
        {
            return Icons.Material.Filled.QuestionMark;
        }
    }
}