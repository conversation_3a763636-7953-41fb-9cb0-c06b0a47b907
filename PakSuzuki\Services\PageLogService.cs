public class PageLogService(ApplicationDbContext dc)
{
    public Task<List<PageAccessLog>> GetPageLogs(DateTime dateFrom, DateTime dateTo)
    {
        var q = (from a in dc.PageAccessLogs
                 where a.AccessedDateTime >= dateFrom && a.AccessedDateTime <= dateTo
                 select a).ToList();
        return Task.FromResult(q);
    }
    // Save Page Log as per PageAccessLog Entity
    public Task SavePageLog(PageAccessLog log)
    {
        dc.PageAccessLogs.Add(log);
        dc.SaveChanges();
        return Task.CompletedTask;
    }
}