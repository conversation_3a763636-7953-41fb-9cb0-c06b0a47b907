﻿@inject AppDataService Service
@if (!string.IsNullOrEmpty(_biDashboardUrl))
{
    <style>
        .chartcard {
            width: 100%;
            height: 500px;
            padding: 10px 15px;
            display: flex;
            overflow-y: auto;
            flex-direction: column;
        }

            .chartcard img {
                margin: auto;
                height: 450px;
            }
    </style>
    <div class="card chartcard" style="max-height:unset !important">
        <div class="row">
            <div class="col" style="text-align:right">
                <a class="view-all" href="http://ps-powerbi:808/Reports/browse" target="biportal">Business Intelligence Dashboards</a>
            </div>
        </div>
        <div class="row">
            <div class="col" style="display:flex;height:500px">
                @*<img src="/media/charts/1.png" />*@
                <iframe title="Portal Dashboard" style="width:100%;height:490px;display:inline-block;outline:none" tabindex="-1" frameborder="0" src='@_biDashboardUrl' ></iframe>
            </div>
        </div>
    </div>
}

@code {
    private string _biDashboardUrl = "";
    protected override async Task OnInitializedAsync()
    {
        _biDashboardUrl = await Service.GetBIDashboardURL();
    }
}
