﻿@using PakSuzuki.DTO
@inject AppDataService service
@rendermode InteractiveServer
<link href="/styles/card.css" rel="stylesheet" />




<SfDialog @ref="picDialog" ShowCloseIcon="true" IsModal="true" Width="900px" CloseOnEscape="true" Visible="IsPicDialogVisible">
    <DialogTemplates>
        <Header>@Title</Header>
        <Content>
            <div class="popup">

                <div class="row mb-2">
                    <div class="col">
                        <img src="@postDetail!.postHeadImagePath" width="100%" style="max-height: calc(100vh - 200px); object-fit:cover" alt=""/>
                    </div>
                </div>
                <div class="row mb-2">
                    <div class="col-md">
                        <h2 class="card-title" style="font-size: 18px; font-weight:unset; font-size:1.5rem;">@postDetail.postName</h2>
                        <p>@postDetail.postPublishDateStr</p>
                    </div>
                </div>
                <div class="row mb-2">
                    <div class="col">
                        @((MarkupString)postDetail.postContentHTML)
                    </div>
                </div>

            </div>
        </Content>
    </DialogTemplates>
</SfDialog>
<SfDialog @ref="detailForm" ShowCloseIcon="true" IsModal="true" Width="900px" CloseOnEscape="true" Visible="IsDetailVisible">
    <DialogTemplates>
        <Header>@Title</Header>
        <Content>
            <div class="popup">

                <div class="row">
                    <div class="col">
                        <img src="@postDetail!.postHeadImagePath" width="100%" height="340px" style="object-fit: cover;object-position:top" alt=""/>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md">
                        <h2 class="card-title" style="font-size: 18px; font-weight:unset; font-size:1.5rem;">@postDetail.postName</h2>
                        <p>@postDetail.postPublishDateStr</p>
                        @((MarkupString)postDetail.postContentHTML)
                    </div>
                </div>
                <div class="row">
                    <div class="col-md">
                        <h4 style="font-size:17px">Tags</h4>
                        <p></p>
                        @foreach (var tag in postDetail.Tags)
                        {
                            <span class="tag">@tag</span>
                        }
                    </div>
                </div>
                @if (postDetail.Attachments != null && postDetail.Attachments.Any())
                {
                    int idx = 0;

                    <div class="row">
                        <div class="col-md">
                            <h4 style="font-size:17px">Attachments</h4>
                            <p></p>

                            @foreach (var tag in postDetail.Attachments)
                            {
                                var trg = $"tg{postDetail.postId}_{idx}";
                                <a class="tag" target="@trg" href="@tag.Url">@tag.Name</a>
                            }
                        </div>
                    </div>
                }
            </div>
        </Content>
    </DialogTemplates>

</SfDialog>
@if (Items.Any())
{
    @if (!string.IsNullOrEmpty(Title))
    {
        <div class="view-all-container">
            <h3 class="sec-title">@Title</h3>
            @if (!string.IsNullOrEmpty(DetailPageUrl))
            {
                <a class="view-all" href="@DetailPageUrl">View All</a>
            }
        </div>

    }


    <div class="row mb-1">
        <div class="col">

            @if (CatalogType == CompCatalogType.Card)
            {
                <BitCarousel Style="height:500px" VisibleItemsCount="3" ScrollItemsCount="3">
                    @foreach (var itm in Items)
                    {
                        if (!string.IsNullOrEmpty(itm.PostExternalLink))
                        {
                            <BitCarouselItem Class="item">
                                <div class="card-outer">
                                    <a class="card" href="@itm.PostExternalLink" target="_blank">
                                        <img src="@itm.postThumbnailPath" class="card-image" alt="">
                                        <div class="card-content">

                                            <h2 class="card-title">@itm.postName</h2>
                                            <p class="card-description">
                                                @if (itm.postContentText!.Length > 250)
                                                {
                                                    @itm.postContentText.Substring(0, 250)
                                                    <span>...&nbsp;</span> <span @onclick="() => ShowDetail(itm.postId)" class="m-link">more</span>
                                                }
                                                else
                                                {
                                                    @itm.postContentText
                                                }

                                            </p>
                                            <p class="card-date">Posted on <span>@itm.postPublishDateStr</span></p>
                                        </div>
                                    </a>
                                </div>
                            </BitCarouselItem>
                        }
                        else
                        {
                    <BitCarouselItem Class="item">
                        <div class="card-outer">
                            <div class="card" @onclick="() => ShowDetail(itm.postId)">
                                <img src="@itm.postThumbnailPath" class="card-image" alt="">
                                <div class="card-content">

                                    <h2 class="card-title">@itm.postName</h2>
                                    <p class="card-description">
                                        @if (itm.postContentText!.Length > 250)
                                        {
                                        @itm.postContentText.Substring(0, 250)
                                        <span>...&nbsp;</span> <span @onclick="() => ShowDetail(itm.postId)" class="m-link">more</span>
                                        }
                                        else
                                        {
                                        @itm.postContentText
                                        }

                                    </p>
                                    <p class="card-date">Posted on <span>@itm.postPublishDateStr</span></p>
                                </div>
                            </div>
                        </div>
                    </BitCarouselItem>
                        }
                        
                    }

                </BitCarousel>
            }
            else if (CatalogType == CompCatalogType.Image)
            {
                <BitCarousel Style="height: 340px" VisibleItemsCount="3" ScrollItemsCount="3">
                    @foreach (var itm in Items)
                    {
                        <BitCarouselItem Class="item">
                            <div class="card" style="height:unset;margin:4px;">
                                <img @onclick="() => ShowPic(itm.postId)" src="@itm.postThumbnailPath" class="card-image img-only" style="border-radius:15px;" alt="">
                            </div>
                        </BitCarouselItem>
                    }

                </BitCarousel>


            }
            else if (CatalogType == CompCatalogType.Idea)
            {
                <BitCarousel Style="height: 500px" VisibleItemsCount="3" ScrollItemsCount="3">
                    @foreach (var itm in Items)
                    {
                        <BitCarouselItem Class="item">
                            <div class="card-outer">
                                <div class="card" @onclick="() => ShowDetail(itm.postId)">
                                    <div class="header" style="margin-bottom:unset;">
                                        <img src="/uploadUserPics/main.png" style="" alt="main">
                                        <div class="user">

                                            <p></p>
                                            <span>
                                                <span>@itm.postPublishDateStr</span>
                                            </span>
                                        </div>
                                    </div>
                                    <img src="@itm.postThumbnailPath" class="card-image" alt="" style="border-radius:unset;">
                                    <div class="card-content">

                                        <h2 class="card-title">@itm.postName</h2>

                                        <p class="card-date">1.6k likes - 25 comments </p>
                                    </div>
                                </div>
                            </div>
                        </BitCarouselItem>
                    }

                </BitCarousel>
            }
        </div>

    </div>
}



@code {

    [Parameter]
    public string? Title { get; set; }

    [Parameter]
    public CompCatalogType? CatalogType { get; set; }

    [Parameter]
    public List<PostDTO> Items { get; set; } = new();

    [Parameter]
    public string? DetailPageUrl { get; set; }


    private SfDialog detailForm;
    private SfDialog picDialog;
    private bool IsDetailVisible = false;
    private bool IsPicDialogVisible = false;
    private PostDTO? postDetail;

    private async Task ShowDetail(int postId)
    {
        postDetail = await service.GetPostDetail(postId);
        {
            if (postDetail != null)
            {
                await detailForm.ShowAsync();
            }
        }
    }

    public async Task ShowPic(int postId)
    {
        postDetail = await service.GetPostDetail(postId);
        {
            if (postDetail != null)
            {
                await picDialog.ShowAsync();
            }
        }
    }

}