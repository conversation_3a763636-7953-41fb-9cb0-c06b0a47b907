﻿@page "/setup/DocumentationManagement"
@using Microsoft.AspNetCore.Components.Forms
@using MudBlazor
@using PakSuzuki.Model
@using Syncfusion.Blazor.RichTextEditor
@using Syncfusion.Blazor.Buttons
@using Syncfusion.Blazor.Grids
@using Syncfusion.Blazor.Lists
@using Syncfusion.Blazor.Notifications
@using Syncfusion.Blazor.Popups
@inject IWebHostEnvironment env
@inject PostDataService service
@inject NavigationManager nav
@inject SfDialogService DialogService
@inject AuthenticationStateProvider AuthenticationStateProvider

<SfToast @ref="toastObj"></SfToast>

<div id="banner">
    <MudProgressCircular Color="Color.Info" Indeterminate="@spinner" />
</div>

<h2>Documentation Management</h2>
<SfDialog @ref="dlgForm" Visible="false" Width="800px" ShowCloseIcon="true" CloseOnEscape="true" IsModal="true">
    <DialogTemplates>
        <Header>Documentation Management</Header>
        <Content>
            <div style="width:;margin-left: auto;margin-right:auto">

                <div style="height:12px;"></div>

                <div class="row mb-2">
                    <div class="col-md">
                        <MudImage style="width:100%;height:130px;object-fit:cover" Src="@profilePicThumb" Alt="Thumbnail" Elevation="25" Class="rounded-lg" />
                        <MudFileUpload T="IBrowserFile" Accept=".png, .jpg, .jpeg" FilesChanged="UploadThumbnail" MaximumFileCount="1" Disabled="!IsFormEditable">
                            <ButtonTemplate Context="ctx">
                                <MudButton HtmlTag="label"
                                           Style="width:100%"
                                           Variant="Variant.Filled"
                                           Color="Color.Primary"
                                           Size="Size.Small"
                                           StartIcon="@Icons.Material.Filled.CloudUpload"
                                           for="@ctx.Id">
                                    <div style="display:flex; flex-direction:column;">
                                        <div>Thumbnail</div>
                                        <div style="font-size:10px"> (310 px x 500px)</div>

                                    </div>
                                </MudButton>
                            </ButtonTemplate>
                        </MudFileUpload>
                    </div>
                    <div class="col-md">
                        <MudImage Style="width:100%;height:130px;object-fit:cover" Src="@profilePicHdr" Alt="Header Image" Elevation="25" Class="rounded-lg" />
                        <MudFileUpload T="IBrowserFile" Accept=".png, .jpg, .jpeg" FilesChanged="UploadHeaderImage" MaximumFileCount="1" Disabled="!IsFormEditable" >
                            <ButtonTemplate Context="ctx">
                                <MudButton HtmlTag="label"
                                           Style="width:100%"
                                           Size="Size.Small"
                                           Variant="Variant.Filled"
                                           Color="Color.Primary"
                                           StartIcon="@Icons.Material.Filled.CloudUpload"
                                           for="@ctx.Id">
                                    <div style="display:flex; flex-direction:column;">
                                        <div>Header Picture</div>
                                        <div style="font-size:10px"> (1080 px x 420px)</div>

                                    </div>
                                </MudButton>
                            </ButtonTemplate>
                        </MudFileUpload>
                    </div>
                </div>
                <div cass="row mb-2">
                    <div class="col-md">
                        <SfTextBox Placeholder="Title" 
                                   FloatLabelType="FloatLabelType.Always" 
                                   @bind-Value="postObj.postName" style="width:100%" Enabled="IsFormEditable" />
                    </div>
                </div>
                <div class="row mb-2">
                    <div class="col-md">
                        <SfDropDownList FloatLabelType="FloatLabelType.Always"
                                        Enabled="IsFormEditable"
                                        AllowFiltering="true" FilterType="Syncfusion.Blazor.DropDowns.FilterType.Contains"
                                        TValue="int" TItem="Category" Placeholder="Category" DataSource="@categories" @bind-Value="postObj.postCategoryId">
                            <DropDownListFieldSettings Text="CategoryTitle" Value="CategoryId"></DropDownListFieldSettings>
                            <DropDownListEvents TValue="int" TItem="Category" ValueChange="OnCategorySelect"></DropDownListEvents>
                        </SfDropDownList>
                    </div>
                    <div class="col-md">
                        <SfDropDownList FloatLabelType="FloatLabelType.Always"
                                        Enabled="IsFormEditable"
                                        AllowFiltering="true" FilterType="Syncfusion.Blazor.DropDowns.FilterType.Contains"
                                        ShowClearButton="true"
                                        TValue="int?" TItem="SubCategoryDto" Placeholder="Sub Category" DataSource="@_subCategories" @bind-Value="postObj.postSubCategoryId">
                            <DropDownListFieldSettings Text="Title" Value="Id"></DropDownListFieldSettings>
                        </SfDropDownList>
                    </div>
                </div>
                <div class="row mb-2">
                    <div class="col-md">
                        <label>Content</label>
                        <SfRichTextEditor @ref="rTxt" Enabled="true" Readonly="false" Width="100%" Height="300px" @bind-Value="postObj.postContentText" EnableXhtml="true">
                            <RichTextEditorToolbarSettings Items="@Tools" />
                        </SfRichTextEditor>
                    </div>
                </div>
                <div class="row mb-2">
                    <div class="col-md">
                        <SfTextBox Placeholder="Remarks" FloatLabelType="FloatLabelType.Always" @bind-Value="postObj.postRemarks" Multiline="true" Enabled="IsFormEditable" />

                    </div>
                </div>
                <div class="row mb-2">
                    <div class="col-md" style="display: flex;gap:10px;align-items: end;">
                        <SfTextBox Placeholder="Search Tags" FloatLabelType="FloatLabelType.Always" @bind-Value="srhTag" Enabled="IsFormEditable"/>
                        <MudButton ButtonType="MudBlazor.ButtonType.Button" Color="Color.Success" Disabled="!IsFormEditable"
                                   Variant="Variant.Filled" Size="Size.Small" OnClick="addSrhTag">Add</MudButton>
                    </div>
                </div>
                <div class="row mb-2">
                    <div class="col-md">

                        <MudText Typo="@Typo.caption">Total Search Tags : @(searchTags.Any() ? @searchTags.Count : 0)</MudText>

                        <MudChipSet T="string" AllClosable OnClose="Closed">
                            @foreach (var item in searchTags)
                            {
                                <MudChip Variant="Variant.Outlined" Color="Color.Secondary" Text="@item.postSearchTagName"></MudChip>
                            }
                        </MudChipSet>
                    </div>
                </div>
                <div class="row mb-2">
                    <div class="col-md">

                        <MudFileUpload Style="margin-top:20px;" T="IReadOnlyList<IBrowserFile>" 
                                       Disabled="!IsFormEditable"
                                       Accept=".png, .jpg, .jpeg, .pdf, .docx, .txt, .doc," FilesChanged="UploadAttachments">
                            <ButtonTemplate>
                                <MudButton HtmlTag="label" Style="width:100%"
                                           Variant="Variant.Filled"
                                           Color="Color.Primary"
                                           StartIcon="@Icons.Material.Filled.CloudUpload"
                                           Size="Size.Small"
                                           for="@context.Id">
                                    Attach
                                </MudButton>
                            </ButtonTemplate>
                        </MudFileUpload>
                    </div>
                    <div class="col-md">
                        <label>Publish Date</label>
                        <Syncfusion.Blazor.Calendars.SfDatePicker Enabled="IsFormEditable"
                                                                  Placeholder="Publish Date" @bind-Value="postObj.postPublishDate" />
                    </div>
                    <div class="col-md">
                        <label>Expiry Date</label>
                        <Syncfusion.Blazor.Calendars.SfDatePicker Placeholder="Expiry Date" @bind-Value="postObj.postExpiryDate" />
                    </div>
                </div>
                <div class="row mb-2">
                    <div class="col-md">
                        <MudChipSet T="string" AllClosable OnClose="ClosedAttachment">
                            @foreach (var attachFile in attachmentDTO!)
                            {
                                <MudChip Variant="Variant.Outlined" Color="Color.Success" Text="@attachFile.postAttachmentName"></MudChip>
                            }
                        </MudChipSet>
                    </div>
                </div>
                <div class="row">
                    <dic class="col-md">
                        <MudButton ButtonType="MudBlazor.ButtonType.Submit" Color="Color.Primary"
                                   Variant="Variant.Filled" Size="Size.Small" StartIcon="@Icons.Material.Filled.Save" OnClick="savePost">Save</MudButton>
                        <MudButton ButtonType="MudBlazor.ButtonType.Button" Color="Color.Success"
                                   Variant="Variant.Filled" Size="Size.Small" OnClick="(() => dlgForm!.HideAsync() )">Cancel</MudButton>
                    </dic>
                </div>

            </div>
        </Content>

    </DialogTemplates>
    <DialogEvents Opened="@DialogOpen"></DialogEvents>
</SfDialog>


<div class="row mb-2">
    <div class="col-md">
        <MudButton Size="Size.Small" Color="Color.Primary" StartIcon="@Icons.Material.Filled.Add" Variant="Variant.Filled" OnClick="OpenCreateForm">Add</MudButton>
        <MudButton Size="Size.Small" Color="Color.Success" StartIcon="@Icons.Material.Filled.Edit" Variant="Variant.Filled" OnClick="OpenEditForm">Edit</MudButton>
        <MudButton Size="Size.Small" Color="Color.Warning" StartIcon="@Icons.Material.Filled.Delete" Variant="Variant.Filled" OnClick="ConfirmDelete">Delete</MudButton>
        <MudButton Size="Size.Small" Color="Color.Info" StartIcon="@Icons.Material.Filled.Group" Variant="Variant.Filled" OnClick="openAssignRole">Assign Roles</MudButton>
    </div>
</div>

<div class="row">
    <div class="mb-2 col-md">
        @if (allPosts.Any())
        {
            <SfGrid @ref="dgMain" DataSource="allPosts" AllowSelection="true" AllowFiltering="true" AllowSorting="true" Width="100%">
                <GridFilterSettings Type="Syncfusion.Blazor.Grids.FilterType.Excel"></GridFilterSettings>
                <GridColumns>
                    <GridColumn AutoFit="true" Visible="false" HeaderText="ID" Field="@nameof(PostDTO.postId)"></GridColumn>
                    <GridColumn Width="450" HeaderText="Title" Field="@nameof(PostDTO.postName)"></GridColumn>
                    <GridColumn AutoFit="true" HeaderText="Category" Field="@nameof(PostDTO.PostCategory)" Format="dd-MMM-yyyy"></GridColumn>
                    <GridColumn AutoFit="true" HeaderText="Sub Category" Field="@nameof(PostDTO.PostSubCategory)" Format="dd-MMM-yyyy"></GridColumn>
                    <GridColumn AutoFit="true" HeaderText="Publish Date" Field="@nameof(PostDTO.postPublishDate)" Format="dd-MMM-yyyy"></GridColumn>
                    <GridColumn AutoFit="true" HeaderText="Expiry Date" Field="@nameof(PostDTO.postExpiryDate)" Format="dd-MMM-yyyy"></GridColumn>
                    <GridColumn AutoFit="true" HeaderText="Status" Field="@nameof(PostDTO.postStatus)"></GridColumn>
                    <GridColumn AutoFit="true" HeaderText="Created By" Field="@nameof(PostDTO.CreatedBy)"></GridColumn>
                    <GridColumn AutoFit="true" HeaderText="Created Date" Field="@nameof(PostDTO.CreatedDate)" Format="dd-MMM-yyyy hh:mm:ss"></GridColumn>
                    <GridColumn AutoFit="true" HeaderText="Modified By" Field="@nameof(PostDTO.ModifiedBy)"></GridColumn>
                    <GridColumn AutoFit="true" HeaderText="Modified Date" Field="@nameof(PostDTO.ModifiedDate)" Format="dd-MMM-yyyy h:mm:ss tt"></GridColumn>
                </GridColumns>
            </SfGrid>
        }
    </div>
</div>

<SfDialog @ref="dlgFormRole" Visible="false" Width="500px" ShowCloseIcon="true" CloseOnEscape="true" IsModal="true">
    <DialogTemplates>
        <Header>Assign Roles</Header>
        <Content>
            <div class="row" style="display:flex">
                <MudText Typo="Typo.caption">Selected Post: <b>@postObj.postName</b></MudText>
            </div>
            <div style="height:10px;"></div>
            <div class="row" style="text-align:right;">
                <SfCheckBox @bind-Checked="isChecked" Label="Select All" @onchange="selectAll">Select All</SfCheckBox>
            </div>
            <div style="height:10px;"></div>

            <div class="row">
                <SfGrid @ref="dgMainRole" Height="400px" DataSource="allRole" AllowSelection="true" AllowFiltering="true" AllowSorting="true" Width="500px">
                    <GridFilterSettings Type="Syncfusion.Blazor.Grids.FilterType.Excel"></GridFilterSettings>
                    <GridColumns>
                        <GridColumn AutoFit="true" Visible="false" HeaderText="Id" Field="@nameof(RoleDTO.Id)"></GridColumn>
                        <GridColumn Width="350" HeaderText="Roles" Field="@nameof(RoleDTO.Name)"></GridColumn>
                        <GridColumn Field=@nameof(RoleDTO.isSelect) HeaderTextAlign="TextAlign.Center" HeaderText="Selected" Width="120" ClipMode="ClipMode.EllipsisWithTooltip">
                            <Template>
                                @{
                                    var ctx = (context as RoleDTO);
                                    <div>
                                        <center><SfCheckBox @bind-Checked="@ctx.isSelect"></SfCheckBox></center>
                                    </div>
                                }
                            </Template>
                        </GridColumn>
                    </GridColumns>
                </SfGrid>

            </div>
            <div class="page-panel">
                <div>
                    <MudButton Size="Size.Small" Color="Color.Primary" StartIcon="@Icons.Material.Filled.Save" Variant="Variant.Filled" OnClick="SaveUserRole">Save</MudButton>
                    <MudButton Size="Size.Small" Color="Color.Success" StartIcon="@Icons.Material.Filled.Cancel" Variant="Variant.Filled" OnClick="(() => dlgFormRole!.HideAsync())">Cancel</MudButton>
                </div>
            </div>
        </Content>
    </DialogTemplates>
</SfDialog>

@code {
    SfDialog? dlgForm, dlgFormRole;
    IList<IBrowserFile> files = new List<IBrowserFile>();
    DateTime? date = DateTime.Today;
    private List<RoleDTO> allRole = new();
    private RoleDTO Role = new();
    private List<Category> categories = new();
    SfGrid<RoleDTO>? dgMainRole;
    private List<PostDTO> allPosts = new();
    private SfGrid<PostDTO> dgMain;
    private PostDTO postObj = new();
    private List<PostSearchTagDTO> searchTags = new();
    private string srhTag;
    SfRichTextEditor rTxt;
    private string profilePicThumb = "images/upload.jpg";
    private string profilePicHdr = "images/upload.jpg";
    private IBrowserFile? imgfileThumb = null;
    private IBrowserFile? imgfileHdr = null;
    private List<IBrowserFile>? attachments = new();
    private List<PostAttachmentDTO> attachmentDTO = new();
    private SfToast? toastObj = new();
    private bool spinner = false;
    private bool isPublish = true;

    bool isChecked = false;
    bool IsFormEditable = true;
    private async void selectAll(Microsoft.AspNetCore.Components.ChangeEventArgs args)
    {
        try
        {
            foreach (var item in allRole)
            {
                item.isSelect = isChecked;
            }
            this.StateHasChanged();
        }
        catch (Exception ex)
        {
            ToastModel tm = new ToastModel() { Content = ex.Message.ToString(), Title = "Error", ShowCloseButton = true, Timeout = 0 };
            await toastObj!.ShowAsync(tm);
        }
    }
    private async Task SaveUserRole()
    {
        try
        {
            await service.savePostRolesInPostScreen(allRole, postObj.postId);
            ToastModel tm = new ToastModel() { Content = "Roles assigned successfully.", Title = "Info", ShowCloseButton = true, Timeout = 5000 };
            await toastObj!.ShowAsync(tm);
            await dlgFormRole!.HideAsync();
        }
        catch (Exception ex)
        {
            ToastModel tm = new ToastModel() { Content = ex.Message.ToString(), Title = "Error", ShowCloseButton = true, Timeout = 5000 };
            await toastObj!.ShowAsync(tm);
        }
    }

    private async Task openAssignRole()
    {
        try
        {
            if (dgMain!.SelectedRecords is { Count: > 0 })
            {
                int pId = dgMain.SelectedRecords[0].postId;
                allRole = await service.GetAllRolesAsyncWithSelectedPost(pId);
                postObj = await service.getPostWithId(pId);
                _subCategories = await service.GetSubCategories(postObj.postCategoryId);
                await dlgFormRole!.ShowAsync();
            }
            else
            {
                ToastModel tm = new ToastModel() { Content = "Please select post.", Title = "Info", ShowCloseButton = true, Timeout = 5000 };
                await toastObj!.ShowAsync(tm);
            }
        }
        catch (Exception ex)
        {
            ToastModel tm = new ToastModel() { Content = ex.Message.ToString(), Title = "Error", ShowCloseButton = true, Timeout = 5000 };
            await toastObj!.ShowAsync(tm);
        }
    }

    private void DialogOpen()
    {
        this.rTxt.RefreshUI();
    }

    private async void OpenEditForm()
    {
        if (dgMain?.SelectedRecords.Count > 0)
        {
            if (dgMain?.SelectedRecords[0].postStatus != "Expired")
            {

                //isPublish = false;
                var pId = dgMain.SelectedRecords[0].postId;
                postObj = await service.getPostWithId(pId);
                _subCategories = await service.GetSubCategories(postObj.postCategoryId);
                isPublish = postObj.postPublishDate != null && !(postObj.postPublishDate >= DateTime.Today);
                IsFormEditable = postObj.postStatus is "Draft" or "Published";
                profilePicThumb = string.IsNullOrEmpty(postObj.postThumbnailPath) ? "images/upload.jpg" : AppDataService.ConvertToUrl(postObj.postThumbnailPath);
                profilePicHdr = string.IsNullOrEmpty(postObj.postHeadImagePath) ? "images/upload.jpg" : AppDataService.ConvertToUrl(postObj.postHeadImagePath);
                searchTags = await service.getPostSrhTagsWithPostId(pId);
                attachmentDTO = await service.getPostAttachmentWithPostId(pId);
                await dlgForm!.ShowAsync();
            }
            else
            {
                ToastModel tm = new ToastModel()
                {
                    Content = "Post is expired, you can not edit.",
                    Title = "Info",
                    ShowCloseButton = true,
                    Timeout = 5000,
                    CssClass = "e-warning",
                    ShowProgressBar = true
                };
                await toastObj!.ShowAsync(tm);
            }
        }
    }

    public void Closed(MudChip chip)
    {
        if (!IsFormEditable) return;
        PostSearchTagDTO p = searchTags.SingleOrDefault(x => x.postSearchTagName == chip.Text)!;
        searchTags.Remove(p);
        this.StateHasChanged();
    }

    public void ClosedAttachment(MudChip chip)
    {
        if (!IsFormEditable) return;
        var a = attachmentDTO!.SingleOrDefault(x => x.postAttachmentName == chip.Text);
        attachmentDTO.Remove(a);
        this.StateHasChanged();
    }

    private void OpenCreateForm()
    {
        IsFormEditable = true;
        isPublish = true;

        postObj = new PostDTO() { postExpiryDate = DateTime.Today.AddDays(60) };
        searchTags = new();
        profilePicThumb = "images/upload.jpg";
        profilePicHdr = "images/upload.jpg";
        imgfileThumb = null;
        imgfileHdr = null;
        attachments = new();
        attachmentDTO = new();
        files = new List<IBrowserFile>();
        dlgForm!.ShowAsync();
    }

    private async void ConfirmDelete()
    {
        try
        {
            if (dgMain!.SelectedRecords is { Count: > 0 })
            {
                var conf = await DialogService.ConfirmAsync("Are you sure want to delete this record", "Confirm");
                if (conf)
                {
                    var Id = dgMain.SelectedRecords[0].postId;
                    service.deletePost(Id);
                    allPosts = await service.getAllPosts((int)DTO.Section.DocumentationManagement);
                    this.StateHasChanged();
                }

            }
        }
        catch (Exception ex)
        {
            ToastModel t = new() { Title = "Error", Content = ex.Message.ToString() };
            await toastObj.ShowAsync(t);
        }
    }

    protected override async Task OnInitializedAsync()
    {
        try
        {
            allPosts = await service.getAllPosts((int)DTO.Section.DocumentationManagement);
            categories = await service.GetCategories((int)DTO.Section.DocumentationManagement);
        }
        catch
        {
            throw;
        }
    }

    private async void UploadAttachments(IReadOnlyList<IBrowserFile> files)
    {
        foreach (var file in files)
        {
            attachments!.Add(file);
            string postTitle = System.Guid.NewGuid().ToString();
            string dirPath = $"{env.WebRootPath}\\Posts\\{postTitle}\\Attachments";
            if (System.IO.Directory.Exists(dirPath) == false) { System.IO.Directory.CreateDirectory(dirPath); }
            var path = dirPath + "\\" + file.Name;
            PostAttachmentDTO p = new();
            p.postAttachmentDocURL = path;
            p.postAttachmentName = file.Name;
            p.postAttachmentDocSize = file.Size.ToString();
            p.postAttachmentDocType = file.ContentType.ToString();
            p.postAttachmentDocExt = System.IO.Path.GetExtension(file.Name);
            attachmentDTO.Add(p);

            Stream stream = file.OpenReadStream(30 * 1024 * 1024);
            FileStream fs = File.Create(path);
            await stream.CopyToAsync(fs);
            stream.Close();
            fs.Close();

        }
        this.StateHasChanged();
    }

    private async void UploadThumbnail(IBrowserFile file)
    {
        imgfileThumb = file;
        MemoryStream memoryStream = new MemoryStream();
        using var fileStream = file.OpenReadStream(long.MaxValue);
        await fileStream.CopyToAsync(memoryStream);
        byte[] bytes = memoryStream.ToArray();
        profilePicThumb = "data:image/png;base64," + Convert.ToBase64String(bytes);
        this.StateHasChanged();
    }
    private async void UploadHeaderImage(IBrowserFile file)
    {
        imgfileHdr = file;
        MemoryStream memoryStream = new MemoryStream();
        using var fileStream = file.OpenReadStream(long.MaxValue);
        await fileStream.CopyToAsync(memoryStream);
        byte[] bytes = memoryStream.ToArray();
        profilePicHdr = "data:image/png;base64," + Convert.ToBase64String(bytes);
        this.StateHasChanged();
    }
    private List<ToolbarItemModel> Tools = new List<ToolbarItemModel>()
{
        new ToolbarItemModel() { Command = ToolbarCommand.Bold },
        new ToolbarItemModel() { Command = ToolbarCommand.Italic },
        new ToolbarItemModel() { Command = ToolbarCommand.Underline },
        new ToolbarItemModel() { Command = ToolbarCommand.Separator },
        new ToolbarItemModel() { Command = ToolbarCommand.Formats },
        new ToolbarItemModel() { Command = ToolbarCommand.Alignments },
        new ToolbarItemModel() { Command = ToolbarCommand.OrderedList },
        new ToolbarItemModel() { Command = ToolbarCommand.UnorderedList },
        new ToolbarItemModel() { Command = ToolbarCommand.Separator },
        // new ToolbarItemModel() { Command = ToolbarCommand.CreateLink },
        // new ToolbarItemModel() { Command = ToolbarCommand.Image },
        // new ToolbarItemModel() { Command = ToolbarCommand.Separator },
        // new ToolbarItemModel() { Command = ToolbarCommand.SourceCode },
        // new ToolbarItemModel() { Command = ToolbarCommand.Separator },
        new ToolbarItemModel() { Command = ToolbarCommand.Undo },
        new ToolbarItemModel() { Command = ToolbarCommand.Redo }
    };

    private void addSrhTag()
    {
        if (srhTag != "" && srhTag != null)
        {
            PostSearchTagDTO p = new();
            p.postSearchTagName = srhTag;
            p.postSearchTagID = -1;
            searchTags.Add(p);
            srhTag = "";
            this.StateHasChanged();
        }
    }

    private void removeSrhTag(string tagName)
    {
        PostSearchTagDTO p = searchTags.SingleOrDefault(x => x.postSearchTagName == tagName)!;
        searchTags.Remove(p);
        this.StateHasChanged();
    }
    private void removeAttachFile(string fileName)
    {
        var a = attachmentDTO!.SingleOrDefault(x => x.postAttachmentName == fileName);
        attachmentDTO.Remove(a);
        this.StateHasChanged();
    }

    private async Task<string> saveImageThumb(string postTitle, string fileName)
    {
        if (imgfileThumb != null && profilePicThumb != "images/upload.jpg")
        {
            postTitle = System.Guid.NewGuid().ToString();
            fileName = postTitle;
            string dirPath = $"{env.WebRootPath}\\Posts\\{postTitle}";
            if (System.IO.Directory.Exists(dirPath) == false) { System.IO.Directory.CreateDirectory(dirPath); }
            Stream stream = imgfileThumb.OpenReadStream(long.MaxValue);
            var path = dirPath + "\\" + fileName + "-thumb.png";
            FileStream fs = File.Create(path);
            await stream.CopyToAsync(fs);
            stream.Close();
            fs.Close();
            imgfileThumb = null;
            return await Task.FromResult(path);
        }
        else
        {
            return await Task.FromResult(profilePicThumb);
        }

        return await Task.FromResult("");
    }
    private async Task<string> saveImageHDR(string postTitle, string fileName)
    {
        if (imgfileHdr != null && profilePicHdr != "images/upload.jpg")
        {
            postTitle = System.Guid.NewGuid().ToString();
            fileName = postTitle;
            string dirPath = $"{env.WebRootPath}\\Posts\\{postTitle}";
            if (System.IO.Directory.Exists(dirPath) == false) { System.IO.Directory.CreateDirectory(dirPath); }
            Stream stream = imgfileHdr.OpenReadStream(long.MaxValue);
            var path = dirPath + "\\" + fileName + "-hdr.png";

            FileStream fs = File.Create(path);
            await stream.CopyToAsync(fs);
            stream.Close();
            fs.Close();
            imgfileHdr = null;
            return await Task.FromResult(path);
        }
        else
        {
            return await Task.FromResult(profilePicHdr);
        }
        return await Task.FromResult("");
    }

    private async Task<string> saveAttachments(string postTitle)
    {
        postTitle = System.Guid.NewGuid().ToString();
        //fileName = postTitle;
        string dirPath = $"{env.WebRootPath}\\Posts\\{postTitle}\\Attachments";
        if (System.IO.Directory.Exists(dirPath) == false) { System.IO.Directory.CreateDirectory(dirPath); }
        if (attachments!.Any())
        {
            foreach (var attachfile in attachments!)
            {
                var path = dirPath + "\\" + attachfile.Name;
                PostAttachmentDTO p = new();
                p.postAttachmentDocURL = path;
                p.postAttachmentName = attachfile.Name;
                p.postAttachmentDocSize = attachfile.Size.ToString();
                p.postAttachmentDocType = attachfile.ContentType.ToString();
                p.postAttachmentDocExt = System.IO.Path.GetExtension(attachfile.Name);
                attachmentDTO.Add(p);

                Stream stream = attachfile.OpenReadStream(30 * 1024 * 1024);
                FileStream fs = File.Create(path);
                await stream.CopyToAsync(fs);
                stream.Close();
                fs.Close();
                // await using FileStream fs = new(path, FileMode.Create);
                // await attachfile.OpenReadStream(long.MaxValue).CopyToAsync(fs);

            }
            return await Task.FromResult("");
        }
        return await Task.FromResult("");
    }

    private async Task savePost()
    {
        try
        {
            spinner = true;
            this.StateHasChanged();
            string msg = await service.checkPublishExpiryDate(postObj.postPublishDate ?? DateTime.Now, postObj.postExpiryDate ?? DateTime.Now, postObj.postId);
            if (msg != "")
            {
                ToastModel t1 = new()
                {
                    Title = "Information",
                    Content = msg
                    ,
                    ShowProgressBar = true,
                    ShowCloseButton = true,
                    Timeout = 5000
                };
                await toastObj!.ShowAsync(t1);
                spinner = false;
                return;
            }
            if (String.IsNullOrEmpty(postObj.postName) || postObj.postName == "" || postObj.postContentText == "")
            {
                ToastModel t1 = new()
                {
                    Title = "Information",
                    Content = "Title, Description fields is required.."
                 ,
                    ShowProgressBar = true,
                    ShowCloseButton = true,
                    Timeout = 5000
                };
                await toastObj!.ShowAsync(t1);
                spinner = false;
                return;
            }
            // if (imgfileThumb == null)
            // {
            //     // thmbnail image empty msg
            //     return;
            // }
            // if (imgfileHdr == null)
            // {
            //     // header image empty msg
            //     return;
            // }
            // if (searchTags.Count <= 0)
            // {
            //     // searchtag empty msg
            //     return;
            // }
            string contentText = await rTxt.GetTextAsync();
            string postName = postObj.postName + "-" + DateTime.Now.ToString("ddMMMyyyyHHmmssff");
            postObj.postThumbnailPath = await saveImageThumb("", "");
            postObj.postHeadImagePath = await saveImageHDR("", "");
            postObj.postIconImagePath = "";
            //await saveAttachments("");

            var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
            var user = authState.User;
            var userId = "";
            if (user.Identity is { Name: not null })
            {
                userId = user.Identity.Name;
            }

            await service.savePost(postObj, searchTags, attachmentDTO, -1, contentText, userId, (int)DTO.Section.DocumentationManagement);

            allPosts = await service.getAllPosts((int)DTO.Section.DocumentationManagement);

            postObj = new();
            searchTags = new();
            profilePicThumb = "images/upload.jpg";
            profilePicHdr = "images/upload.jpg";
            imgfileThumb = null;
            imgfileHdr = null;
            attachments = new();
            attachmentDTO = new();
            files = new List<IBrowserFile>();

            spinner = false;
            this.StateHasChanged();

            ToastModel t = new() { Title = "Information", Content = "Record Saved successfully." };
            await toastObj.ShowAsync(t);

            dlgForm.HideAsync();

            //nav.NavigateTo("/");

        }
        catch (Exception ex)
        {
            ToastModel t = new() { Title = "Error", Content = ex.Message.ToString() };
            await toastObj.ShowAsync(t);
        }
    }

    private List<SubCategoryDto> _subCategories = new List<SubCategoryDto>();

    private async Task OnCategorySelect(ChangeEventArgs<int, Category> obj)
    {
        postObj.postSubCategoryId = null;
        _subCategories = await service.GetSubCategories(obj.Value);
    }
}

<style>
    #banner {
        position: fixed;
        left: 50%;
        margin-left: -240px;
        width: 480px;
        height: 115px;
        border-radius: 20px;
        text-align: center;
    }


    .e-grid td.e-active {
        background: #faa601 !important;
    }
</style>