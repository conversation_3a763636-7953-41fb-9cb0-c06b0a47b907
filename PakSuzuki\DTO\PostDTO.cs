﻿using Syncfusion.Blazor.RichTextEditor.Internal;
using System.ComponentModel.DataAnnotations;

namespace PakSuzuki.DTO
{
    public class PostDTO
    {
        public DateTime? postPublishDate { get; set; } = DateTime.Now;
        public int? postSubCategoryId { get; set; }
        public string? postPublishDateStr =>
            postPublishDate == null ? "" : postPublishDate.Value.ToString("d MMM, yyyy");
        public DateTime? postExpiryDate { get; set; } = DateTime.Now;
        public int postId { get; set; }
        [Required(ErrorMessage ="Post Name Required")]
        [MaxLength(200)]
        public string postName { get; set; }
        public string? PostCategory { get; set; }
        public string? PostSubCategory { get; set; }
        public string postRemarks { get; set; }
        public string postContentText { get; set; }
        
        public string? PostExternalLink { get; set; }
        public string postContentHTML { get; set; }
        public string postThumbnailPath { get; set; }
        public string postHeadImagePath { get; set; }
        public string postIconImagePath { get; set; }
        public int postCategoryId { get; set; }
        public string postDate { get; set; }
        public int? postSectionId { get; set; }
        public int? JobDepartmentId { get; set; }
        public int? JobCityId { get; set; }
        public int? JobTypeId { get; set; }
        public string? JobType { get; set; }
        public DateTime? JobLastDateToApply { get; set; }
        public string? CreatedBy { get; set; }
        public DateTime? CreatedDate { get; set; }
        public string? ModifiedBy { get; set; }
        public DateTime? ModifiedDate { get; set; }
        public string? PostWidth { get; set; }
        public string? PostHeight { get; set; }
        //public string postStatus { get; set; }
        // return postStatus based on following roles, if postPublishDate is null then return "Draft", else if postPublishDate is greater than current date then return "Scheduled", else if postExpiryDate is less than current date then return "Expired", else return "Published"
        public string postStatus
        {
            get
            {
                // make postPublishDate as date only
                
                if (postPublishDate == null)
                {
                    return "Draft";
                }

                DateTime currentDate = DateTime.Today;
                postPublishDate = postPublishDate?.Date;
                if (postPublishDate > currentDate)
                {
                    return "Draft";
                }

                if (postExpiryDate != null && currentDate >= postExpiryDate)
                {
                    return "Expired";
                }

                return "Published";
            }
        }
        //public string postStatus
        //{
        //    get
        //    {
        //        if (postPublishDate == null)
        //        {
        //            return "Draft";
        //        }

        //        if (postPublishDate < DateTime.Today)
        //        {
        //            return "Draft";
        //        }

        //        if (postPublishDate >= DateTime.Today && postExpiryDate<DateTime.Today)
        //        {
        //            return "Published";
        //        }
        //        return "Expired";

        //    }
        //}

        public List<string> Tags { get; set; } = new List<string>();
        public List<AttachmentDto>? Attachments { get; set; }
        public string? JobDepartment { get; internal set; }
        public string? JobCity { get; internal set; }
        public string? JobDivisionName { get; set; }
        public string? JobDepartmentName { get; set; }
    }


    public enum Section
    {
        Announcements = 1, NewsAneEvents = 2, DigitalLearning = 3, EmployeeSpotLight = 4, Collaboration = 5,
        Poll = 6, Wellness = 7, policy = 8, mission = 9, vision = 10, Newsletter = 11, Calender = 13, Organogram = 14, DocumentationManagement=15, QualityStatements = 22, maxFileSize=1024*30,
        InternalVacancies=23, QuickAnnouncement=25


    }
}
