﻿.card {
    height: 370px;
    box-shadow: var(--mud-elevation-1);
    border-color: #bbb;
    width: 100%;
    border-radius: 5px;
    display: flex;
    flex-direction: column;
    justify-content: space-between
}

.header {
    display: flex;
    gap: 20px;
    height: 70px;
    align-items: center;
    padding: 10px;
    border-bottom: 1px solid #999;
    color: #013582;
    font-weight: bold;
    justify-content: flex-start;
    padding-left: 20px;
}

.badge {
    background-color: #d8e8ff;
    color: #013582;
    padding: 10px;
    border-radius: 50%;
    font-weight: bold;
}

.content {
    padding: 10px 20px;
    overflow-y: auto;
}

.question {
    font-size: 16px;
    font-weight: bold;
    color: #013582
}

.footer {
    font-size: 12px;
    color: #888;
    padding: 10px;
    display: flex;
    justify-content: space-between
}

.status-closed {
    color: red;
    font-weight: bold;
}
