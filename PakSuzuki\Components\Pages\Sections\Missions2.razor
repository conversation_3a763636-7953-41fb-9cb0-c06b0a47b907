﻿@page "/setup/missions2"
@using Section = PakSuzuki.DTO.Section
@inject IWebHostEnvironment env
@inject PostDataService service
@inject NavigationManager nav
@inject SfDialogService DialogService
@inject AuthenticationStateProvider AuthenticationStateProvider

<SfToast @ref="toastObj"></SfToast>

<h2>Missions</h2>

<SfDialog @ref="dlgForm" Visible="false" Width="600px" ShowCloseIcon="true" CloseOnEscape="true" IsModal="true">
    <DialogTemplates>
        <Header>Missions</Header>
        <Content>

            @*  <div class="row mb">
                <div class="col-md">
                <MudImage Width="120" Height="70" Src="@profilePicThumb" Alt="Vision Icons" Elevation="25" Class="rounded-lg" />
                <MudFileUpload T="IBrowserFile" Accept=".png, .jpg" FilesChanged="UploadFiles2" MaximumFileCount="100">
                <ButtonTemplate Context="ctx">
                <MudButton HtmlTag="label"
                Variant="Variant.Filled"
                Color="Color.Primary"
                StartIcon="@Icons.Material.Filled.CloudUpload"
                Size="Size.Small"
                for="@ctx.Id">
                Select File
                </MudButton>
                </ButtonTemplate>
                </MudFileUpload>
                </div>
                </div> *@
            <div class="row">
                <div class="col-md">
                    <div class="col-md">
                        <SfRichTextEditor Placeholder="Heading" FloatLabelType="FloatLabelType.Auto" @bind-Value="postDTO.postName" Enabled="IsFormEditable" />
                    </div>
                </div>
            </div>
            @*   <div class="row">
                <div class="col-md">
                <SfTextBox Placeholder="Description" FloatLabelType="FloatLabelType.Auto" @bind-Value="postDTO.postContentText" Multiline="true" />
                </div>
                </div> *@

            <div style="height:14px"></div>
            <div class="row mb-2">
                <div class="col-md-4">
                    <label>Publish Date</label>
                    <Syncfusion.Blazor.Calendars.SfDatePicker Enabled="IsFormEditable"
                                                              Placeholder="Publish Date" @bind-Value="postDTO.postPublishDate" />
                </div>
                <div class="col-md-4">
                    <label>Expiry Date</label>
                    <Syncfusion.Blazor.Calendars.SfDatePicker Placeholder="Expiry Date" @bind-Value="postDTO.postExpiryDate" />
                </div>
            </div>
            <div style="height:12px;"></div>
            <div class="row">
                <div class="mb-2 col-md">
                    <MudButton ButtonType="MudBlazor.ButtonType.Submit" Color="Color.Primary"
                               Variant="Variant.Filled" Size="Size.Small" StartIcon="@Icons.Material.Filled.Save" OnClick="savePost">Save</MudButton>
                    <MudButton ButtonType="MudBlazor.ButtonType.Button" Color="Color.Success"
                               Variant="Variant.Filled" Size="Size.Small" OnClick="(() => dlgForm.HideAsync())">Cancel</MudButton>
                </div>
            </div>

        </Content>
    </DialogTemplates>
</SfDialog>

<div class="row mb-2">
    <div class="col-md">
        <MudButton Size="Size.Small" Color="Color.Primary" StartIcon="@Icons.Material.Filled.Add" Variant="Variant.Filled" OnClick="OpenCreateForm">Add</MudButton>
        <MudButton Size="Size.Small" Color="Color.Success" StartIcon="@Icons.Material.Filled.Edit" Variant="Variant.Filled" OnClick="OpenEditForm">Edit</MudButton>
        <MudButton Size="Size.Small" Color="Color.Warning" StartIcon="@Icons.Material.Filled.Delete" Variant="Variant.Filled" OnClick="ConfirmDelete">Delete</MudButton>
        <MudButton Size="Size.Small" Color="Color.Info" StartIcon="@Icons.Material.Filled.Group" Variant="Variant.Filled" OnClick="openAssignRole">Assign Roles</MudButton>
    </div>
</div>

<div class="row">
    <div class="mb-2 col-md">
        @if (allPosts.Any())
        {
            <SfGrid @ref="dgMain" Height="calc(100vh -220px)" DataSource="allPosts" AllowSelection="true" AllowFiltering="true" AllowSorting="true" Width="100%">
                <GridFilterSettings Type="Syncfusion.Blazor.Grids.FilterType.Excel"></GridFilterSettings>
                <GridColumns>
                    <GridColumn AutoFit="true" Visible="false" HeaderText="ID" Field="@nameof(PostDTO.postId)"></GridColumn>
                    <GridColumn Width="450" HeaderText="Title">
                        <Template Context="po">
                            @{
                                var obj = po as PostDTO;
                                if (obj != null)
                                {
                                    @((MarkupString)obj.postName)
                                }
                            }
                        </Template>
                    </GridColumn>
                    <GridColumn AutoFit="true" HeaderText="Publish Date" Field="@nameof(PostDTO.postPublishDate)" Format="dd-MMM-yyyy"></GridColumn>
                    <GridColumn AutoFit="true" HeaderText="Expiry Date" Field="@nameof(PostDTO.postExpiryDate)" Format="dd-MMM-yyyy"></GridColumn>
                    <GridColumn AutoFit="true" HeaderText="Status" Field="@nameof(PostDTO.postStatus)"></GridColumn>
                    <GridColumn AutoFit="true" HeaderText="Created By" Field="@nameof(PostDTO.CreatedBy)"></GridColumn>
                    <GridColumn AutoFit="true" HeaderText="Created Date" Field="@nameof(PostDTO.CreatedDate)" Format="dd-MMM-yyyy h:mm:ss tt"></GridColumn>
                    <GridColumn AutoFit="true" HeaderText="Modified By" Field="@nameof(PostDTO.ModifiedBy)"></GridColumn>
                    <GridColumn AutoFit="true" HeaderText="Modified Date" Field="@nameof(PostDTO.ModifiedDate)" Format="dd-MMM-yyyy h:mm:ss tt"></GridColumn>
                </GridColumns>
            </SfGrid>
        }
    </div>
</div>

<SfDialog @ref="dlgFormRole" Visible="false" Width="500px" ShowCloseIcon="true" CloseOnEscape="true" IsModal="true">
    <DialogTemplates>
        <Header>Assign Roles</Header>
        <Content>
            <div class="row" style="display:flex">
                <MudText Typo="Typo.caption">Selected Post: <b>@postDTO.postName</b></MudText>
            </div>
            <div style="height:10px;"></div>
            <div class="row" style="text-align:right;">
                <SfCheckBox @bind-Checked="isChecked" Label="Select All" @onchange="selectAll">Select All</SfCheckBox>
            </div>
            <div style="height:10px;"></div>
            <div class="row">
                <SfGrid @ref="dgMainRole" Height="400px" DataSource="allRole" AllowSelection="true" AllowFiltering="true" AllowSorting="true" Width="500px">
                    <GridFilterSettings Type="Syncfusion.Blazor.Grids.FilterType.Excel"></GridFilterSettings>
                    <GridColumns>
                        <GridColumn AutoFit="true" Visible="false" HeaderText="Id" Field="@nameof(RoleDTO.Id)"></GridColumn>
                        <GridColumn Width="350" HeaderText="Roles" Field="@nameof(RoleDTO.Name)"></GridColumn>
                        <GridColumn Field=@nameof(RoleDTO.isSelect) HeaderTextAlign="TextAlign.Center" HeaderText="Selected" Width="120" ClipMode="ClipMode.EllipsisWithTooltip">
                            <Template>
                                @{
                                    var ctx = (context as RoleDTO);
                                    <div>
                                        <center><SfCheckBox @bind-Checked="@ctx.isSelect"></SfCheckBox></center>
                                    </div>
                                }
                            </Template>
                        </GridColumn>
                    </GridColumns>
                </SfGrid>

            </div>
            <div class="page-panel">
                <div>
                    <MudButton Size="Size.Small" Color="Color.Primary" StartIcon="@Icons.Material.Filled.Save" Variant="Variant.Filled" OnClick="SaveUserRole">Save</MudButton>
                    <MudButton Size="Size.Small" Color="Color.Success" StartIcon="@Icons.Material.Filled.Cancel" Variant="Variant.Filled" OnClick="(() => dlgFormRole!.HideAsync())">Cancel</MudButton>
                </div>
            </div>
        </Content>
    </DialogTemplates>
</SfDialog>
@code {
    IList<IBrowserFile> files = new List<IBrowserFile>();
    private IBrowserFile? imgfileThumb = null;
    private string profilePicThumb = "images/upload.jpg";
    SfGrid<PostDTO>? dgMain;
    private List<PostDTO> allPosts = new();
    SfDialog? dlgForm, dlgFormRole;
    DateTime? date = DateTime.Today;
    private PostDTO postDTO = new();
    private SfToast? toastObj = new();
    private bool spinner = false;
    private List<RoleDTO> allRole = new();
    private RoleDTO Role = new();
    SfGrid<RoleDTO>? dgMainRole;
    private bool isPublish = true;
    bool isChecked = false;
    bool IsFormEditable = true;
    private async void selectAll(Microsoft.AspNetCore.Components.ChangeEventArgs args)
    {
        try
        {
            foreach (var item in allRole)
            {
                item.isSelect = isChecked;
            }
            this.StateHasChanged();
        }
        catch (Exception ex)
        {
            ToastModel tm = new ToastModel() { Content = ex.Message.ToString(), Title = "Error", ShowCloseButton = true, Timeout = 0 };
            await toastObj!.ShowAsync(tm);
        }
    }
    private async Task SaveUserRole()
    {
        try
        {
            await service.savePostRolesInPostScreen(allRole, postDTO.postId);
            ToastModel tm = new ToastModel() { Content = "Roles assigned successfully.", Title = "Info", ShowCloseButton = true, Timeout = 5000 };
            await toastObj!.ShowAsync(tm);
            await dlgFormRole!.HideAsync();
        }
        catch (Exception ex)
        {
            ToastModel tm = new ToastModel() { Content = ex.Message.ToString(), Title = "Error", ShowCloseButton = true, Timeout = 5000 };
            await toastObj!.ShowAsync(tm);
        }
    }

    private async Task openAssignRole()
    {
        try
        {
            if (dgMain!.SelectedRecords is { Count: > 0 })
            {
                int pId = dgMain.SelectedRecords[0].postId;
                allRole = await service.GetAllRolesAsyncWithSelectedPost(pId);
                postDTO = await service.getPostWithId(pId);
                await dlgFormRole!.ShowAsync();
            }
            else
            {
                ToastModel tm = new ToastModel() { Content = "Please select user.", Title = "Info", ShowCloseButton = true, Timeout = 5000 };
                await toastObj!.ShowAsync(tm);
            }
        }
        catch (Exception ex)
        {
            ToastModel tm = new ToastModel() { Content = ex.Message.ToString(), Title = "Error", ShowCloseButton = true, Timeout = 5000 };
            await toastObj!.ShowAsync(tm);
        }
    }

    private async void OpenEditForm()
    {
        if (dgMain.SelectedRecords.Count > 0)
        {
            if (dgMain?.SelectedRecords[0].postStatus != "Expired")
            {

                //isPublish = false;
                var pId = dgMain.SelectedRecords[0].postId;
                postDTO = await service.getPostWithId(pId);
                isPublish = postDTO.postPublishDate != null && !(postDTO.postPublishDate >= DateTime.Today);
                IsFormEditable = postDTO.postStatus is "Draft" or "Published";

                if (postDTO.postHeadImagePath != "")
                {
                    profilePicThumb = AppDataService.ConvertToUrl(postDTO.postHeadImagePath);
                }
                else
                {
                    profilePicThumb = "images/upload.jpg";
                }


                dlgForm!.ShowAsync();
            }
            else
            {
                ToastModel tm = new ToastModel()
                {
                    Content = "Post is expired, you can not edit.",
                    Title = "Info",
                    ShowCloseButton = true,
                    Timeout = 5000,
                    CssClass = "e-warning",
                    ShowProgressBar = true
                };
                await toastObj!.ShowAsync(tm);
            }
        }
    }

    private async Task<string> saveImageThumb(string postTitle, string fileName)
    {
        if (imgfileThumb != null && profilePicThumb != "images/upload.jpg")
        {
            postTitle = System.Guid.NewGuid().ToString();
            fileName = postTitle;
            string dirPath = $"{env.WebRootPath}\\Posts\\Missions\\{postTitle}";
            if (System.IO.Directory.Exists(dirPath) == false) { System.IO.Directory.CreateDirectory(dirPath); }
            Stream stream = imgfileThumb.OpenReadStream(long.MaxValue);
            var path = dirPath + "\\" + fileName + ".png";
            FileStream fs = File.Create(path);
            await stream.CopyToAsync(fs);
            stream.Close();
            fs.Close();
            imgfileThumb = null;
            return await Task.FromResult(path);
        }
        else
        {
            return await Task.FromResult(profilePicThumb);
        }
        return await Task.FromResult("");
    }

    private async void UploadFiles2(IBrowserFile file)
    {
        imgfileThumb = file;
        MemoryStream memoryStream = new MemoryStream();
        using var fileStream = file.OpenReadStream(long.MaxValue);
        await fileStream.CopyToAsync(memoryStream);
        byte[] bytes = memoryStream.ToArray();
        profilePicThumb = "data:image/png;base64," + Convert.ToBase64String(bytes);
        this.StateHasChanged();
    }

    private void OpenCreateForm()
    {
        IsFormEditable = true;
        isPublish = true;

        postDTO = new() { postExpiryDate = DateTime.Now.AddDays(60) };
        dlgForm!.ShowAsync();
    }
    private async void ConfirmDelete()
    {
        try
        {
            if (dgMain!.SelectedRecords is { Count: > 0 })
            {
                var conf = await DialogService.ConfirmAsync("Are you sure want to delete this record", "Confirm");
                if (conf)
                {
                    var Id = dgMain.SelectedRecords[0].postId;
                    service.deletePost(Id);
                    allPosts = await service.getAllPosts((int)Section.mission);
                    this.StateHasChanged();
                }
            }
        }
        catch (Exception ex)
        {
            ToastModel t = new()
            {
                Title = "Error",
                Content = ex.Message
                                            ,
                ShowProgressBar = true,
                ShowCloseButton = true,
                Timeout = 5000

            };
            await toastObj.ShowAsync(t);
        }
    }
    protected override async Task OnInitializedAsync()
    {
        try
        {
            allPosts = await service.getAllPosts((int)Section.mission);
        }
        catch
        {
            // throw;
        }
    }
    private async Task savePost()
    {
        try
        {
            spinner = true;
            this.StateHasChanged();
            string msg = await service.checkPublishExpiryDate(postDTO.postPublishDate ?? DateTime.Now, postDTO.postExpiryDate ?? DateTime.Now, postDTO.postId);
            if (msg != "")
            {
                ToastModel t1 = new()
                {
                    Title = "Information",
                    Content = msg
                            ,
                    ShowProgressBar = true,
                    ShowCloseButton = true,
                    Timeout = 5000
                };
                await toastObj!.ShowAsync(t1);
                spinner = false;
                return;
            }
            if (String.IsNullOrEmpty(postDTO.postName) || postDTO.postName == "")
            {
                ToastModel t1 = new()
                {
                    Title = "Information",
                    Content = "Title field is required.."
                                                ,
                    ShowProgressBar = true,
                    ShowCloseButton = true,
                    Timeout = 5000

                };
                await toastObj.ShowAsync(t1);
                spinner = false;
                return;
            }

            string postName = postDTO.postName + "-" + DateTime.Now.ToString("ddMMMyyyyHHmmssff");
            //postDTO.postThumbnailPath = await saveImageThumb(postName.Replace(" ", "").ToLower(), postDTO.postName.Replace(" ", "").ToLower());
            postDTO.postThumbnailPath = await saveImageThumb("", "");

            var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
            var user = authState.User;
            var userId = "";
            if (user.Identity is { Name: not null })
            {
                userId = user.Identity.Name;
            }

            await service.savePostMission(postDTO, -1, userId);

            allPosts = await service.getAllPosts((int)Section.mission);

            postDTO = new();

            spinner = false;
            this.StateHasChanged();

            ToastModel t = new()
            {
                Title = "Information",
                Content = "Record Saved successfully."
                                            ,
                ShowProgressBar = true,
                ShowCloseButton = true,
                Timeout = 5000

            };
            await toastObj!.ShowAsync(t);

            dlgForm.HideAsync();

            //nav.NavigateTo("/");

        }
        catch (Exception ex)
        {
            ToastModel t = new() { Title = "Error", Content = ex.Message };
            await toastObj!.ShowAsync(t);
        }
    }
}

<style>
    #banner {
        position: fixed;
        left: 50%;
        margin-left: -240px;
        width: 480px;
        height: 115px;
        border-radius: 20px;
        text-align: center;
    }

    .e-grid td.e-active {
        background: #faa601 !important;
    }
</style>
