

body {
    background-color: #F4F6F9 !important;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
}

html, body {
    overscroll-behavior: none;
}
body, p, h1, h2, h3 {
    font-family: "Poppins", sans-serif;
    font-weight: 400;
}
.userprofilecard {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 6px;
    width: 100%;
}

    .userprofilecard h3 {
        font-size: 16px;
        color: #013582;
        font-weight: bold;
        line-height: 16px;
        margin: 10px 0;
    }

    .userprofilecard p {
        color: dimgray;
        font-size: 12px;
        margin: 0;
        margin-bottom: 5px
    }

.userlocationcard {
    color: white;
    background: #013582;
    border-radius: 6px;
    width: 80%;
    padding: 4px;
    font-size: 12px;
    text-align: center;
}

.mud-appbar {
    background: #013582 !important;
}

.ticker {
    display: flex;
    height: 50px;

}

    .ticker .head {
        background-color: #E5655A;
        width: 170px;
        color: white;
        height: 50px;
        text-align: center;
        /*padding-top:12px;*/
        font-size:18px;
        display:flex;
        justify-content:center;
        align-items:center;
        font-weight: bold;
    }

    .ticker .text {
        padding: 3px;
        color: #013582;
        background-color: #E4E4E4;
        font-weight: bold;
        padding: 12px;
        flex: 1;
        height: 50px;
        font-size: 16px;
        padding-left:20px;
        overflow-x: auto;
        white-space: nowrap;
        scrollbar-width: thin;
        scrollbar-color: #013582 #E4E4E4;
        overflow-y: hidden;


    }

    
.page-panel {
    display: flex;
    justify-content: space-between;
    margin: 6px;
    min-height: 60px;
    align-items: center;
    flex-wrap: wrap;
}


.e-dialog
{
    max-height: 90vh !important;
}


.sec-title {
    color: #013582;
    font-size: 1.4rem;
    font-weight: 600;
}

.c-container{
    padding: 5px 10px;
}


.bit-csl-dot>div
{
    background-color: #78a8f1 !important;
}

.bit-csl-dot>div.current
{
    background-color: #013582 !important;
}

.bit-icon {

    background-color: white;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    padding: 10px;
}

.vision-card {
    display: flex;
    padding: 10px;
    align-items: center;
    gap: 20px;
    height: 159px;
    cursor: pointer;
    
    overflow: hidden;
}

.vision-card div img {
    width: 100px;
    height: 100px;
    object-fit:  cover;
}

.vision-card div h3 {
    cursor:pointer; margin-bottom: 22px; font-size: 20px;font-weight: bold;
}
.vision-card div p {
    font-size:11px;
}
.view-all {
    color: darkblue;
    text-decoration: underline;
    font-weight: bold;
    font-size:16px;
}
.view-all-container {
    display: flex;
    justify-content: space-between;
    margin-left: 5px;
    margin-right: 10px;
    margin-top: 10px;
    align-items: center
}

.separator {
    height: 1px;
    width: 100%;
    margin-top: 25px;
    margin-bottom: 15px;
    border-top: 1px solid #7777;
}

.item {
        background-color: white;
        width: 33%;
    }


.e-dialog .e-dlg-header {
    /*color: white;
    
    font-size: 20px;
    font-weight: normal;*/
}

.e-dialog .e-dlg-header-content {
}



.mud-toolbar-appbar {
    background: url('/images/header-left.png');
    background-repeat: no-repeat;
}

.e-popup.e-popup-open.e-dialog {
    border: 1px solid #013582;
}

.e-dialog .e-dlg-header-content{
    background-color: #013582;
    color: white;
    font-size: 20px;
    font-weight: normal;
}
.e-dialog .e-dlg-header,
.e-dlg-header, .e-dlg-header * {
    color: white !important;
}

/* trucate text if text is exceeded three lines */

/* Set close button color white for Syncfuion Dialog*/
.e-dialog .e-btn .e-btn-icon.e-icon-dlg-close::before {
    color: white;
}

.e-grid td.e-active {
    background: #faa601 !important;
}

.validation-message{
    color:red; font-weight:bold; font-size:10px;padding:6px;background-color: #ffe9e9;
    
}