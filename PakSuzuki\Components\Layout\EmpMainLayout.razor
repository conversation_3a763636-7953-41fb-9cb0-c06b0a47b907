﻿@inherits LayoutComponentBase
@inject IWebHostEnvironment env
@inject AppDataService service
@inject CorporateService cService
@inject AuthenticationStateProvider AuthenticationStateProvider
@using PakSuzuki.DTO 
    <style>
        .mmii {
        }

            .mmii button {
                margin: 0;
                padding: 0;
            }
    </style>
<MudThemeProvider />
<MudDialogProvider />
<MudSnackbarProvider />
<style>
</style>

<MudLayout>
    <MudAppBar Elevation="1">
        <MudIconButton Icon="@Icons.Material.Filled.Menu" Color="Color.Inherit" Edge="Edge.Start" OnClick="@((e) => DrawerToggle())" />
        <MudSpacer />
        <MudText Typo="Typo.h5" Style="font-weight:600">CORPORATE INTRANET PORTAL - Employee Portal</MudText>
        <MudSpacer />
        @*<MudIconButton Icon="@Icons.Material.Filled.Notifications" Color="Color.Inherit" Edge="Edge.End" />*@
   
        <AuthorizeView>
            <Authorized>
                <MudBadge Content="@approvalsList.Sum(x=>x.Total)" Color="Color.Error" Overlap="true">
                    <MudMenu Label="Open Menu" Icon="@Icons.Material.Filled.Notifications" Color="Color.Inherit" Class="mmii" AnchorOrigin="Origin.BottomCenter">
                        <MudMenuItem Icon="@Icons.Material.Filled.Refresh" OnClick="RefreshApprovals">Refresh</MudMenuItem>
                        <MudDivider />
                        @foreach (var item in approvalsList)
                        {
                            <MudMenuItem><a target="@item.ProjectCode" href="@item.Link">@item.ProjectCode @item.ProjectName (@item.Total)</a></MudMenuItem>
                        }

                    </MudMenu>
                </MudBadge>

                <p style="margin-left: 20px;margin-right:10px;">Welcome: @GetUserFullName(context.User.Identity.Name)</p>
                @{
                    string url = "/uploadUserPics/missing.jpg";
                    var userId = context.User.Identity.Name.Replace("\\", "-");
                    string path = $"{env.WebRootPath}\\uploadUserPics\\" + userId + ".png";
                    if (File.Exists(path))
                    {
                        url = "/uploadUserPics/" + userId + ".png";
                    }

                    <a href="/myprofile">
                        <img src="@url" alt="Alternate Text" style="width:40px;height:40px;object-fit:cover;border-radius: 50%" />

                    </a>
                }


            </Authorized>
            <NotAuthorized>
                <p>You are not authorized!</p>
            </NotAuthorized>
        </AuthorizeView>


    </MudAppBar>
    <MudDrawer @bind-Open="_drawerOpen" Elevation="2">
        <MudDrawerHeader>
            <a href="/">
                <div style="display: flex; align-items: center; justify-content:center">
                    <img src="/images/logo.png" alt="Pak Suzuki" width="80%" />

                </div>
            </a>
        </MudDrawerHeader>
        <EmpNavMenu />
    </MudDrawer>
    <MudMainContent>
        <div class="c-container">

            @Body
        </div>
    </MudMainContent>
</MudLayout>
@code {
    bool _drawerOpen = true;

    void DrawerToggle()
    {
        _drawerOpen = !_drawerOpen;
    }
    private List<PendingApprovalDto> pendingApprovals = new List<PendingApprovalDto>();

    //private List<PendingApprovalDto> groupList = new List<PendingApprovalDto>();
    private List<PendingApprovalDto> approvalsList = new List<PendingApprovalDto>();

    private string GetUserFullName(string userId)
    {
        string fullName = service.GetUserFullName(userId).Result;
        return fullName;
    }

    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();
        await RefreshApprovals();
    }

    private async Task RefreshApprovals()
    {
        var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        var user = authState.User;
        var userId = "";
        if (user.Identity is { Name: not null })
        {
            userId = user.Identity.Name;
        }

        approvalsList = await cService.GetEmpPendingApprovals(userId);
        approvalsList = approvalsList.Where(c => c.Total > 0).ToList();
    }
}