<div class="progress-bar">
    <div class="label">@Label</div>
    <div class="bar">
        <div class="value" style="width: @Percentage%;display:inline-block">@Percentage%</div>
    </div>
</div>

@code {
    [Parameter] public string Label { get; set; }
    [Parameter] public int Value { get; set; }
    [Parameter] public int Total { get; set; }
    public double Percentage => Total == 0 ? 0 : double.Round((double)Value / (double)Total * 100.0,2);


}