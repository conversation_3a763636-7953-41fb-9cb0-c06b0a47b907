﻿@page "/employee/myjd"
@using System.IO
@using PakSuzuki.Components.Layout
@using Path = System.IO.Path
@inject AuthenticationStateProvider AuthenticationStateProvider
@layout EmpMainLayout
@inject CorporateService cService

<MudText Typo="Typo.h5">My Job Description</MudText>
@* Embed PDF File *@
<embed src="@pdfPath" width="100%"  style="height: calc(100vh - 120px)" />
@code {
    private string pdfPath = "";
    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();

        var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        var user = authState.User;
        if (user.Identity is { Name: not null })
        {
            // download employee JD
            // output folder and file ./wwwroot/EmployeeJD/EmpCode.pdf
            // split the user.Identity.Name by '/' to get the empcode. second part of the split array is the empcode
            var empCode = user.Identity.Name.Split('\\')[1];

            // create the folder if not exists
            var folderPath = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "EmployeeJD");
            if (!Directory.Exists(folderPath))
            {
                Directory.CreateDirectory(folderPath);
            }

            // create download file path
            //var filePath = Path.Combine(folderPath, $"{empCode}.pdf");

            var op = await cService.DownloadMyJD(user.Identity.Name, folderPath);
            if (op.StartsWith("/employeejd/"))
            {
                pdfPath = op;
                // download success
                // open the file in new tab

            }
            else
            {
                // download failed
            }
        }
    }

}
