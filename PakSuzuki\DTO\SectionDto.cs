﻿namespace PakSuzuki.DTO
{
    public class SectionDto
    {
        // Id and Name fiedls
        public int Id { get; set; }
        public string Name { get; set; }
        public bool isSelect { get; set; }
        public string? Description { get; set; }
        public string? Module { get; set; }
        public int? ModuleId { get; set; }
        public int SortOrder { get; set; } = 1;
        public string? PostType { get; set; }
        public int? PostTypeId { get; set; }
        public bool IsActive { get; set; }
        public string Status => IsActive ? "Active" : "InActive";
        // list of post
        public List<PostDTO> Posts { get; set; } = new List<PostDTO>();

    }
}
