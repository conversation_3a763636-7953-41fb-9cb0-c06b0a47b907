﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace PakSuzuki.Model;

public partial class Post
{
    public int PostId { get; set; }

    public string PostName { get; set; }

    public string PostContentText { get; set; }

    public string PostContentHtml { get; set; }

    public byte[] PostThumbnailImage { get; set; }

    public string PostThumbnailImagePath { get; set; }

    public byte[] PostHeadImage { get; set; }

    public string PostHeadImagePath { get; set; }

    public byte[] PostIconImage { get; set; }

    public string PostIconImagePath { get; set; }

    public int? PostSectionId { get; set; }

    public int? PostCategoryId { get; set; }

    public int? PostCreatedBy { get; set; }

    public DateTime? PostCreatedDate { get; set; }

    public int? PostModifiedBy { get; set; }

    public DateTime? PostModifiedDate { get; set; }

    public DateTime? PostPublishDate { get; set; }

    public DateTime? PostExpiryDate { get; set; }

    public string PostApprovalStatus { get; set; }

    public int? PostApprovedStatusBy { get; set; }

    public DateTime? PostApprovalStatusDate { get; set; }

    public string PostIsActive { get; set; }

    public string PostIsDel { get; set; }

    public string PostRemarks { get; set; }

    public int? CategoryId { get; set; }

    public int? PostSubCategoryId { get; set; }

    public int? JobDepartmentId { get; set; }

    public int? JobDepartmentTypeId { get; set; }

    public DateTime? JobLastDateToApply { get; set; }

    public int? JobCityId { get; set; }

    public int? JobTypeId { get; set; }

    public string JobDepartmentName { get; set; }

    public string JobDivisionName { get; set; }

    public string PostExternalLink { get; set; }

    public string PostWidth { get; set; }

    public string PostHeight { get; set; }

    public string Column1 { get; set; }

    public virtual JobCity JobCity { get; set; }

    public virtual JobDepartment JobDepartment { get; set; }

    public virtual JobType JobType { get; set; }

    public virtual SubCategory PostSubCategory { get; set; }
}