﻿<style>
    .img-fluid {
        width: 100%;
        height: 100%;
        max-height: 250px;
        object-fit: cover;
        margin-top: 15px;
        border-radius: 10px;
        /*decent drop shadow and a good border outline */
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1), 0 1px 3px rgba(0, 0, 0, 0.08);
        border: 1px solid rgba(0, 0, 0, 0.125);
    }
</style>
@if (Items != null && Items.Any())
{
    // display first header image from the post as banner
    var firstPost = Items.FirstOrDefault();
    if (firstPost != null)
    {
        var headerImage = firstPost.postHeadImagePath;
        string style = "";
        if (!string.IsNullOrEmpty(firstPost.PostWidth) )
        {
            style += "width:"+ firstPost.PostWidth + ";";
        }
        if (!string.IsNullOrEmpty(firstPost.PostHeight))
        {
            style += "min-height:" + firstPost.PostHeight + ";";
        }
        if (headerImage != null && string.IsNullOrEmpty(firstPost.PostExternalLink))
        {
            <img src="@headerImage" class="img-fluid" alt="@Title" style="@style" />
        }
        else if (!string.IsNullOrEmpty(firstPost.postHeadImagePath) &&
            !string.IsNullOrEmpty(firstPost.PostExternalLink))
        {
            <a href="@firstPost.PostExternalLink" target="_blank">
                <img src="@headerImage" class="img-fluid" alt="@Title" style="@style" />
            </a>
        }
    }
}

@code {

    [Parameter] public List<PostDTO>? Items { get; set; }
    [Parameter] public string? Title { get; set; }
}
