<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="wwwroot\NewFolder1\**" />
    <Compile Remove="wwwroot\NewFolder\**" />
    <Content Remove="wwwroot\NewFolder1\**" />
    <Content Remove="wwwroot\NewFolder\**" />
    <EmbeddedResource Remove="wwwroot\NewFolder1\**" />
    <EmbeddedResource Remove="wwwroot\NewFolder\**" />
    <None Remove="wwwroot\NewFolder1\**" />
    <None Remove="wwwroot\NewFolder\**" />
  </ItemGroup>

  <ItemGroup>
    <Compile Remove="Services\EmployeeDataService.cs" />
  </ItemGroup>

  <ItemGroup>
    <Content Remove="Components\Pages\Sections\Missions2.razor" />
    <Content Remove="Components\Pages\Sections\Policies.razor" />
    <Content Remove="Components\Pages\Sections\Spotlight.razor" />
    <Content Remove="Components\Pages\Sections\TransformationPosts.razor" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Components\Layout\Employee\" />
    <Folder Include="wwwroot\media\charts\" />
  </ItemGroup>

  <ItemGroup>
    <None Include="efpt.config.json.user" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="bit.blazorui" Version="8.8.1" />
    <PackageReference Include="bit.blazorui.assets" Version="8.8.1" />
    <PackageReference Include="bit.blazorui.extras" Version="8.8.1" />
    <PackageReference Include="bit.blazorui.icons" Version="8.8.1" />
    <PackageReference Include="dapper" Version="2.1.35" />
    <PackageReference Include="EPPlus" Version="7.2.1" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.Negotiate" Version="8.0.5" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.4" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.4">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="8.0.4" />
    <PackageReference Include="Microsoft.Identity.Client" Version="4.61.3" />
    <PackageReference Include="MudBlazor" Version="6.19.1" />
    <PackageReference Include="Smart.Blazor" Version="19.0.5" />
    <PackageReference Include="Syncfusion.Blazor" Version="25.1.41" />
    <PackageReference Include="Syncfusion.Blazor.Themes" Version="25.1.41" />
  </ItemGroup>

  <ItemGroup>
    <_ContentIncludedByDefault Remove="Components\Pages\Transformations\DigitalTransofmations.razor" />
    <_ContentIncludedByDefault Remove="Components\Pages\Transformations\OrganizationTransformations.razor" />
  </ItemGroup>

</Project>
