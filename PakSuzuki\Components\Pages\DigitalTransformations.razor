@page "/transformations/{ModuleId:int}"
@inject TransformationDataService Service
@inject AppDataService AppService
@inject NavigationManager NavMgr
@rendermode InteractiveServer
<style>
    .c-container {
        padding: unset !important;
    }

    .m-link {
        color: #013582;
        text-decoration: none;
        cursor: pointer;
    }

    .m-link:hover {
        text-decoration: underline;
    }

    .truncate-text {
        max-height: 60px;
        overflow: hidden;
        text-overflow: ellipsis;
        /* only show max 3 lines */
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
    }

    .truncate-text ul {
        list-style: none;
        padding-left: 0;
    }

    .truncate-text ul li {
        position: relative;
        padding-left: 1em;
        margin-bottom: 0;
        font-size: 12px;
    }

    .truncate-text ul li::before {
        content: '';
        position: absolute;
        left: 0;
        top: 0.5em;
        width: 0.5em;
        height: 0.5em;
        background-color: #3498db;
        border-radius: 50%;
    }


    .msg ul {
        list-style: none;
        padding-left: 0;
    }

    .msg ul, .msg ol {
        margin-left: 1rem;
        padding-left: 1rem;
    }

    .msg ul li {
        position: relative;
        padding-left: 1em;
        margin-bottom: 0;
        font-size: 12px;
    }

    .msg ul li::before {
        content: '';
        position: absolute;
        left: 0;
        top: 0.5em;
        width: 0.5em;
        height: 0.5em;
        background-color: #3498db;
        border-radius: 50%;
    }
</style>
<div class="ticker">
    <div class="head" style="width: 100%;font-size:18px;">
        @moduleName
    </div>


</div>
<div class="container">

    @foreach (var sec in userSections)
    {
        <div class="row mb-2">

            <div class="col-md">
                @if (sec.PostType == "announcement")
                {
                    <CompCatalog Title="@sec.Description" CatalogType="@CompCatalogType.Card" Items="sec.Posts"
                                 DetailPageUrl=""/>
                }
                @if (sec.PostType == "gallery")
                {
                    <CompCatalog Title="@sec.Description" CatalogType="@CompCatalogType.Image" Items="sec.Posts"
                                 DetailPageUrl=""/>
                }
                @if (sec.PostType == "news & events")
                {
                    <CompNews Title="@sec.Description" Items="sec.Posts" DetailPageUrl=""/>
                }
                @if (sec.PostType == "banner")
                {
                    <CompBanner Title="@sec.Description" Items="sec.Posts"/>
                }
                @if (sec.PostType == "iframe")
                {
                    <CompFrame Title="@sec.Description" Items="sec.Posts"/>
                }
            </div>
        </div>
    }
</div>

@code
{
    [CascadingParameter] public Task<AuthenticationState>? AuthStateProvider { get; set; }
    [Parameter] public int? ModuleId { get; set; }

    private string userId = "";

    // list of sections
    private List<SectionDto> userSections = new();
    // on page initialize asycn

    // on parameter change async
    private string moduleName = "";

    protected override async Task OnParametersSetAsync()
    {
        if (ModuleId == null)
            NavMgr.NavigateTo("/");

        if (AuthStateProvider != null)
        {
            var authState = await AuthStateProvider;
            var user = authState.User;
            userId = "";
            if (user.Identity is { Name: not null })
            {
                userId = user.Identity.Name;
                userSections = await Service.GetUserSection(userId, ModuleId ?? 0);
                moduleName = await Service.GetModuleName(ModuleId ?? 0) ?? "";
                // create a dictionary of sections which contins list of posts
                foreach (var sec in userSections)
                {
                    sec.Posts = await AppService.GetUserPosts(userId, sec.Id);
                }
            }
        }
    }

    //protected override async Task OnInitializedAsync()
    //{
    //    if (authenticationStateProvider != null)
    //    {
    //        var authState = await authenticationStateProvider;
    //        var user = authState.User;
    //        _userId = "";
    //        if (user.Identity is { Name: not null })
    //        {
    //            _userId = user.Identity.Name;
    //            _userSections = await Service.GetUserSection(_userId, ModuleId);
    //            // create a dictionary of sections which contins list of posts
    //            foreach (var sec in _userSections)
    //            {
    //                sec.Posts = await AppService.GetUserPosts(_userId, sec.Id);
    //            }
    //        }
    //    }

    //}

}