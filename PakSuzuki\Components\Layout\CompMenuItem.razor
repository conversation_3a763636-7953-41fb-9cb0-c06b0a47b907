﻿@using PakSuzuki.DTO
@if (menu.HasSubMenu)
{
    var subMenu = from mm in userMenu where mm.ParentId == menu.Id select mm;
    @if (string.IsNullOrEmpty(menu.Icon))
    {
        <MudNavGroup Title="@menu.Name" Expanded="false">
            @{
                subMenu = subMenu.OrderBy(m => m.SortOrder).ToList();
            }
            @foreach (var kk in subMenu)
            {
                <CompMenuItem userMenu="@userMenu" userSubMenu="@userSubMenu" menu="@kk"/>
            }
        </MudNavGroup>
    }
    else
    {
        <MudNavGroup Title="@menu.Name"
                     Icon="@IconHelper.GetIconByName(menu.Icon)" Expanded="false">
            @{
                subMenu = subMenu.OrderBy(m => m.SortOrder).ToList();
            }
            @foreach (var kk in subMenu)
            {
                <CompMenuItem userMenu="@userMenu" userSubMenu="@userSubMenu" menu="@kk"/>
            }
        </MudNavGroup>
    }
}
else
{
    var trg = "";
    if (menu.URLTarget == "iframe")
    {
        trg = "";
    }
    else
    {
        trg = menu.URLTarget ?? "";
    }

    @if (string.IsNullOrEmpty(menu.Icon))
    {
        <MudNavLink Target="@trg"
                    Href="@menu.Url" Match="NavLinkMatch.Prefix">
            @menu.Name
        </MudNavLink>
    }
    else
    {
        <MudNavLink Target="@trg" Icon="@IconHelper.GetIconByName(menu.Icon)"
                    Href="@menu.Url" Match="NavLinkMatch.Prefix">
            @menu.Name
        </MudNavLink>
    }
}

@code {
    [Parameter] public MenuDTO menu { get; set; }
    [Parameter] public List<MenuDTO> userMenu { get; set; }
    [Parameter] public List<MenuDTO> userSubMenu { get; set; }
}