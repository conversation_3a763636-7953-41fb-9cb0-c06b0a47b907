﻿.survey-dashboard {
    /*display: flex;
    flex-wrap: wrap;
    overflow-x: auto;
    gap: 10px;*/
    /* added */
    /*max-width: 1200px;*/ /* adjust the width as needed */
    /*justify-content: space-between;
    gap: 20px;*/
}

.p-item .survey-card {
    background: white;
    padding: 10px;
    min-height: 300px;
    display: flex !important;
    width: unset;
    flex-direction: column;
    justify-content: space-between;
    border-radius: 5px;
    height: 100%;
    box-shadow: var(--mud-elevation-1);
    min-width:100%;
}

.survey-card .item {
    /*background-color: white;
    padding: 20px;
    border-radius: 5px;
    box-shadow: var(--mud-elevation-1)
    max-width: 33.33%;*/ /* added */
    /*width: 32%;*/ /* added */
    /*margin-bottom: 20px;*/
}

/* rest of the styles remain the same */
.survey-card .header {
    display: flex !important;
    align-items: center;
    margin-bottom: 10px;
    flex-direction: row;
    justify-content: flex-start;
}

.logo {
    background-color: lightblue;
    color: white;
    font-weight: bold;
    padding: 10px;
    border-radius: 50%;
    margin-right: 10px;
}

.title {
    font-size: 18px;
    font-weight: bold;
    /*color: #013582;*/
}

.question {
    font-size: 16px;
    margin-bottom: 20px;
    font-weight: 600;
    color: #013582;
}

.options {
    margin-bottom: 20px;
    flex-direction: column;
    align-content: stretch;
    justify-content: space-between;
    flex: 1;
    display: flex;
    justify-content: space-evenly;
}

.progress-bar {
    /*display: flex;*/
    align-items: center;
    margin-bottom: 10px;
}

.label {
    width: 150px;
    font-weight: bold;
    font-size: 12px;
}

.bar {
    height: 20px;
    background-color: lightgray;
    border-radius: 10px;
    width: 100%;
    overflow: hidden;
    display: inline-block;
}

.value {
    background-color: lightblue;
    height: 100%;
    text-align: right;
    padding-right: 10px;
    color: white;
    font-weight: bold;
    display: inline-block;
}

.deadline,
.poll-status {
    font-size: 14px;
    color: gray;
    text-align: right;
}



. /*p-item {
    background-color: white;
    padding: 20px;
    border-radius: 5px;
    box-shadow: var(--mud-elevation-1)
    max-width: 100%;*/ /* added */
/*width: 100%;*/ /* added */
/*margin-bottom: 20px;
}*/
.bit-csl-cnt :first-child {
}

.bit-csl-cnt :only-child {
}

.p-item div {
    transform: unset !important;
}
