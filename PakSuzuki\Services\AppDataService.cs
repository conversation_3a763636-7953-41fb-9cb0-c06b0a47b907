﻿using OfficeOpenXml;
using PakSuzuki.Components.Pages.Admin;
using PakSuzuki.Model;
using System.Runtime.Serialization;
using FileInfo = System.IO.FileInfo;
using Section = PakSuzuki.DTO.Section;

namespace PakSuzuki.Services;

public class AppDataService(ApplicationDbContext dc)
{
    public Task<List<MenuDTO>> GetUserMenus(string user, int moduleId = 1)
    {
        // Get Current Login User

        var m = (from a in dc.RoleMenus
                 join b in dc.UserRoles on a.RoleId equals b.RoleId
                 where b.User.UserId == user &&
                       b.User.IsActive &&
                       a.Menu.IsActive &&
                       a.Menu.ModuleId == moduleId
                 orderby a.Menu.SortOrder ?? 0, a.Menu.Name
                 select new MenuDTO
                 {
                     Id = a.MenuId,
                     Name = a.Menu.Name,
                     Url = a.Menu.Url,
                     URLTarget = a.Menu.Urltarget,
                     ParentId = a.Menu.MenuParentId,
                     SortOrder = a.Menu.SortOrder,
                     Icon = a.Menu.Icon
                 }).Distinct().ToList();

        foreach (var i in m)
        {
            i.HasSubMenu = (from mm in m where mm.ParentId == i.Id select mm).Any();
            if (i.URLTarget == "iframe")
            {

                i.Url = "/epage?url=" + Uri.EscapeDataString(i.Url);
            }
        }



        return Task.FromResult(m);

        // var q = (from a in dc.Menus
        //              where a.IsActive
        //              orderby a.SortOrder, a.Name
        //              select new MenuDTO
        //              {
        //                  Id = a.Id,
        //                  Name = a.Name,
        //                  Url = a.Url,
        //                  URLTarget = a.Urltarget,
        //                  ParentId = a.MenuParentId, SortOrder = a.SortOrder,
        //              }).ToList();
        //
        //     foreach (var i in q)
        //     {
        //         i.HasSubMenu = (from mm in q where mm.ParentId == i.Id select mm).Any();
        //     }
        //
        //     return Task.FromResult(q);
    }

    public static string ConvertToUrl(string filePath)
    {
        if (string.IsNullOrEmpty(filePath)) return "";
        // find last index of "wwwroot"

        var wwwrootIndex = filePath.LastIndexOf("wwwroot", StringComparison.OrdinalIgnoreCase);
        if (wwwrootIndex != -1)
        {
            // Remove everything before and including "wwwroot"
            var relativePath = filePath.Substring(wwwrootIndex + 8); // 8 is the length of "wwwroot\"

            // Replace backslashes with forward slashes
            relativePath = relativePath.Replace('\\', '/');

            // Add leading forward slash
            var x = "/" + relativePath.TrimStart('/');
            return x;
        }

        // Return original path if "wwwroot" is not found
        return filePath;
    }

    public Task<(string, string, string)> GetSectionText(DTO.Section section, string userId)
    {
        // all userId roles
        var userRoles = dc.UserRoles.Where(x => x.User.UserId == userId).Select(x => x.RoleId).ToList();
        // user's roles based on this section
        var q = (from a in dc.Posts
                 orderby a.PostPublishDate descending, (a.PostModifiedDate ?? a.PostCreatedDate) descending
                 where a.PostSectionId == (int)section &&
                       a.PostIsActive == "ACTIVE" &&
                       a.PostIsDel == "NO" &&
                       dc.PostRoles.Any(x => x.PostId == a.PostId && userRoles.Contains(x.RoleId)) &&
                       DateTime.Now >= a.PostPublishDate && DateTime.Today < a.PostExpiryDate
                 select new { a.PostName, a.PostIconImagePath, a.PostContentText }).FirstOrDefault();

        if (q != null)
            return Task.FromResult((q.PostName ?? "...", ConvertToUrl(q.PostIconImagePath), q.PostContentText ?? ""));

        return Task.FromResult(("...", "", ""));
    }

    public Task<List<PostDTO>> GetUserPosts(string user, DTO.Section section, string searchTag = "", int categoryId = -1, int subCategoryId = -1)
    {
        var userRoles = dc.UserRoles.Where(x => x.User.UserId == user).Select(x => x.RoleId).ToList();

        var q = (from a in dc.Posts
                 where a.PostSectionId == (int)section
                       && DateTime.Now >= a.PostPublishDate
                       && DateTime.Today < a.PostExpiryDate
                       && (categoryId == -1 || a.PostCategoryId == categoryId)
                       && (subCategoryId == -1 || a.PostSubCategoryId == subCategoryId)
                       && a.PostIsActive == "ACTIVE"
                       && a.PostIsDel == "NO"
                       && dc.PostRoles.Any(x => x.PostId == a.PostId && userRoles.Contains(x.RoleId))
                       && (searchTag == "" || dc.PostSearchTags.Any(p => p.PostId == a.PostId && p.PostSearchTagName.Contains(searchTag)))
                 orderby (a.PostModifiedDate ?? a.PostCreatedDate) descending
                 select new PostDTO
                 {
                     postId = a.PostId,
                     postCategoryId = a.PostCategoryId ?? 0,
                     postContentHTML = a.PostContentHtml,
                     postContentText = a.PostContentText,
                     postRemarks = a.PostRemarks,
                     postExpiryDate = a.PostExpiryDate,
                     postHeadImagePath = a.PostHeadImagePath,
                     postIconImagePath = a.PostIconImagePath,
                     postName = a.PostName,
                     postPublishDate = a.PostPublishDate,
                     postSectionId = a.PostSectionId ?? 0,
                     //postThumbnailPath =
                     //    (a.PostThumbnailImagePath ?? "").Replace(@"E:\OfficeProjects\PakSuzuki\PakSuzuki\wwwroot\",
                     //        "/").Replace(@"\","/")
                     postThumbnailPath = ConvertToUrl(a.PostThumbnailImagePath),
                     JobDepartment = a.JobDepartment.Name,
                     JobCity = a.JobCity.Name,
                     JobType = a.JobType.Name,
                     JobLastDateToApply = a.JobLastDateToApply,
                     JobDepartmentName = a.JobDepartmentName ?? "",
                     JobDivisionName = a.JobDivisionName ?? "",
                     PostExternalLink = a.PostExternalLink ??"",
                     PostWidth = a.PostWidth??"",
                     PostHeight = a.PostHeight ?? ""
                     //postThumbnailPath = a.PostThumbnailImagePath

                 }).ToList();
        //// also add all those posts which are not restricted to any role
        //var q2 = (from a in dc.Posts
        //          where a.PostSectionId == (int)section
        //                && DateTime.Now >= a.PostPublishDate
        //                && DateTime.Today <= a.PostExpiryDate
        //                && (categoryId == -1 || a.PostCategoryId == categoryId)
        //                && a.PostIsActive == "ACTIVE"
        //                && a.PostIsDel == "NO"
        //                && !dc.PostRoles.Any(x => x.PostId == a.PostId)
        //                && (searchTag == "" ||
        //                    dc.PostSearchTags.Any(p => p.PostId == a.PostId && p.PostSearchTagName.Contains(searchTag)))
        //          orderby a.PostCreatedDate descending
        //          select new PostDTO
        //          {
        //              postId = a.PostId,
        //              postCategoryId = a.PostCategoryId ?? 0,
        //              postContentHTML = a.PostContentHtml,
        //              postContentText = a.PostContentText,
        //              postRemarks = a.PostRemarks,
        //              postExpiryDate = a.PostExpiryDate,
        //              postHeadImagePath = a.PostHeadImagePath,
        //              postIconImagePath = a.PostIconImagePath,
        //              postName = a.PostName,
        //              postPublishDate = a.PostPublishDate,
        //              postSectionId = a.PostSectionId ?? 0,
        //              //postThumbnailPath =
        //              //    (a.PostThumbnailImagePath ?? "").Replace(@"E:\OfficeProjects\PakSuzuki\PakSuzuki\wwwroot\",
        //              //        "/").Replace(@"\","/")
        //              postThumbnailPath = ConvertToUrl(a.PostThumbnailImagePath)
        //              //postThumbnailPath = a.PostThumbnailImagePath

        //          }).ToList();
        //// combine both lists and make uniqueby postId
        //q.AddRange(q2);
        //q = q.GroupBy(x => x.postId).Select(x => x.First()).ToList();

        foreach (var p in q)
        {

            var op = (from a in dc.PostAttachments
                      where a.PostId == p.postId
                      select new AttachmentDto()
                      {
                          Name = a.PostAttachmentName,
                          Url = ConvertToUrl(a.PostAttachmentUrl)
                      }).ToList();

            p.Attachments = op.Any() ? op : new();
        }

        return Task.FromResult(q);
    }


    public Task<List<PostDTO>> GetUserPosts(string user, int sectionId)
    {
        var userRoles = dc.UserRoles.Where(x => x.User.UserId == user).Select(x => x.RoleId).ToList();

        var q = (from a in dc.Posts
                 where a.PostSectionId == (int)sectionId
                       && DateTime.Now >= a.PostPublishDate
                       && DateTime.Today < a.PostExpiryDate
                       && a.PostIsActive == "ACTIVE"
                       && a.PostIsDel == "NO"
                       && dc.PostRoles.Any(x => x.PostId == a.PostId && userRoles.Contains(x.RoleId))
                 orderby (a.PostModifiedDate ?? a.PostCreatedDate) descending
                 select new PostDTO
                 {
                     postId = a.PostId,
                     postCategoryId = a.PostCategoryId ?? 0,
                     postContentHTML = a.PostContentHtml,
                     postContentText = a.PostContentText,
                     postRemarks = a.PostRemarks,
                     postExpiryDate = a.PostExpiryDate,
                     postHeadImagePath = ConvertToUrl(a.PostHeadImagePath),
                     postIconImagePath = a.PostIconImagePath,
                     postName = a.PostName,
                     postPublishDate = a.PostPublishDate,
                     postSectionId = a.PostSectionId ?? 0,
                     postThumbnailPath = ConvertToUrl(a.PostThumbnailImagePath),
                     JobDepartment = a.JobDepartment.Name,
                     JobCity = a.JobCity.Name,
                     JobType = a.JobType.Name,
                     JobLastDateToApply = a.JobLastDateToApply,
                     JobDepartmentName = a.JobDepartmentName ?? "",
                     JobDivisionName = a.JobDivisionName ?? "",
                     PostExternalLink = (a.PostExternalLink??"").Trim(),
                     PostWidth = (a.PostWidth ?? "").Trim(),
                     PostHeight = (a.PostHeight ?? "").Trim()

                 }).ToList();

        foreach (var p in q)
        {

            var op = (from a in dc.PostAttachments
                      where a.PostId == p.postId
                      select new AttachmentDto()
                      {
                          Name = a.PostAttachmentName,
                          Url = ConvertToUrl(a.PostAttachmentUrl)
                      }).ToList();

            p.Attachments = op.Any() ? op : new();
        }



        return Task.FromResult(q);
    }

    public Task<PostDTO?> GetPostDetail(int postId)
    {
        var q = (from a in dc.Posts
                 where a.PostId == postId
                 select new PostDTO
                 {
                     postName = a.PostName,
                     postContentText = a.PostContentText,
                     postContentHTML = a.PostContentHtml,
                     postPublishDate = a.PostPublishDate,
                     postHeadImagePath = ConvertToUrl(a.PostHeadImagePath),
                     JobDepartment = a.JobDepartment.Name,
                     JobCity = a.JobCity.Name,
                     JobType = a.JobType.Name,
                     JobLastDateToApply = a.JobLastDateToApply,
                     //postThumbnailPath =
                     //    (a.PostThumbnailImagePath ?? "").Replace(@"E:\OfficeProjects\PakSuzuki\PakSuzuki\wwwroot\",
                     //        "/").Replace(@"\","/")
                     postThumbnailPath = ConvertToUrl(a.PostThumbnailImagePath),
                     PostWidth = a.PostWidth ?? "",
                     PostHeight = a.PostHeight ?? "",
                     PostExternalLink = a.PostExternalLink ?? "",
                 }).FirstOrDefault();

        if (q != null)
        {
            q.Tags = (from b in dc.PostSearchTags
                      where b.PostId == postId
                      orderby b.PostSearchTagName
                      select b.PostSearchTagName).ToList();


            q.Attachments = (from m in dc.PostAttachments
                             where m.PostId == postId
                             orderby m.PostAttachmentName
                             select new AttachmentDto
                             {
                                 Name = m.PostAttachmentName,
                                 Url = m.PostAttachmentUrl
                             }).ToList();

            foreach (var aa in q.Attachments)
                if (!string.IsNullOrEmpty(aa.Url))
                    aa.Url = ConvertToUrl(aa.Url);
        }


        return Task.FromResult(q);
    }

    public Task<CalenderDto> GetCalender()
    {
        var op = new CalenderDto() { Attachment = "", Thumbnail = "" };
        var q = (from a in dc.Posts
                 orderby (a.PostModifiedDate ?? a.PostCreatedDate) descending
                 where a.PostIsActive == "ACTIVE" &&
                       a.PostIsDel == "NO" &&
                       DateTime.Now >= a.PostPublishDate && DateTime.Now <= a.PostExpiryDate &&
                       //a.PostPublishDate<a.PostExpiryDate &&
                       a.PostSectionId == (int)Section.Calender
                 select a).FirstOrDefault();

        if (q != null)
        {
            var at = (from b in dc.PostAttachments
                      where b.PostId == q.PostId
                      select b).FirstOrDefault();

            if (at != null)
            {
                op = new CalenderDto()
                {
                    Thumbnail = ConvertToUrl(q.PostThumbnailImagePath),
                    Attachment = ConvertToUrl(at.PostAttachmentUrl)

                };

            }
        }
        return Task.FromResult(op);

    }

    public Task<CalenderDto> GetOrganogram()
    {
        var op = new CalenderDto() { Attachment = "", Thumbnail = "" };
        var q = (from a in dc.Posts
                 where a.PostIsActive == "ACTIVE" &&
                       a.PostIsDel == "NO" &&
                       DateTime.Now >= a.PostPublishDate && DateTime.Now <= a.PostExpiryDate &&
                       a.PostSectionId == (int)Section.Organogram
                 select a).FirstOrDefault();

        if (q != null)
        {
            var at = (from b in dc.PostAttachments
                      where b.PostId == q.PostId
                      select b).FirstOrDefault();

            if (at != null)
            {
                op = new CalenderDto()
                {
                    Thumbnail = ConvertToUrl(q.PostThumbnailImagePath),
                    Attachment = ConvertToUrl(at.PostAttachmentUrl)

                };

            }
        }
        return Task.FromResult(op);
    }
    public Task<string> GetCategoryTitle(int categoryId)
    {
        var q = dc.Categories.FirstOrDefault(m => m.CategoryId == categoryId);
        if (q == null)
        {
            return Task.FromResult("");
        }
        else
        {
            return Task.FromResult(" - " + q.CategoryTitle);
        }
    }



    public Task<List<CanteenMenuDto>> GetCanteenMenu(DateTime dateFrom, DateTime dateTo)
    {
        var q = (from a in dc.CanteenMenus
                 where a.Date >= dateFrom && a.Date <= dateTo
                 select new CanteenMenuDto
                 {
                     Id = a.Id,
                     Category = a.Category,
                     Date = a.Date,
                     MainDish = a.MainDish,
                     Code = a.Code,
                     Day = a.Day,
                     Fruit = a.Fruit,
                     Salad = a.Salad,
                     SideDish = a.SideDish,
                     Sweet = a.Sweet
                 }).ToList();
        return Task.FromResult(q);
    }

    public Task<string> DeleteCanteenMenuItem(int id)
    {
        var q = dc.CanteenMenus.FirstOrDefault(m => m.Id == id);
        if (q != null)
        {
            dc.CanteenMenus.Remove(q);
            dc.SaveChanges();
        }
        return Task.FromResult("OK");
    }

    // Get Todays Canteen Menu List
    public Task<List<CanteenMenuDto>> GetTodaysMenu(DateTime date)
    {
        var q = (from a in dc.CanteenMenus
                 where a.Date.Year == date.Year && a.Date.Month == date.Month && a.Date.Day == date.Day
                 select new CanteenMenuDto
                 {
                     Id = a.Id,
                     Category = a.Category,
                     Date = a.Date,
                     MainDish = a.MainDish,
                     Sweet = a.Sweet,
                     Salad = a.Salad,
                     SideDish = a.SideDish,
                     Fruit = a.Fruit,
                     Code = a.Code,
                     Day = a.Day
                 }).ToList();
        return Task.FromResult(q);
    }

    public Task<List<CanteenMenuDto>> GetThisMonthMenu()
    {
        var q = (from a in dc.CanteenMenus
                 orderby a.Date, a.Category
                 where a.Date.Year == DateTime.Now.Year && a.Date.Month == DateTime.Now.Month
                 select new CanteenMenuDto
                 {
                     Id = a.Id,
                     Category = a.Category,
                     Date = a.Date,
                     MainDish = a.MainDish,
                     Day = a.Day,
                     Code = a.Code,
                     Fruit = a.Fruit,
                     Sweet = a.Sweet,
                     SideDish = a.SideDish,
                     Salad = a.Salad
                 }).ToList();
        return Task.FromResult(q);
    }


    // Get Users Section based on User Role
    public Task<List<SectionDto>> GetUserSections(string user)
    {
        var q = (from a in dc.UserRoles
                 join b in dc.SectionRoles on a.RoleId equals b.RoleId
                 join s in dc.Sections on b.SectionId equals s.SectionId
                 where a.User.UserId == user
                 select new SectionDto
                 {
                     Id = b.SectionId ?? 0,
                     Name = s.SectionTitle.ToLower()
                 }).Distinct().ToList();
        return Task.FromResult(q);
    }

    public Task<string> PopulateApplicationMenus(string excelFilePath, string user)
    {
        int idx = 1;
        var rawMenu = new List<RawMenuDto>();
        try
        {
            using (var package = new ExcelPackage(new FileInfo(excelFilePath)))
            {
                var worksheet = package.Workbook.Worksheets[0];
                var rowCount = worksheet.Dimension.Rows;
                for (var row = 2; row <= rowCount; row++)
                {
                    var mi = new RawMenuDto();
                    mi.Id = idx;
                    mi.Menu = worksheet.Cells[row, 2].Value.ToString() ?? "";
                    mi.SubMenu = (worksheet.Cells[row, 3].Value ?? "").ToString() ?? "";
                    mi.SubMenu02 = (worksheet.Cells[row, 4].Value ?? "").ToString() ?? "";
                    mi.SubMenu03 = (worksheet.Cells[row, 5].Value ?? "").ToString() ?? "";
                    mi.Link = (worksheet.Cells[row, 6].Value ?? "").ToString() ?? "";
                    // sort order from first column which is an integer value
                    mi.SortOrder = Convert.ToInt32(worksheet.Cells[row, 1].Value.ToString());

                    rawMenu.Add(mi);
                    idx++;
                }
            }
        }
        catch (Exception)
        {

        }

        // get all distinct "Menu" from rawMenu 


        // insert into dc.Menus with following attributes, ModuleId=2, name=Menu, Device="web", IsActive=true, MenuParentId=2, CreatedDate=DateTime.Now, CreatedBy=user, SortOrder=apply sort as per menu list

        var menuList = new List<Menu>();

        // get all distinct "Menu" from rawMenu
        var menus = rawMenu.Select(m => m.Menu).Distinct().ToList();

        // delete all menus which are created 1-Aug-2024 and onwards
        var q = dc.Menus.Where(m => m.CreatedDate >= new DateTime(2024, 8, 1));
        dc.Menus.RemoveRange(q);
        dc.SaveChanges();


        foreach (var m in menus)
        {
            var obj = new Menu { ModuleId = 2, Name = m, Device = "web", IsActive = true, MenuParentId = 2, CreatedDate = DateTime.Now, CreatedBy = 11, SortOrder = menus.IndexOf(m) + 1 };
            dc.Menus.Add(obj);
            dc.SaveChanges();

            // now get those menu items who has link at subMenu level and sub menu 02 and sub menu 03 not defined
            var sm1 = (from m1 in rawMenu
                       where m1.Menu == m &&
                       m1.Link != "" && m1.SubMenu02 == "" && m1.SubMenu03 == ""
                       select new { m1.SubMenu, m1.Link, m1.SortOrder }).ToList();
            foreach (var mi1 in sm1)
            {
                var obj1 = new Menu
                {
                    ModuleId = 2,
                    Name = mi1.SubMenu,
                    MenuParentId = obj.Id,
                    CreatedDate = DateTime.Now,
                    CreatedBy = 11,
                    Device = "web",
                    IsActive = true,
                    SortOrder = mi1.SortOrder,
                    Url = mi1.Link,
                    Urltarget = "iframe"
                };
                dc.Menus.Add(obj);
                dc.SaveChanges();
            }
        }


        return Task.FromResult("OK");
    }

    public Task<string> UploadMenuByExcelFile(string excelFilePath, string user)
    {
        int idx = 0;
        // using epplus read excel file data into List<CanteenMenuDto>
        var menu = new List<CanteenMenuDto>();
        try
        {
            using (var package = new ExcelPackage(new FileInfo(excelFilePath)))
            {
                var worksheet = package.Workbook.Worksheets[0];
                var rowCount = worksheet.Dimension.Rows;
                for (var row = 2; row <= rowCount; row++)
                {
                    if (row == 7)
                    {
                        var msg2 = "";
                    }
                    idx = row;
                    var cm = new CanteenMenuDto();

                    cm.Day = worksheet.Cells[row, 1].Value.ToString() ?? "";
                    try
                    {
                        cm.Date = DateTime.Parse((worksheet.Cells[row, 3].Value ?? "").ToString() ?? "");

                    }
                    catch (Exception)
                    {

                        break;
                    }
                    cm.Code = worksheet.Cells[row, 2].Value.ToString() ?? ""; //Convert.ToInt32(worksheet.Cells[row, 2].Value.ToString());
                    cm.Category = worksheet.Cells[row, 4].Value.ToString() ?? "";
                    cm.MainDish = worksheet.Cells[row, 5].Value.ToString() ?? "";
                    cm.SideDish = worksheet.Cells[row, 6].Value == null ? "" : worksheet.Cells[row, 6].Value.ToString() ?? "";
                    cm.Sweet = worksheet.Cells[row, 7].Value == null ? "" : worksheet.Cells[row, 7].Value.ToString() ?? "";
                    cm.Fruit = worksheet.Cells[row, 8].Value == null ? "" : worksheet.Cells[row, 8].Value.ToString() ?? "";
                    cm.Salad = worksheet.Cells[row, 9].Value == null ? "" : worksheet.Cells[row, 9].Value.ToString() ?? "";





                    menu.Add(cm);
                }
            }
        }
        catch (Exception ex2)
        {
            var msg = ex2.Message;
            // copy all internal exceptions messages
            if (ex2.InnerException != null)
            {
                msg += " Detail: " + ex2.InnerException.Message;
            }
            return Task.FromResult(msg);
        }


        var q = dc.CanteenMenus.Where(m => m.Date.Year == menu[0].Date.Year &&
       m.Date.Month == menu[0].Date.Month);
        dc.CanteenMenus.RemoveRange(q);
        dc.SaveChanges();
        foreach (var cm in menu)
        {
            var obj = new CanteenMenu { Category = cm.Category, Date = cm.Date, MainDish = cm.MainDish, UploadBy = user, UploadDate = DateTime.Now, Code = cm.Code, Day = cm.Day, Fruit = cm.Fruit, Salad = cm.Salad, SideDish = cm.SideDish, Sweet = cm.Sweet }; ;
            dc.CanteenMenus.Add(obj);
            dc.SaveChanges();
        }
        return Task.FromResult("OK");
    }

    public Task<string> GetUserFullName(string userId)
    {
        var q = dc.Users.FirstOrDefault(m => m.UserId == userId);
        if (q == null)
        {
            return Task.FromResult(userId);
        }
        else
        {
            return Task.FromResult(q.Name);
        }
    }

    public Task<string> GetQuickAnnouncementUrl()
    {
        var q = (from a in dc.QuickAnnouncements
                 where a.IsActive
                 select a).FirstOrDefault();
        return Task.FromResult(q == null ? "" : q.Url);
    }
    public Task<string> GetBIDashboardURL()
    {
        var q = (from a in dc.BidashboardLinks
                 where a.IsActive
                 select a).FirstOrDefault();
        return Task.FromResult(q == null ? "" : q.Url);
    }


    public Task<List<SubCategoryDto>> SetAllSubCategories(int categoryId)
    {
        var q = (from a in dc.SubCategories
                 orderby a.Title
                 where a.CategoryId == categoryId
                 select new SubCategoryDto()
                 {
                     Id = a.Id,
                     Title = a.Title
                 }).ToList();
        return Task.FromResult(q);
    }

    public Task<(string missionDetailImg, string missionThumbImg)> GetMissionImages(string user)
    {
        var userRoles = dc.UserRoles.Where(x => x.User.UserId == user).Select(x => x.RoleId).ToList();

        var q = (from a in dc.Posts
                 where a.PostSectionId == (int)Section.mission
                       && DateTime.Now >= a.PostPublishDate
                       && DateTime.Today < a.PostExpiryDate
                       && a.PostIsActive == "ACTIVE"
                       && a.PostIsDel == "NO"
                       && dc.PostRoles.Any(x => x.PostId == a.PostId && userRoles.Contains(x.RoleId))

                 orderby (a.PostModifiedDate ?? a.PostCreatedDate) descending
                 select new PostDTO
                 {
                     postId = a.PostId,
                     postThumbnailPath = ConvertToUrl(a.PostThumbnailImagePath),
                     postHeadImagePath = ConvertToUrl(a.PostHeadImagePath)
                 }).FirstOrDefault();
        return Task.FromResult(q == null ? ("", "") : (q.postHeadImagePath, q.postThumbnailPath));
    }

    public Task<List<SectionDto>> GetModuleSections(int moduleId)
    {
        
        var q = (from a in dc.Sections
                 orderby a.SortOrder
                 where a.ModuleId == moduleId
                 select new SectionDto
                 {
                     Id = a.SectionId,
                     Name = a.SectionTitle,
                     Description = a.SectionDescription,
                     Module = a.Module.ModuleTitle,
                     SortOrder = a.SortOrder,
                     PostTypeId = a.PostTypeId,
                     PostType = a.PostType.PostTypeTitle,
                     IsActive = a.IsActive==true
                 }).ToList();
        return Task.FromResult(q);
    }

    public Task<List<ModuleDto>> GetModules()
    {
        var q = (from a in dc.Modules
                 where a.IsActive == true && a.Id >= 5
                 select new ModuleDto
                 {
                     Id = a.Id,
                     Name = a.ModuleTitle,
                 }).ToList();
        return Task.FromResult(q);
    }

    public Task<string> DeleteSection(int Id)
    {
        var q = dc.Sections.FirstOrDefault(m => m.SectionId == Id);
        // if section is associated with any post then return error message
        if (dc.Posts.Any(m => m.PostSectionId == Id))
        {
            return Task.FromResult("Section is associated with posts, can not be deleted");
        }
        if (q != null)
        {
            dc.Sections.Remove(q);
            dc.SaveChanges();
        }
        return Task.FromResult("OK");
    }


    public Task<string> SaveSection(SectionDto sec)
    {
        if (sec.Id == 0)
        {
            var q = new Model.Section
            {
                SectionTitle = (sec.Name ?? "").Trim(),
                SectionDescription = (sec.Description ?? "").Trim(),
                ModuleId = sec.ModuleId,
                SortOrder = sec.SortOrder,
                IsActive = sec.IsActive,
                PostTypeId = sec.PostTypeId
            };
            dc.Sections.Add(q);
            dc.SaveChanges();
        }
        else
        {
            var q = dc.Sections.FirstOrDefault(m => m.SectionId == sec.Id);
            if (q != null)
            {
                q.SectionTitle = (sec.Name??"").Trim();
                q.SectionDescription = (sec.Description??"").Trim();
                q.ModuleId = sec.ModuleId;
                q.SortOrder = sec.SortOrder;
                q.IsActive = sec.IsActive;
                q.PostTypeId = sec.PostTypeId;
                dc.SaveChanges();
            }
        }
        return Task.FromResult("OK");
    }

    public Task<SectionDto?> GetSectionById(int Id)
    {
        var q = (from a in dc.Sections
                 where a.SectionId == Id
                 select new SectionDto
                 {
                     Id = a.SectionId,
                     Name = a.SectionTitle,
                     Description = a.SectionDescription,
                     Module = a.Module.ModuleTitle,
                     SortOrder = a.SortOrder,
                     PostType = a.PostType.PostTypeTitle,
                     IsActive = a.IsActive==true,
                     PostTypeId = a.PostTypeId,
                     ModuleId = a.ModuleId
                 }).FirstOrDefault();
        return Task.FromResult(q);
    }

    public Task<List<PostTypeDto>> GetPostTypes()
    {
        var q = (from a in dc.PostTypes
                 select new PostTypeDto
                 {
                     Id = a.PostTypeId,
                     Name = a.PostTypeTitle
                 }).ToList();
        return Task.FromResult(q);
    }
}