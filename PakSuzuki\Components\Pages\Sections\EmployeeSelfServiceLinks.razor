﻿@page "/setup/selfservicelinks"
@using PakSuzuki.Services
@using PakSuzuki.DTO
@using PakSuzuki.Model
@inject SfDialogService DialogService
@inject EmpSelfServiceDataService service
@inject AdminDataService adminService
@* injec javascript *@
@inject IJSRuntime js
@rendermode InteractiveServer


<SfToast @ref="@toastObj"></SfToast>
<SfDialog @ref="@dlgForm" Visible="false" Width="700px" ShowCloseIcon="true" CloseOnEscape="true" IsModal="true" MinHeight="auto">
    <DialogTemplates>
        <Header>Link Detail</Header>
        <Content>
            <EditForm Model="@link" OnValidSubmit="SaveLink">
                <DataAnnotationsValidator />
                <ValidationSummary />
                <div class="form-group">
                    
                    <SfTextBox Placeholder="Title" FloatLabelType="FloatLabelType.Always" @bind-Value="@link.Text"></SfTextBox>
                </div>
                <div class="form-group">
                    
                    <SfTextBox Placeholder="Link" FloatLabelType="FloatLabelType.Always" @bind-Value="@link.Link"></SfTextBox>
                </div>
                <div class="form-group">
                    
                    <SfDropDownList AllowFiltering="true" FilterType="Syncfusion.Blazor.DropDowns.FilterType.Contains" FloatLabelType="FloatLabelType.Always"
                                    Placeholder="Icon" DataSource="@MenuIconsList" @bind-Value="link.Icon">
                        <DropDownListEvents TValue="string" TItem="string" ValueChange="OnIconChange"></DropDownListEvents>
                    </SfDropDownList>
                    
                    @if (!string.IsNullOrEmpty(IconUrl))
                    {
    
                        <MudIcon Class="mb-1" Icon="@IconUrl" Size="Size.Large"></MudIcon>
                    }
                </div>
                <div class="form-group mb-2 mt-2">
                    
                    <label for="isActive">Status</label>
                    <SfCheckBox @bind-Checked="@link.IsActive"></SfCheckBox> <span>@link.Status</span>
                    
                </div>
                <div class="form-group">
                    <SfButton ButtonType="ButtonType.Submit" CssClass="e-primary">Save</SfButton>
                    <SfButton ButtonType="ButtonType.Button" CssClass="e-secondary" @onclick="Cancel">Cancel</SfButton>
                </div>
            </EditForm>

        </Content>
    </DialogTemplates>
</SfDialog>
<div class="row mb-2">
    <div class="col-md">
        <MudText Typo="Typo.h5">Employee Self Service Menus</MudText>
    </div>
</div>
<div class="row mb-2">
    <div class="col-md">
        <MudButton Size="Size.Small" Color="Color.Primary" StartIcon="@Icons.Material.Filled.Add" Variant="Variant.Filled" OnClick="OpenCreateForm">Add</MudButton>
        <MudButton Size="Size.Small" Color="Color.Success" StartIcon="@Icons.Material.Filled.Edit" Variant="Variant.Filled" OnClick="OpenEditForm">Edit</MudButton>
        <MudButton Size="Size.Small" Color="Color.Warning" StartIcon="@Icons.Material.Filled.Delete" Variant="Variant.Filled" OnClick="ConfirmDelete">Delete</MudButton>
    </div>
</div>
<div class="row mb-2">
    <div class="col">
        @if (links.Any())
        {
            
            
            <SfGrid @ref="@dgMain" Height="calc(100vh -220px)" DataSource="links" AllowSelection="true" AllowFiltering="true" AllowSorting="true" Width="100%">
                <GridFilterSettings Type="Syncfusion.Blazor.Grids.FilterType.Excel"></GridFilterSettings>
                <GridColumns>
                    <GridColumn Field="@nameof(SelfServiceLinkDto.Text)" HeaderText="Text" AutoFit="true"></GridColumn>
                    <GridColumn Field="@nameof(SelfServiceLinkDto.Link)" HeaderText="Link" AutoFit="true"></GridColumn>
                    <GridColumn Field="@nameof(SelfServiceLinkDto.Icon)" HeaderText="Icon" AutoFit="true"></GridColumn>
                    <GridColumn Field="@nameof(SelfServiceLinkDto.Status)" HeaderText="Status" AutoFit="true"></GridColumn>

                </GridColumns>
            </SfGrid>

        }
        else
        {
            <p>No Links are defined</p>
        }
    </div>
</div>
@code
{
    private SfToast toastObj;
    private SfDialog dlgForm;
    private List<SelfServiceLinkDto> links;
    private SelfServiceLinkDto link = new SelfServiceLinkDto();
    public SfGrid<SelfServiceLinkDto> dgMain { get; set; }
    private List<string> MenuIconsList = new List<string>();
    protected override async Task OnInitializedAsync()
    {
        links = await service.GetAllLinks();
        MenuIconsList = await adminService.GetIconsList();
    }

    private async Task Cancel()
    {
        await dlgForm.HideAsync();
    }

    private async Task OpenCreateForm(MouseEventArgs obj)
    {
        link=new SelfServiceLinkDto
        {
            IsActive = true
        };
        await dlgForm.ShowAsync();
    }

    private async Task SaveLink()
    {
        var res = await service.SaveLink(link, "jawaid");
        if (res>0)
        {
            links = await service.GetAllLinks();
            await dlgForm.HideAsync();
        }
    }

    private async Task OpenEditForm(MouseEventArgs obj)
    {

        try
        {
            var  selectedRec = await dgMain!.GetSelectedRecordsAsync();
            if (selectedRec is { Count: > 0 })
            {
                int id = selectedRec[0].Id;
                link = await service.GetLinkById(id);
                await dlgForm.ShowAsync();
            }
        }
        catch (Exception ex)
        {
            ToastModel tm = new ToastModel() { Content = ex.Message.ToString(), Title = "Error", ShowCloseButton = true, Timeout = 0 };
            await toastObj!.ShowAsync(tm);
        }
    }

    private async Task ConfirmDelete(MouseEventArgs obj)
    {
        var selectedRec = await dgMain!.GetSelectedRecordsAsync();
        // confirm by javascript

        if (selectedRec is { Count: > 0 })
        {
            var confirm = await js.InvokeAsync<bool>("confirm", "Are you sure you want to delete the selected record?");
            if (confirm)
            {
                int id = selectedRec[0].Id;
                var res1 = await service.DeleteLink(id);
                links = await service.GetAllLinks();

            }
        }
        else
        {
            // show toast message
            ToastModel tm = new ToastModel() { Content = "Please select a record to delete", Title = "Error", ShowCloseButton = true, Timeout = 0 };
            await toastObj!.ShowAsync(tm);

        }
    }
    private string IconUrl = "";
    private void OnIconChange(ChangeEventArgs<string, string> changeEventArgs)
    {
        IconUrl = IconHelper.GetIconByName(changeEventArgs.Value);
    }
}
