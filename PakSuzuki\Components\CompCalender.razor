﻿@using PakSuzuki.DTO
@inject AppDataService Service

<h3 class="sec-title">Annual Calender</h3>
<div class="cc" >
    @if (string.IsNullOrEmpty(cc.Attachment))
    {

        <img class="calender-pic" src="/media/calender/1.jpg" alt="Calender" />

    }
    else
    {
        <a href="@cc.Attachment" target="cc1">
            @if (string.IsNullOrEmpty(cc.Thumbnail))
            {
                <img class="calender-pic" src="/media/calender/1.jpg" alt="Calender"/>
            }
            else
            {
                <img class="calender-pic" src="@cc.Thumbnail" alt="Calender"/>

            }

        </a>
    }
</div>

@code {
    private CalenderDto cc = new CalenderDto();
    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();


         cc = await Service.GetCalender();




    }

}
