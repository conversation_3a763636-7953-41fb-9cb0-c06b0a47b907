﻿using System.ComponentModel.DataAnnotations;

namespace PakSuzuki.DTO
{
    public class SelfServiceLinkDto
    {
        public int Id { get; set; }

        [Required(ErrorMessage = "Service Location Text is required.")]
        [MaxLength(300, ErrorMessage = "Service Location Text cannot exceed 300 characters.")]
        public string Text { get; set; }
        [Required(ErrorMessage = "Service Location Link is required.")]
        [MaxLength(300, ErrorMessage = "Service Location Link cannot exceed 300 characters.")]
        public string Link { get; set; }

        public string Icon { get; set; }

        public bool IsActive { get; set; }
        public string Status => IsActive ? "Active" : "Inactive";
    }
}
