@inject AuthenticationStateProvider AuthenticationStateProvider
@inject PollDataService service

<!-- SurveyCard.razor -->
<div class="survey-card">
    <div class="header">
        <div class="logo">@Survey.Department.ToUpper().Substring(0, 2)</div>
        <div class="title">@Survey.Department</div>
    </div>
    <div class="question">@Survey.Question</div>

    @if (Survey.IsOpen)
    {
        var name = $"sn{Survey.Id}";
        <div class="options">
            @*<SfRadioButton Label="Option 1" Name="options" Value="card" @bind-Checked="stringChecked"></SfRadioButton>
        <SfRadioButton Label="Option 2" Name="options" Value="cash" @bind-Checked="stringChecked"></SfRadioButton>*@

            @foreach (var option in Survey.Options)
            {
                var val = $"{option.Id}";
                @*<SfRadioButton Label="@option.Label" Value="val" Name="@name" TChecked="int"
            ValueChange="() => SaveSurvey(option.Id)"></SfRadioButton>*@

                <SfRadioButton Label="@option.Label" Name="@name" Value="@val" @bind-Checked="stringChecked" TChecked="int?"
                               ValueChange="() => SaveSurvey(option.Id)">
                </SfRadioButton>
                <br/>

                @* <div>@option.Label</div> *@
            }
        </div>
        <div class="deadline">@Survey.Deadline.Value.ToString("dd-MM-yyyy") - @RemainingDays() Days Left</div>
    }
    else
    {
        <div class="options">
            @{
                foreach (var option in Survey.Options)
                {
                    <CompProgressBar Label="@option.Label" Value="@option.ResponseCount" Total="@total"/>
                }
            }
        </div>
        <div class="poll-status">Poll Closed. - Total Votes: @total</div>
    }
</div>

@code {

    private int? stringChecked = null;


    [Parameter] public PollQuestionDto Survey { get; set; }
    private int total = 0;

    private string RemainingDays()
    {
        var daysRemaining = (Survey.Deadline!.Value - DateTime.Now).Days;
        return daysRemaining > 0 ? daysRemaining.ToString() : "0";
    }

    // OnInitializedAsync is called when the component is initialized
    protected override async Task OnInitializedAsync()
    {
        //Survey.Options = await service.GetPollsOptions(Survey.Id);
        total = Survey.Options.Sum(m => m.ResponseCount);
    }

    private async Task SaveSurvey(int optionId)
    {
        var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        var user = authState.User;
        if (user.Identity is { Name: not null })
        {
            var pollId = Survey.Id;
            var res = await service.SavePoll(user.Identity.Name!, pollId, optionId);

            if (res == "OK")
            {
                Survey.IsOpen = false;
                //var allPolls = await service.GetUsersPolls(user.Identity.Name);
                //Survey = allPolls.First(m => m.Id == pollId);
                Survey.IsSubmitted = true;
                Survey.Options = await service.GetPollsOptions(pollId);
                total = Survey.Options.Sum(m => m.ResponseCount);
                StateHasChanged();
            }
        }
    }

}