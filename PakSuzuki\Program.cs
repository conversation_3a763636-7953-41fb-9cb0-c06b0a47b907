using PakSuzuki.Components;
using MudBlazor.Services;
using Smart.Blazor;
using Microsoft.AspNetCore.Authentication.Negotiate;
using OfficeOpenXml;

var builder = WebApplication.CreateBuilder(args);

// configure non commercial license of epplus
ExcelPackage.LicenseContext = LicenseContext.NonCommercial;

builder.Services.AddAuthentication(NegotiateDefaults.AuthenticationScheme)
    .AddNegotiate();

builder.Services.AddAuthorization(options =>
{
    options.FallbackPolicy = options.DefaultPolicy;
});

// Add services to the container.
builder.Services.AddRazorComponents()
    .AddInteractiveServerComponents();

builder.Services.AddMudServices();
Syncfusion.Licensing.SyncfusionLicenseProvider.RegisterLicense("MzIzODczMkAzMjM1MmUzMDJlMzBQVFVDcXQ3dW8yQnU3YlArTk5SZU1GVGFxTlRpUXU5WlZubXFtckUvMkx3PQ==");

//SyncfusionLicenseProvider.RegisterLicense("MzA5ODk4N0AzMjM0MmUzMDJlMzBUbzFqdGRDWGF0VXZqQnh4cjA5NDNSSzBhNnBBbmtvbFZBNlk1bjYrT2lrPQ==");

var connectionString = builder.Configuration.GetConnectionString("DefaultConnection");
builder.Services.AddDbContext<ApplicationDbContext>(options => options.UseSqlServer(connectionString));

builder.Services.AddServerSideBlazor().AddCircuitOptions(options => { options.DetailedErrors = true; });

builder.Services.AddScoped<SfDialogService>();
builder.Services.AddScoped<AdminDataService>();
builder.Services.AddScoped<AppDataService>();
builder.Services.AddScoped<PostDataService>();
builder.Services.AddScoped<PollDataService>();
builder.Services.AddScoped<CorporateService>();
builder.Services.AddScoped<LocationDataService>();
builder.Services.AddScoped<EmpSelfServiceDataService>();
builder.Services.AddScoped<MeetingRoomService>();
builder.Services.AddScoped<TransformationDataService>();
builder.Services.AddScoped<PageLogService>();

//builder.Services.AddScoped<EmployeeDataService>();

builder.Services.AddRazorPages();
builder.Services.AddServerSideBlazor();
builder.Services.AddSyncfusionBlazor();
builder.Services.AddSmart();

var app = builder.Build();

// Configure the HTTP request pipeline.
if (!app.Environment.IsDevelopment())
{
    app.UseExceptionHandler("/Error", createScopeForErrors: true);
    // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
    app.UseHsts();
}

app.UseHttpsRedirection();

app.UseStaticFiles();
app.UseAntiforgery();

app.UseAuthentication();
app.UseAuthorization();

app.MapRazorComponents<App>()
    .AddInteractiveServerRenderMode();

app.Run();
