﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using Microsoft.EntityFrameworkCore;

namespace PakSuzuki.Model;

public partial class ApplicationDbContext : DbContext
{
    public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options)
        : base(options)
    {
    }

    public virtual DbSet<BidashboardLink> BidashboardLinks { get; set; }

    public virtual DbSet<CanteenMenu> CanteenMenus { get; set; }

    public virtual DbSet<Category> Categories { get; set; }

    public virtual DbSet<Department> Departments { get; set; }

    public virtual DbSet<Designation> Designations { get; set; }

    public virtual DbSet<Division> Divisions { get; set; }

    public virtual DbSet<EmpLocationTiming> EmpLocationTimings { get; set; }

    public virtual DbSet<EmployeeSelfServiceLink> EmployeeSelfServiceLinks { get; set; }

    public virtual DbSet<JobCity> JobCities { get; set; }

    public virtual DbSet<JobDepartment> JobDepartments { get; set; }

    public virtual DbSet<JobType> JobTypes { get; set; }

    public virtual DbSet<Location> Locations { get; set; }

    public virtual DbSet<MeetingRoom> MeetingRooms { get; set; }

    public virtual DbSet<MeetingRoomApprovar> MeetingRoomApprovars { get; set; }

    public virtual DbSet<MeetingRoomRequest> MeetingRoomRequests { get; set; }

    public virtual DbSet<MeetingRoomStatus> MeetingRoomStatuses { get; set; }

    public virtual DbSet<Menu> Menus { get; set; }

    public virtual DbSet<Module> Modules { get; set; }

    public virtual DbSet<OfficeLocation> OfficeLocations { get; set; }

    public virtual DbSet<PageAccessLog> PageAccessLogs { get; set; }

    public virtual DbSet<Post> Posts { get; set; }

    public virtual DbSet<PostAttachment> PostAttachments { get; set; }

    public virtual DbSet<PostAttachmentTag> PostAttachmentTags { get; set; }

    public virtual DbSet<PostComment> PostComments { get; set; }

    public virtual DbSet<PostEmotion> PostEmotions { get; set; }

    public virtual DbSet<PostPoll> PostPolls { get; set; }

    public virtual DbSet<PostPollResult> PostPollResults { get; set; }

    public virtual DbSet<PostRole> PostRoles { get; set; }

    public virtual DbSet<PostSearchTag> PostSearchTags { get; set; }

    public virtual DbSet<PostType> PostTypes { get; set; }

    public virtual DbSet<PostVacancy> PostVacancies { get; set; }

    public virtual DbSet<QuickAnnouncement> QuickAnnouncements { get; set; }

    public virtual DbSet<Role> Roles { get; set; }

    public virtual DbSet<RoleMenu> RoleMenus { get; set; }

    public virtual DbSet<Section> Sections { get; set; }

    public virtual DbSet<SectionRole> SectionRoles { get; set; }

    public virtual DbSet<ShiftLocation> ShiftLocations { get; set; }

    public virtual DbSet<Station> Stations { get; set; }

    public virtual DbSet<SubCategory> SubCategories { get; set; }

    public virtual DbSet<User> Users { get; set; }

    public virtual DbSet<UserQuickLink> UserQuickLinks { get; set; }

    public virtual DbSet<UserRole> UserRoles { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<BidashboardLink>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__BIDashbo__3214EC07FBCBFEDB");

            entity.ToTable("BIDashboardLink");

            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.Url)
                .IsRequired()
                .HasMaxLength(1000)
                .HasColumnName("URL");
        });

        modelBuilder.Entity<CanteenMenu>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__CanteenM__3214EC07628DDAD7");

            entity.ToTable("CanteenMenu");

            entity.Property(e => e.Category)
                .IsRequired()
                .HasMaxLength(150);
            entity.Property(e => e.Code)
                .IsRequired()
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.Day)
                .IsRequired()
                .HasMaxLength(10)
                .IsUnicode(false);
            entity.Property(e => e.Fruit).HasMaxLength(300);
            entity.Property(e => e.MainDish)
                .IsRequired()
                .HasMaxLength(300);
            entity.Property(e => e.Salad).HasMaxLength(300);
            entity.Property(e => e.SideDish).HasMaxLength(300);
            entity.Property(e => e.Sweet).HasMaxLength(300);
            entity.Property(e => e.UploadBy)
                .HasMaxLength(300)
                .IsUnicode(false);
            entity.Property(e => e.UploadDate).HasDefaultValueSql("(getdate())");
        });

        modelBuilder.Entity<Category>(entity =>
        {
            entity.Property(e => e.CategoryTitle).HasMaxLength(200);
        });

        modelBuilder.Entity<Department>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK_Department");

            entity.HasIndex(e => e.Name, "UQ__Departme__737584F6E4B07092").IsUnique();

            entity.Property(e => e.Name)
                .IsRequired()
                .HasMaxLength(300);
        });

        modelBuilder.Entity<Designation>(entity =>
        {
            entity.Property(e => e.Name)
                .IsRequired()
                .HasMaxLength(300);
        });

        modelBuilder.Entity<Division>(entity =>
        {
            entity.Property(e => e.Name)
                .IsRequired()
                .HasMaxLength(250);
        });

        modelBuilder.Entity<EmpLocationTiming>(entity =>
        {
            entity.ToTable("EmpLocationTiming");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.EmpId).HasColumnName("empId");
            entity.Property(e => e.FromTime).HasColumnType("datetime");
            entity.Property(e => e.ToTime).HasColumnType("datetime");
        });

        modelBuilder.Entity<EmployeeSelfServiceLink>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__Employee__3214EC07C9AA0601");

            entity.Property(e => e.CreatedBy)
                .HasMaxLength(300)
                .IsUnicode(false);
            entity.Property(e => e.CreatedDate).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.Icon)
                .HasMaxLength(200)
                .IsUnicode(false);
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.Link)
                .IsRequired()
                .HasMaxLength(500);
            entity.Property(e => e.ModifiedBy)
                .HasMaxLength(300)
                .IsUnicode(false);
            entity.Property(e => e.Text)
                .IsRequired()
                .HasMaxLength(400);
        });

        modelBuilder.Entity<JobCity>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__JobCitie__3214EC0799F5C2D6");

            entity.HasIndex(e => e.Name, "UQ__JobCitie__737584F618F4C901").IsUnique();

            entity.Property(e => e.Name)
                .IsRequired()
                .HasMaxLength(200)
                .IsUnicode(false);
        });

        modelBuilder.Entity<JobDepartment>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__JobDepar__3214EC075EA75C41");

            entity.ToTable("JobDepartment");

            entity.HasIndex(e => e.Name, "UQ__JobDepar__737584F691C02DC1").IsUnique();

            entity.Property(e => e.Name)
                .IsRequired()
                .HasMaxLength(200)
                .IsUnicode(false);
        });

        modelBuilder.Entity<JobType>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__JobTypes__3214EC071BD82CD5");

            entity.HasIndex(e => e.Name, "UQ__JobTypes__737584F6145FA0F8").IsUnique();

            entity.Property(e => e.Name)
                .IsRequired()
                .HasMaxLength(200)
                .IsUnicode(false);
        });

        modelBuilder.Entity<Location>(entity =>
        {
            entity.Property(e => e.EmployeeShiftCode)
                .HasMaxLength(255)
                .HasColumnName("Employee_Shift_Code");
            entity.Property(e => e.LocationCode)
                .HasMaxLength(255)
                .HasColumnName("Location_Code");
            entity.Property(e => e.LocationTxt)
                .HasMaxLength(255)
                .HasColumnName("Location_TXT");
            entity.Property(e => e.ShiftEnd).HasColumnName("Shift_End");
            entity.Property(e => e.ShiftStart).HasColumnName("Shift_Start");
        });

        modelBuilder.Entity<MeetingRoom>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK_MeetingRooms_Id");

            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.MeetingRoomName)
                .IsRequired()
                .HasMaxLength(200)
                .IsUnicode(false);

            entity.HasOne(d => d.OfficeLocation).WithMany(p => p.MeetingRooms)
                .HasForeignKey(d => d.OfficeLocationId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__MeetingRo__Offic__37703C52");
        });

        modelBuilder.Entity<MeetingRoomApprovar>(entity =>
        {
            entity.HasKey(e => new { e.UserId, e.MeetingRoomId }).HasName("PK__MeetingR__B0C29656EA295CCA");

            entity.Property(e => e.UserId)
                .HasMaxLength(100)
                .IsUnicode(false);
            entity.Property(e => e.CreateDate).HasDefaultValueSql("(getdate())");
        });

        modelBuilder.Entity<MeetingRoomRequest>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK_MeetingRoomBookingRequests_Id");

            entity.Property(e => e.AdminUserId)
                .HasMaxLength(300)
                .IsUnicode(false);
            entity.Property(e => e.ApproveRejectDate).HasColumnType("datetime");
            entity.Property(e => e.CreatedDate).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.MeetingRoomStatusId).HasDefaultValue((byte)1);
            entity.Property(e => e.Notes).IsUnicode(false);
            entity.Property(e => e.Reason)
                .HasMaxLength(1000)
                .IsUnicode(false);
            entity.Property(e => e.UserId)
                .IsRequired()
                .HasMaxLength(300)
                .IsUnicode(false);

            entity.HasOne(d => d.MeetingRoom).WithMany(p => p.MeetingRoomRequests)
                .HasForeignKey(d => d.MeetingRoomId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__MeetingRo__Meeti__41EDCAC5");

            entity.HasOne(d => d.MeetingRoomStatus).WithMany(p => p.MeetingRoomRequests)
                .HasForeignKey(d => d.MeetingRoomStatusId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__MeetingRo__Meeti__42E1EEFE");
        });

        modelBuilder.Entity<MeetingRoomStatus>(entity =>
        {
            entity.HasKey(e => e.StatusId).HasName("PK_MeetingRoomStatuses_StatusId");

            entity.Property(e => e.Name)
                .IsRequired()
                .HasMaxLength(50)
                .IsUnicode(false);
        });

        modelBuilder.Entity<Menu>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK_tbMenus");

            entity.Property(e => e.Code)
                .HasMaxLength(50)
                .IsUnicode(false);
            entity.Property(e => e.CreatedDate).HasColumnType("datetime");
            entity.Property(e => e.Device)
                .HasMaxLength(100)
                .IsUnicode(false);
            entity.Property(e => e.Icon).HasMaxLength(200);
            entity.Property(e => e.ModifiedDate).HasColumnType("datetime");
            entity.Property(e => e.Name)
                .IsRequired()
                .HasMaxLength(100)
                .IsUnicode(false);
            entity.Property(e => e.Url)
                .HasMaxLength(500)
                .IsUnicode(false)
                .HasColumnName("URL");
            entity.Property(e => e.Urltarget)
                .HasMaxLength(50)
                .HasColumnName("URLTarget");
        });

        modelBuilder.Entity<Module>(entity =>
        {
            entity.HasIndex(e => e.IsActive, "IX_Modules");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.ModuleTitle).HasMaxLength(300);
        });

        modelBuilder.Entity<OfficeLocation>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK_OfficeLocation_Id");

            entity.ToTable("OfficeLocation");

            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.Location)
                .IsRequired()
                .HasMaxLength(200)
                .IsUnicode(false);
        });

        modelBuilder.Entity<PageAccessLog>(entity =>
        {
            entity.HasKey(e => e.LogId).HasName("PK__PageAcce__5E5499A8F790ABEB");

            entity.Property(e => e.LogId)
                .HasDefaultValueSql("(newid())")
                .HasColumnName("LogID");
            entity.Property(e => e.AccessedDateTime)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.ActionType).HasMaxLength(50);
            entity.Property(e => e.Ipaddress)
                .HasMaxLength(45)
                .HasColumnName("IPAddress");
            entity.Property(e => e.MachineName).HasMaxLength(256);
            entity.Property(e => e.PageAccessed)
                .IsRequired()
                .HasMaxLength(512);
            entity.Property(e => e.ReferrerUrl)
                .HasMaxLength(512)
                .HasColumnName("ReferrerURL");
            entity.Property(e => e.SessionId)
                .HasMaxLength(128)
                .HasColumnName("SessionID");
            entity.Property(e => e.UserAgent).HasMaxLength(512);
            entity.Property(e => e.UserId)
                .HasMaxLength(128)
                .HasColumnName("UserID");
            entity.Property(e => e.UserName).HasMaxLength(256);
        });

        modelBuilder.Entity<Post>(entity =>
        {
            entity.Property(e => e.PostId).HasColumnName("postID");
            entity.Property(e => e.Column1)
                .HasMaxLength(50)
                .IsUnicode(false);
            entity.Property(e => e.JobDepartmentName)
                .HasMaxLength(100)
                .IsUnicode(false);
            entity.Property(e => e.JobDivisionName)
                .HasMaxLength(100)
                .IsUnicode(false);
            entity.Property(e => e.JobLastDateToApply).HasColumnType("datetime");
            entity.Property(e => e.PostApprovalStatus)
                .HasMaxLength(100)
                .IsUnicode(false)
                .HasColumnName("postApprovalStatus");
            entity.Property(e => e.PostApprovalStatusDate)
                .HasColumnType("datetime")
                .HasColumnName("postApprovalStatusDate");
            entity.Property(e => e.PostApprovedStatusBy).HasColumnName("postApprovedStatusBy");
            entity.Property(e => e.PostCategoryId).HasColumnName("postCategoryId");
            entity.Property(e => e.PostContentHtml).HasColumnName("postContentHTML");
            entity.Property(e => e.PostContentText).HasColumnName("postContentText");
            entity.Property(e => e.PostCreatedBy).HasColumnName("postCreatedBy");
            entity.Property(e => e.PostCreatedDate)
                .HasColumnType("datetime")
                .HasColumnName("postCreatedDate");
            entity.Property(e => e.PostExpiryDate)
                .HasColumnType("datetime")
                .HasColumnName("postExpiryDate");
            entity.Property(e => e.PostExternalLink)
                .HasMaxLength(1000)
                .IsUnicode(false)
                .HasColumnName("postExternalLink");
            entity.Property(e => e.PostHeadImage)
                .HasColumnType("image")
                .HasColumnName("postHeadImage");
            entity.Property(e => e.PostHeadImagePath)
                .HasMaxLength(2000)
                .HasColumnName("postHeadImagePath");
            entity.Property(e => e.PostHeight)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("postHeight");
            entity.Property(e => e.PostIconImage)
                .HasColumnType("image")
                .HasColumnName("postIconImage");
            entity.Property(e => e.PostIconImagePath)
                .HasMaxLength(2000)
                .HasColumnName("postIconImagePath");
            entity.Property(e => e.PostIsActive)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("postIsActive");
            entity.Property(e => e.PostIsDel)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("postIsDel");
            entity.Property(e => e.PostModifiedBy).HasColumnName("postModifiedBy");
            entity.Property(e => e.PostModifiedDate)
                .HasColumnType("datetime")
                .HasColumnName("postModifiedDate");
            entity.Property(e => e.PostName)
                .HasMaxLength(1000)
                .HasColumnName("postName");
            entity.Property(e => e.PostPublishDate)
                .HasColumnType("datetime")
                .HasColumnName("postPublishDate");
            entity.Property(e => e.PostRemarks)
                .HasMaxLength(2000)
                .HasColumnName("postRemarks");
            entity.Property(e => e.PostSectionId).HasColumnName("postSectionId");
            entity.Property(e => e.PostSubCategoryId).HasColumnName("postSubCategoryId");
            entity.Property(e => e.PostThumbnailImage)
                .HasColumnType("image")
                .HasColumnName("postThumbnailImage");
            entity.Property(e => e.PostThumbnailImagePath)
                .HasMaxLength(2000)
                .HasColumnName("postThumbnailImagePath");
            entity.Property(e => e.PostWidth)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("postWidth");

            entity.HasOne(d => d.JobCity).WithMany(p => p.Posts)
                .HasForeignKey(d => d.JobCityId)
                .HasConstraintName("FK__Posts__JobCityId__43D61337");

            entity.HasOne(d => d.JobDepartment).WithMany(p => p.Posts)
                .HasForeignKey(d => d.JobDepartmentId)
                .HasConstraintName("FK__Posts__JobDepart__44CA3770");

            entity.HasOne(d => d.JobType).WithMany(p => p.Posts)
                .HasForeignKey(d => d.JobTypeId)
                .HasConstraintName("FK__Posts__JobTypeId__45BE5BA9");

            entity.HasOne(d => d.PostSubCategory).WithMany(p => p.Posts)
                .HasForeignKey(d => d.PostSubCategoryId)
                .HasConstraintName("FK__Posts__postSubCa__46B27FE2");
        });

        modelBuilder.Entity<PostAttachment>(entity =>
        {
            entity.Property(e => e.PostAttachmentId).HasColumnName("postAttachmentId");
            entity.Property(e => e.PostAttachmentCode)
                .HasMaxLength(100)
                .IsUnicode(false)
                .HasColumnName("postAttachmentCode");
            entity.Property(e => e.PostAttachmentCreatedBy).HasColumnName("postAttachmentCreatedBy");
            entity.Property(e => e.PostAttachmentCreatedDate)
                .HasColumnType("datetime")
                .HasColumnName("postAttachmentCreatedDate");
            entity.Property(e => e.PostAttachmentDocExt)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("postAttachmentDocExt");
            entity.Property(e => e.PostAttachmentDocSize)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("postAttachmentDocSize");
            entity.Property(e => e.PostAttachmentDocType)
                .HasMaxLength(100)
                .IsUnicode(false)
                .HasColumnName("postAttachmentDocType");
            entity.Property(e => e.PostAttachmentModifiedBy).HasColumnName("postAttachmentModifiedBy");
            entity.Property(e => e.PostAttachmentModifiedDate)
                .HasColumnType("datetime")
                .HasColumnName("postAttachmentModifiedDate");
            entity.Property(e => e.PostAttachmentName)
                .HasMaxLength(250)
                .HasColumnName("postAttachmentName");
            entity.Property(e => e.PostAttachmentObject).HasColumnName("postAttachmentObject");
            entity.Property(e => e.PostAttachmentUrl)
                .HasMaxLength(2000)
                .HasColumnName("postAttachmentURL");
            entity.Property(e => e.PostId).HasColumnName("postId");
        });

        modelBuilder.Entity<PostAttachmentTag>(entity =>
        {
            entity.Property(e => e.PostAttachmentTagId).HasColumnName("postAttachmentTagID");
            entity.Property(e => e.PostAttachmentId).HasColumnName("postAttachmentId");
            entity.Property(e => e.PostAttachmentTagCreatedBy).HasColumnName("postAttachmentTagCreatedBy");
            entity.Property(e => e.PostAttachmentTagCreatedDate)
                .HasColumnType("datetime")
                .HasColumnName("postAttachmentTagCreatedDate");
            entity.Property(e => e.PostAttachmentTagModifiedBy).HasColumnName("postAttachmentTagModifiedBy");
            entity.Property(e => e.PostAttachmentTagMofifiedDate)
                .HasColumnType("datetime")
                .HasColumnName("postAttachmentTagMofifiedDate");
            entity.Property(e => e.PostAttachmentTagName)
                .HasMaxLength(150)
                .IsUnicode(false)
                .HasColumnName("postAttachmentTagName");
        });

        modelBuilder.Entity<PostComment>(entity =>
        {
            entity.Property(e => e.PostCommentId).HasColumnName("postCommentID");
            entity.Property(e => e.PostCommentApprovedBy).HasColumnName("postCommentApprovedBy");
            entity.Property(e => e.PostCommentApprovedDate)
                .HasColumnType("datetime")
                .HasColumnName("postCommentApprovedDate");
            entity.Property(e => e.PostCommentCreatedBy).HasColumnName("postCommentCreatedBy");
            entity.Property(e => e.PostCommentCreatedDate)
                .HasColumnType("datetime")
                .HasColumnName("postCommentCreatedDate");
            entity.Property(e => e.PostCommentIsActive)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("postCommentIsActive");
            entity.Property(e => e.PostCommentIsDel)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("postCommentIsDel");
            entity.Property(e => e.PostCommentModifiedBy).HasColumnName("postCommentModifiedBy");
            entity.Property(e => e.PostCommentModifiedDate)
                .HasColumnType("datetime")
                .HasColumnName("postCommentModifiedDate");
            entity.Property(e => e.PostCommentRemarks)
                .HasMaxLength(500)
                .IsUnicode(false)
                .HasColumnName("postCommentRemarks");
            entity.Property(e => e.PostCommentStatus)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("postCommentStatus");
            entity.Property(e => e.PostCommentText)
                .HasMaxLength(500)
                .IsUnicode(false)
                .HasColumnName("postCommentText");
            entity.Property(e => e.PostId).HasColumnName("postId");
        });

        modelBuilder.Entity<PostEmotion>(entity =>
        {
            entity.Property(e => e.PostEmotionId).HasColumnName("postEmotionID");
            entity.Property(e => e.PostEmotionCreatedBy).HasColumnName("postEmotionCreatedBy");
            entity.Property(e => e.PostEmotionCreatedDate)
                .HasColumnType("datetime")
                .HasColumnName("postEmotionCreatedDate");
            entity.Property(e => e.PostEmotionIsActive)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("postEmotionIsActive");
            entity.Property(e => e.PostEmotionIsDel)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("postEmotionIsDel");
            entity.Property(e => e.PostEmotionModifiedBy).HasColumnName("postEmotionModifiedBy");
            entity.Property(e => e.PostEmotionModifiedDate)
                .HasColumnType("datetime")
                .HasColumnName("postEmotionModifiedDate");
            entity.Property(e => e.PostEmotionName)
                .HasMaxLength(100)
                .IsUnicode(false)
                .HasColumnName("postEmotionName");
            entity.Property(e => e.PostId).HasColumnName("postId");
        });

        modelBuilder.Entity<PostPoll>(entity =>
        {
            entity.Property(e => e.PostPollId).HasColumnName("postPollID");
            entity.Property(e => e.PostId).HasColumnName("postId");
            entity.Property(e => e.PostPollCreatedBy).HasColumnName("postPollCreatedBy");
            entity.Property(e => e.PostPollCreatedDate)
                .HasColumnType("datetime")
                .HasColumnName("postPollCreatedDate");
            entity.Property(e => e.PostPollIsActive)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("postPollIsActive");
            entity.Property(e => e.PostPollIsDel)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("postPollIsDel");
            entity.Property(e => e.PostPollModifiedBy).HasColumnName("postPollModifiedBy");
            entity.Property(e => e.PostPollModifiedDate)
                .HasColumnType("datetime")
                .HasColumnName("postPollModifiedDate");
            entity.Property(e => e.PostPollName)
                .HasMaxLength(100)
                .IsUnicode(false)
                .HasColumnName("postPollName");
        });

        modelBuilder.Entity<PostPollResult>(entity =>
        {
            entity.Property(e => e.PostPollResultId).HasColumnName("postPollResultID");
            entity.Property(e => e.PostPollId).HasColumnName("postPollId");
            entity.Property(e => e.PostPollOptionId).HasColumnName("postPollOptionId");
            entity.Property(e => e.PostPollResultCreatedBy)
                .HasMaxLength(200)
                .IsUnicode(false)
                .HasColumnName("postPollResultCreatedBy");
            entity.Property(e => e.PostPollResultCreatedDate)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime")
                .HasColumnName("postPollResultCreatedDate");
            entity.Property(e => e.PostPollResultIsActive)
                .HasDefaultValue(true)
                .HasColumnName("postPollResultIsActive");
            entity.Property(e => e.PostPollResultIsDel).HasColumnName("postPollResultIsDel");
            entity.Property(e => e.PostPollResultModifiedBy).HasColumnName("postPollResultModifiedBy");
            entity.Property(e => e.PostPollResultModifiedDate)
                .HasColumnType("datetime")
                .HasColumnName("postPollResultModifiedDate");
        });

        modelBuilder.Entity<PostSearchTag>(entity =>
        {
            entity.Property(e => e.PostSearchTagId).HasColumnName("postSearchTagID");
            entity.Property(e => e.PostId).HasColumnName("postId");
            entity.Property(e => e.PostSearchTagCreatedBy).HasColumnName("postSearchTagCreatedBy");
            entity.Property(e => e.PostSearchTagCreatedDate)
                .HasColumnType("datetime")
                .HasColumnName("postSearchTagCreatedDate");
            entity.Property(e => e.PostSearchTagModifiedBy).HasColumnName("postSearchTagModifiedBy");
            entity.Property(e => e.PostSearchTagMofifiedDate)
                .HasColumnType("datetime")
                .HasColumnName("postSearchTagMofifiedDate");
            entity.Property(e => e.PostSearchTagName)
                .HasMaxLength(150)
                .IsUnicode(false)
                .HasColumnName("postSearchTagName");
        });

        modelBuilder.Entity<PostType>(entity =>
        {
            entity.HasKey(e => e.PostTypeId).HasName("PK__PostType__AB2125F03290934D");

            entity.HasIndex(e => e.PostTypeTitle, "UQ__PostType__8E320291D8B5BB01").IsUnique();

            entity.Property(e => e.PostTypeTitle)
                .IsRequired()
                .HasMaxLength(100)
                .IsUnicode(false);
        });

        modelBuilder.Entity<PostVacancy>(entity =>
        {
            entity.Property(e => e.PostVacancyId).HasColumnName("postVacancyID");
            entity.Property(e => e.PostId).HasColumnName("postId");
            entity.Property(e => e.PostVacancyApplyDate)
                .HasColumnType("datetime")
                .HasColumnName("postVacancyApplyDate");
            entity.Property(e => e.PostVacancyCratedDate)
                .HasColumnType("datetime")
                .HasColumnName("postVacancyCratedDate");
            entity.Property(e => e.PostVacancyCreatedBy).HasColumnName("postVacancyCreatedBy");
            entity.Property(e => e.PostVacancyDeptId).HasColumnName("postVacancyDeptId");
            entity.Property(e => e.PostVacancyDivisionId).HasColumnName("postVacancyDivisionId");
            entity.Property(e => e.PostVacancyIsActive)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("postVacancyIsActive");
            entity.Property(e => e.PostVacancyIsDel)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("postVacancyIsDel");
            entity.Property(e => e.PostVacancyModifiedBy).HasColumnName("postVacancyModifiedBy");
            entity.Property(e => e.PostVacancyModifiedDate)
                .HasColumnType("datetime")
                .HasColumnName("postVacancyModifiedDate");
            entity.Property(e => e.PostVacancyName)
                .HasMaxLength(150)
                .IsUnicode(false)
                .HasColumnName("postVacancyName");
            entity.Property(e => e.PostVacancyStationId).HasColumnName("postVacancyStationId");
            entity.Property(e => e.PostVacancyTypeId).HasColumnName("postVacancyTypeId");
        });

        modelBuilder.Entity<QuickAnnouncement>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK_QuickAnnouncement_Id");

            entity.ToTable("QuickAnnouncement");

            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.Url)
                .IsRequired()
                .HasMaxLength(500);
        });

        modelBuilder.Entity<Role>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK_tblUserRoles");

            entity.Property(e => e.Code)
                .IsRequired()
                .HasMaxLength(50)
                .IsUnicode(false);
            entity.Property(e => e.CreatedDate).HasColumnType("datetime");
            entity.Property(e => e.ModifiedDate).HasColumnType("datetime");
            entity.Property(e => e.Name)
                .IsRequired()
                .HasMaxLength(100)
                .IsUnicode(false);
            entity.Property(e => e.Notes)
                .IsRequired()
                .HasMaxLength(500)
                .IsUnicode(false);
        });

        modelBuilder.Entity<RoleMenu>(entity =>
        {
            entity.HasKey(e => new { e.RoleId, e.MenuId });

            entity.Property(e => e.CreatedDate).HasColumnType("datetime");
            entity.Property(e => e.ModifiedDate).HasColumnType("datetime");

            entity.HasOne(d => d.Menu).WithMany(p => p.RoleMenus)
                .HasForeignKey(d => d.MenuId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_RoleMenus_Menus");

            entity.HasOne(d => d.Role).WithMany(p => p.RoleMenus)
                .HasForeignKey(d => d.RoleId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_RoleMenus_Roles");
        });

        modelBuilder.Entity<Section>(entity =>
        {
            entity.Property(e => e.SectionDescription).HasMaxLength(500);
            entity.Property(e => e.SectionTitle).HasMaxLength(200);

            entity.HasOne(d => d.Module).WithMany(p => p.Sections)
                .HasForeignKey(d => d.ModuleId)
                .HasConstraintName("FK__Sections__Module__662B2B3B");

            entity.HasOne(d => d.PostType).WithMany(p => p.Sections)
                .HasForeignKey(d => d.PostTypeId)
                .HasConstraintName("FK__Sections__PostTy__671F4F74");
        });

        modelBuilder.Entity<ShiftLocation>(entity =>
        {
            entity.Property(e => e.EmployeeShiftCode)
                .HasMaxLength(255)
                .HasColumnName("Employee_Shift_Code");
            entity.Property(e => e.LocationCode)
                .HasMaxLength(255)
                .HasColumnName("Location_Code");
            entity.Property(e => e.LocationTxt)
                .HasMaxLength(255)
                .HasColumnName("Location_TXT");
            entity.Property(e => e.ShiftEnd).HasColumnName("Shift_End");
            entity.Property(e => e.ShiftStart).HasColumnName("Shift_Start");
        });

        modelBuilder.Entity<Station>(entity =>
        {
            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.Station1)
                .HasMaxLength(200)
                .HasColumnName("Station");
        });

        modelBuilder.Entity<SubCategory>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK_SubCategories_Id");

            entity.HasIndex(e => new { e.CategoryId, e.Title }, "UQ__SubCateg__DBC25C47F94D7135").IsUnique();

            entity.Property(e => e.Title)
                .IsRequired()
                .HasMaxLength(50)
                .IsUnicode(false);

            entity.HasOne(d => d.Category).WithMany(p => p.SubCategories)
                .HasForeignKey(d => d.CategoryId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__SubCatego__Categ__2A164134");
        });

        modelBuilder.Entity<User>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK_tblUsers");

            entity.Property(e => e.Code)
                .HasMaxLength(50)
                .IsUnicode(false);
            entity.Property(e => e.CreatedDate).HasColumnType("datetime");
            entity.Property(e => e.Email)
                .IsRequired()
                .HasMaxLength(300)
                .IsUnicode(false);
            entity.Property(e => e.EmployeeCode)
                .HasMaxLength(15)
                .IsUnicode(false);
            entity.Property(e => e.MobileNumber)
                .IsRequired()
                .HasMaxLength(50)
                .IsUnicode(false);
            entity.Property(e => e.ModifiedDate).HasColumnType("datetime");
            entity.Property(e => e.Name)
                .IsRequired()
                .HasMaxLength(100)
                .IsUnicode(false);
            entity.Property(e => e.Note)
                .HasMaxLength(500)
                .IsUnicode(false);
            entity.Property(e => e.Password)
                .IsRequired()
                .HasMaxLength(50)
                .IsUnicode(false);
            entity.Property(e => e.Pin)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("PIN");
            entity.Property(e => e.UserId)
                .HasMaxLength(100)
                .IsUnicode(false);
        });

        modelBuilder.Entity<UserQuickLink>(entity =>
        {
            entity.HasKey(e => new { e.UserId, e.MenuId }).HasName("PK__UserQuic__0B11216F1D16AC93");
        });

        modelBuilder.Entity<UserRole>(entity =>
        {
            entity.HasKey(e => new { e.UserId, e.RoleId });

            entity.Property(e => e.CreatedDate).HasColumnType("datetime");
            entity.Property(e => e.ModifiedDate).HasColumnType("datetime");
            entity.Property(e => e.Notes)
                .HasMaxLength(500)
                .IsUnicode(false);

            entity.HasOne(d => d.Role).WithMany(p => p.UserRoles)
                .HasForeignKey(d => d.RoleId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_UserRoles_Roles");

            entity.HasOne(d => d.User).WithMany(p => p.UserRoles)
                .HasForeignKey(d => d.UserId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_UserRoles_Users");
        });

        OnModelCreatingPartial(modelBuilder);
    }

    partial void OnModelCreatingPartial(ModelBuilder modelBuilder);
}