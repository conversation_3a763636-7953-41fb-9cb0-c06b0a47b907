﻿@page "/setup/DigitalLearning"
@using ButtonType = MudBlazor.ButtonType
@using ChangeEventArgs = Microsoft.AspNetCore.Components.ChangeEventArgs
@using FilterType = Syncfusion.Blazor.DropDowns.FilterType
@using Section = PakSuzuki.DTO.Section
@using Path = Path
@inject IWebHostEnvironment Env
@inject PostDataService service
@inject SfDialogService DialogService
@inject AuthenticationStateProvider AuthenticationStateProvider

<SfToast @ref="_toastObj"></SfToast>

<div id="banner">
    <MudProgressCircular Color="Color.Info" Indeterminate="@spinner"/>
</div>

<h2>Digital Learning</h2>
<SfDialog @ref="_dlgForm" Visible="false" Width="800px" ShowCloseIcon="true" CloseOnEscape="true" IsModal="true">
    <DialogTemplates>
        <Header>Digital Learning</Header>
        <Content>
            <div style="width:100%;margin-left: auto;margin-right:auto">

                <div style="height:12px;"></div>

                <div class="row mb-2">
                    <div class="col-md">
                        <MudImage style="width:100%;height:130px;object-fit:cover" Src="@_profilePicThumb" Alt="Thumbnail" Elevation="25" Class="rounded-lg"/>
                        <MudFileUpload T="IBrowserFile" Accept=".png, .jpg, .jpeg" FilesChanged="UploadThumbnail" MaximumFileCount="1" Disabled="!_isFormEditable">
                            <ButtonTemplate Context="ctx">
                                <MudButton HtmlTag="label"
                                           Style="width:100%"
                                           Variant="Variant.Filled"
                                           Color="Color.Primary"
                                           Size="Size.Small"
                                           StartIcon="@Icons.Material.Filled.CloudUpload"
                                           for="@ctx.Id">
                                    <div style="display:flex; flex-direction:column;">
                                        <div>Thumbnail</div>
                                        <div style="font-size:10px"> (310 px x 500px)</div>

                                    </div>
                                </MudButton>
                            </ButtonTemplate>
                        </MudFileUpload>
                    </div>
                    <div class="col-md">
                        <MudImage Style="width:100%;height:130px;object-fit:cover" Src="@_profilePicHdr" Alt="Header Image" Elevation="25" Class="rounded-lg"/>
                        <MudFileUpload T="IBrowserFile" Accept=".png, .jpg, .jpeg" FilesChanged="UploadHeaderImage" MaximumFileCount="1" Disabled="!_isFormEditable">
                            <ButtonTemplate Context="ctx">
                                <MudButton HtmlTag="label"
                                           Style="width:100%"
                                           Size="Size.Small"
                                           Variant="Variant.Filled"
                                           Color="Color.Primary"
                                           StartIcon="@Icons.Material.Filled.CloudUpload"
                                           for="@ctx.Id">
                                    <div style="display:flex; flex-direction:column;">
                                        <div>Header Picture</div>
                                        <div style="font-size:10px"> (1080 px x 420px)</div>

                                    </div>
                                </MudButton>
                            </ButtonTemplate>
                        </MudFileUpload>
                    </div>
                </div>
                <div cass="row mb-2">
                    <div class="col-md">
                        <SfTextBox Placeholder="Title" FloatLabelType="FloatLabelType.Always" @bind-Value="postObj.postName" style="width:100%" Enabled="_isFormEditable"/>
                    </div>
                </div>
                <div class="row mb-2">
                    <div class="col-md">
                        <SfDropDownList FloatLabelType="FloatLabelType.Always"
                                        Enabled="_isFormEditable"
                                        TValue="int" TItem="Category" Placeholder="Category" DataSource="@_categories" @bind-Value="postObj.postCategoryId">
                            <DropDownListFieldSettings Text="CategoryTitle" Value="CategoryId"></DropDownListFieldSettings>
                            <DropDownListEvents TValue="int" TItem="Category" ValueChange="OnCategorySelect"></DropDownListEvents>
                        </SfDropDownList>
                    </div>
                    <div class="col-md">
                        <SfDropDownList FloatLabelType="FloatLabelType.Always"
                                        Enabled="_isFormEditable"
                                        AllowFiltering="true" FilterType="FilterType.Contains"
                                        ShowClearButton="true"
                                        TValue="int?" TItem="SubCategoryDto" Placeholder="Sub Category" DataSource="@_subCategories" @bind-Value="postObj.postSubCategoryId">
                            <DropDownListFieldSettings Text="Title" Value="Id"></DropDownListFieldSettings>
                        </SfDropDownList>
                    </div>
                </div>
                <div class="row mb-2">
                    <div class="col-md">
                        <label>Content</label>
                        <SfRichTextEditor @ref="_rTxt" Enabled="_isFormEditable" Readonly="false" Width="100%" Height="300px" @bind-Value="postObj.postContentText" EnableXhtml="true">
                            <RichTextEditorToolbarSettings Items="@_tools"/>
                        </SfRichTextEditor>
                    </div>
                </div>
                <div class="row mb-2">
                    <div class="col-md">
                        <SfTextBox Placeholder="Remarks" FloatLabelType="FloatLabelType.Always" @bind-Value="postObj.postRemarks" Multiline="true" Enabled="_isFormEditable"/>

                    </div>
                </div>
                <div class="row mb-2">
                    <div class="col-md" style="display: flex;gap:10px;align-items: end;">
                        <SfTextBox Placeholder="Search Tags" FloatLabelType="FloatLabelType.Always" @bind-Value="_srhTag" Enabled="_isFormEditable"/>
                        <MudButton ButtonType="ButtonType.Button" Color="Color.Success" Disabled="!_isFormEditable"
                                   Variant="Variant.Filled" Size="Size.Small" OnClick="AddSrhTag">
                            Add
                        </MudButton>
                    </div>
                </div>
                <div class="row mb-2">
                    <div class="col-md">

                        <MudText Typo="@Typo.caption">Total Search Tags : @(searchTags.Any() ? searchTags.Count : 0)</MudText>

                        <MudChipSet T="string" AllClosable OnClose="Closed">
                            @foreach (var item in searchTags)
                            {
                                <MudChip Variant="Variant.Outlined" Color="Color.Secondary" Text="@item.postSearchTagName"></MudChip>
                            }
                        </MudChipSet>
                    </div>
                </div>
                <div class="row mb-2">
                    <div class="col-md">

                        <MudFileUpload Style="margin-top:20px;" T="IReadOnlyList<IBrowserFile>" Accept=".png, .jpg, .jpeg, .pdf, .docx, .txt, .doc," FilesChanged="UploadAttachments" Disabled="!_isFormEditable">
                            <ButtonTemplate>
                                <MudButton HtmlTag="label" Style="width:100%"
                                           Variant="Variant.Filled"
                                           Color="Color.Primary"
                                           StartIcon="@Icons.Material.Filled.CloudUpload"
                                           Size="Size.Small"
                                           for="@context.Id">
                                    Attach
                                </MudButton>
                            </ButtonTemplate>
                        </MudFileUpload>
                    </div>
                    <div class="col-md">
                        <label>Publish Date</label>
                        <Syncfusion.Blazor.Calendars.SfDatePicker Enabled="_isFormEditable" Placeholder="Publish Date" @bind-Value="postObj.postPublishDate"/>
                    </div>
                    <div class="col-md">
                        <label>Expiry Date</label>
                        <Syncfusion.Blazor.Calendars.SfDatePicker Placeholder="Expiry Date" @bind-Value="postObj.postExpiryDate"/>
                    </div>
                </div>
                <div class="row mb-2">
                    <div class="col-md">
                        <MudChipSet T="string" AllClosable OnClose="ClosedAttachment">
                            @foreach (var attachFile in attachmentsList!)
                            {
                                <MudChip Variant="Variant.Outlined" Color="Color.Success" Text="@attachFile.postAttachmentName"></MudChip>
                            }
                        </MudChipSet>
                    </div>
                </div>
                <div class="row">
                    <dic class="col-md">
                        <MudButton ButtonType="ButtonType.Submit" Color="Color.Primary"
                                   Variant="Variant.Filled" Size="Size.Small" StartIcon="@Icons.Material.Filled.Save" OnClick="SavePost">
                            Save
                        </MudButton>
                        <MudButton ButtonType="ButtonType.Button" Color="Color.Success"
                                   Variant="Variant.Filled" Size="Size.Small" OnClick="() => _dlgForm!.HideAsync()">
                            Cancel
                        </MudButton>
                    </dic>
                </div>

            </div>
        </Content>

    </DialogTemplates>
    <DialogEvents Opened="@DialogOpen"></DialogEvents>
</SfDialog>


<div class="row mb-2">
    <div class="col-md">
        <MudButton Size="Size.Small" Color="Color.Primary" StartIcon="@Icons.Material.Filled.Add" Variant="Variant.Filled" OnClick="OpenCreateForm">Add</MudButton>
        <MudButton Size="Size.Small" Color="Color.Success" StartIcon="@Icons.Material.Filled.Edit" Variant="Variant.Filled" OnClick="OpenEditForm">Edit</MudButton>
        <MudButton Size="Size.Small" Color="Color.Warning" StartIcon="@Icons.Material.Filled.Delete" Variant="Variant.Filled" OnClick="ConfirmDelete">Delete</MudButton>
        <MudButton Size="Size.Small" Color="Color.Info" StartIcon="@Icons.Material.Filled.Group" Variant="Variant.Filled" OnClick="OpenAssignRole">Assign Roles</MudButton>
    </div>
</div>

<div class="row">
    <div class="mb-2 col-md">
        @if (_allPosts.Any())
        {
            <SfGrid @ref="_dgMain" DataSource="_allPosts" AllowSelection="true" AllowFiltering="true" AllowSorting="true" Width="100%">
                <GridFilterSettings Type="Syncfusion.Blazor.Grids.FilterType.Excel"></GridFilterSettings>
                <GridColumns>
                    <GridColumn AutoFit="true" Visible="false" HeaderText="ID" Field="@nameof(PostDTO.postId)"></GridColumn>
                    <GridColumn Width="450" HeaderText="Title" Field="@nameof(PostDTO.postName)"></GridColumn>
                    <GridColumn AutoFit="true" HeaderText="Category" Field="@nameof(PostDTO.PostCategory)" Format="dd-MMM-yyyy"></GridColumn>
                    <GridColumn AutoFit="true" HeaderText="Sub Category" Field="@nameof(PostDTO.PostSubCategory)" Format="dd-MMM-yyyy"></GridColumn>
                    <GridColumn AutoFit="true" HeaderText="Publish Date" Field="@nameof(PostDTO.postPublishDate)" Format="dd-MMM-yyyy"></GridColumn>
                    <GridColumn AutoFit="true" HeaderText="Expiry Date" Field="@nameof(PostDTO.postExpiryDate)" Format="dd-MMM-yyyy"></GridColumn>
                    <GridColumn AutoFit="true" HeaderText="Status" Field="@nameof(PostDTO.postStatus)"></GridColumn>
                    <GridColumn AutoFit="true" HeaderText="Created By" Field="@nameof(PostDTO.CreatedBy)"></GridColumn>
                    <GridColumn AutoFit="true" HeaderText="Created Date" Field="@nameof(PostDTO.CreatedDate)" Format="dd-MMM-yyyy h:mm:ss tt"></GridColumn>
                    <GridColumn AutoFit="true" HeaderText="Modified By" Field="@nameof(PostDTO.ModifiedBy)"></GridColumn>
                    <GridColumn AutoFit="true" HeaderText="Modified Date" Field="@nameof(PostDTO.ModifiedDate)" Format="dd-MMM-yyyy h:mm:ss tt"></GridColumn>
                </GridColumns>
            </SfGrid>
        }
    </div>
</div>

<SfDialog @ref="_dlgFormRole" Visible="false" Width="500px" ShowCloseIcon="true" CloseOnEscape="true" IsModal="true">
    <DialogTemplates>
        <Header>Assign Roles</Header>
        <Content>
            <div class="row" style="display:flex">
                <MudText Typo="Typo.caption">Selected Post: <b>@postObj.postName</b></MudText>
            </div>
            <div style="height:10px;"></div>
            <div class="row" style="text-align:right;">
                <SfCheckBox @bind-Checked="isChecked" Label="Select All" @onchange="SelectAll">Select All</SfCheckBox>
            </div>
            <div style="height:10px;"></div>

            <div class="row">
                <SfGrid @ref="_dgMainRole" Height="400px" DataSource="_allRole" AllowSelection="true" AllowFiltering="true" AllowSorting="true" Width="500px">
                    <GridFilterSettings Type="Syncfusion.Blazor.Grids.FilterType.Excel"></GridFilterSettings>
                    <GridColumns>
                        <GridColumn AutoFit="true" Visible="false" HeaderText="Id" Field="@nameof(RoleDTO.Id)"></GridColumn>
                        <GridColumn Width="350" HeaderText="Roles" Field="@nameof(RoleDTO.Name)"></GridColumn>
                        <GridColumn Field="@nameof(RoleDTO.isSelect)" HeaderTextAlign="TextAlign.Center" HeaderText="Selected" Width="120" ClipMode="ClipMode.EllipsisWithTooltip">
                            <Template>
                                @{
                                    var ctx = context as RoleDTO;
                                    <div>
                                        <center>
                                            <SfCheckBox @bind-Checked="@ctx.isSelect"></SfCheckBox>
                                        </center>
                                    </div>
                                }
                            </Template>
                        </GridColumn>
                    </GridColumns>
                </SfGrid>

            </div>
            <div class="page-panel">
                <div>
                    <MudButton Size="Size.Small" Color="Color.Primary" StartIcon="@Icons.Material.Filled.Save" Variant="Variant.Filled" OnClick="SaveUserRole">Save</MudButton>
                    <MudButton Size="Size.Small" Color="Color.Success" StartIcon="@Icons.Material.Filled.Cancel" Variant="Variant.Filled" OnClick="() => _dlgFormRole!.HideAsync()">Cancel</MudButton>
                </div>
            </div>
        </Content>
    </DialogTemplates>
</SfDialog>

@code {
    private SfDialog? _dlgForm, _dlgFormRole;
    private IList<IBrowserFile> _files = new List<IBrowserFile>();
    private DateTime? _date = DateTime.Today;
    private List<RoleDTO> _allRole = new();
    private RoleDTO _role = new();
    private List<Category> _categories = new();
    private SfGrid<RoleDTO>? _dgMainRole;
    private List<PostDTO> _allPosts = new();
    private SfGrid<PostDTO> _dgMain;
    private PostDTO postObj = new();
    private List<PostSearchTagDTO> searchTags = new();
    private string _srhTag;
    private SfRichTextEditor _rTxt;
    private string _profilePicThumb = "images/upload.jpg";
    private string _profilePicHdr = "images/upload.jpg";
    private IBrowserFile? _imgFileThumb;
    private IBrowserFile? _imgFileHdr;
    private List<IBrowserFile>? _attachments = new();
    private List<PostAttachmentDTO> attachmentsList = new();
    private SfToast? _toastObj = new();
    private bool spinner;
    private bool _isPublish = true;

    bool isChecked;
    private bool _isFormEditable = true;

    private async void SelectAll(ChangeEventArgs args)
    {
        try
        {
            foreach (var item in _allRole)
            {
                item.isSelect = isChecked;
            }

            StateHasChanged();
        }
        catch (Exception ex)
        {
            var tm = new ToastModel { Content = ex.Message, Title = "Error", ShowCloseButton = true, Timeout = 0 };
            await _toastObj!.ShowAsync(tm);
        }
    }

    private async Task SaveUserRole()
    {
        try
        {
            await service.savePostRolesInPostScreen(_allRole, postObj.postId);
            var tm = new ToastModel { Content = "Roles assigned successfully.", Title = "Info", ShowCloseButton = true, Timeout = 5000 };
            await _toastObj!.ShowAsync(tm);
            await _dlgFormRole!.HideAsync();
        }
        catch (Exception ex)
        {
            var tm = new ToastModel { Content = ex.Message, Title = "Error", ShowCloseButton = true, Timeout = 5000 };
            await _toastObj!.ShowAsync(tm);
        }
    }

    private async Task OpenAssignRole()
    {
        try
        {
            if (_dgMain!.SelectedRecords is { Count: > 0 })
            {
                var pId = _dgMain.SelectedRecords[0].postId;
                _allRole = await service.GetAllRolesAsyncWithSelectedPost(pId);
                postObj = await service.getPostWithId(pId);
                _subCategories = await service.GetSubCategories(postObj.postCategoryId);
                await _dlgFormRole!.ShowAsync();
            }
            else
            {
                var tm = new ToastModel { Content = "Please select post.", Title = "Info", ShowCloseButton = true, Timeout = 5000 };
                await _toastObj!.ShowAsync(tm);
            }
        }
        catch (Exception ex)
        {
            var tm = new ToastModel { Content = ex.Message, Title = "Error", ShowCloseButton = true, Timeout = 5000 };
            await _toastObj!.ShowAsync(tm);
        }
    }

    private async Task DialogOpen()
    {
        await  _rTxt.RefreshUIAsync();
    }

    private async void OpenEditForm()
    {
        if (_dgMain?.SelectedRecords.Count > 0)
        {
            if (_dgMain?.SelectedRecords[0].postStatus != "Expired")
            {
                //_isPublish = false;
                var pId = _dgMain.SelectedRecords[0].postId;
                postObj = await service.getPostWithId(pId);
                _subCategories = await service.GetSubCategories(postObj.postCategoryId);
                _isPublish = postObj.postPublishDate != null && !(postObj.postPublishDate >= DateTime.Today);
                _isFormEditable = postObj.postStatus is "Draft" or "Published";
                _profilePicThumb = string.IsNullOrEmpty(postObj.postThumbnailPath) ? "images/upload.jpg" : AppDataService.ConvertToUrl(postObj.postThumbnailPath);
                _profilePicHdr = string.IsNullOrEmpty(postObj.postHeadImagePath) ? "images/upload.jpg" : AppDataService.ConvertToUrl(postObj.postHeadImagePath);
                searchTags = await service.getPostSrhTagsWithPostId(pId);
                attachmentsList = await service.getPostAttachmentWithPostId(pId);
                await _dlgForm!.ShowAsync();
            }
            else
            {
                var tm = new ToastModel
                {
                    Content = "Post is expired, you can not edit.",
                    Title = "Info",
                    ShowCloseButton = true,
                    Timeout = 5000,
                    CssClass = "e-toast-error",
                    ShowProgressBar = true
                };
                await _toastObj!.ShowAsync(tm);
            }
        }
    }

    public void Closed(MudChip chip)
    {
        if (_isFormEditable)
        {
            var p = searchTags.SingleOrDefault(x => x.postSearchTagName == chip.Text)!;
            searchTags.Remove(p);
            StateHasChanged();
        }
    }

    public void ClosedAttachment(MudChip chip)
    {
        if (_isFormEditable)
        {
            var a = attachmentsList.SingleOrDefault(x => x.postAttachmentName == chip.Text);
            if (a != null) attachmentsList.Remove(a);
            StateHasChanged();
        }
    }

    private void OpenCreateForm()
    {
        _isFormEditable = true;
        _isPublish = true;
        postObj = new PostDTO { postExpiryDate = DateTime.Today.AddDays(60) };
        searchTags = new List<PostSearchTagDTO>();
        _profilePicThumb = "images/upload.jpg";
        _profilePicHdr = "images/upload.jpg";
        _imgFileThumb = null;
        _imgFileHdr = null;
        _attachments = new List<IBrowserFile>();
        attachmentsList = new List<PostAttachmentDTO>();
        _files = new List<IBrowserFile>();
        _dlgForm!.ShowAsync();
    }

    private async void ConfirmDelete()
    {
        try
        {
            if (_dgMain!.SelectedRecords is { Count: > 0 })
            {
                var conf = await DialogService.ConfirmAsync("Are you sure want to delete this record", "Confirm");
                if (conf)
                {
                    var Id = _dgMain.SelectedRecords[0].postId;
                    service.deletePost(Id);
                    _allPosts = await service.getAllPosts((int)DTO.Section.DigitalLearning);
                    StateHasChanged();
                }
            }
        }
        catch (Exception ex)
        {
            ToastModel t = new() { Title = "Error", Content = ex.Message };
            await _toastObj!.ShowAsync(t);
        }
    }

    protected override async Task OnInitializedAsync()
    {
        _allPosts = await service.getAllPosts((int)Section.DigitalLearning);
        _categories = await service.GetCategories((int)Section.DigitalLearning);
    }

    private async void UploadAttachments(IReadOnlyList<IBrowserFile> files)
    {
        foreach (var file in files)
        {
            _attachments!.Add(file);
            var postTitle = Guid.NewGuid().ToString();
            var dirPath = $"{Env.WebRootPath}\\Posts\\{postTitle}\\Attachments";
            if (Directory.Exists(dirPath) == false)
            {
                Directory.CreateDirectory(dirPath);
            }

            var path = dirPath + "\\" + file.Name;
            PostAttachmentDTO p = new()
            {
                postAttachmentDocURL = path,
                postAttachmentName = file.Name,
                postAttachmentDocSize = file.Size.ToString(),
                postAttachmentDocType = file.ContentType,
                postAttachmentDocExt = Path.GetExtension(file.Name)
            };
            attachmentsList.Add(p);

            var stream = file.OpenReadStream(30 * 1024 * 1024);
            var fs = File.Create(path);
            await stream.CopyToAsync(fs);
            stream.Close();
            fs.Close();
        }

        StateHasChanged();
    }

    private async void UploadThumbnail(IBrowserFile file)
    {
        _imgFileThumb = file;
        var memoryStream = new MemoryStream();
        await using var fileStream = file.OpenReadStream(long.MaxValue);
        await fileStream.CopyToAsync(memoryStream);
        var bytes = memoryStream.ToArray();
        _profilePicThumb = "data:image/png;base64," + Convert.ToBase64String(bytes);
        StateHasChanged();
    }

    private async void UploadHeaderImage(IBrowserFile file)
    {
        _imgFileHdr = file;
        var memoryStream = new MemoryStream();
        await using var fileStream = file.OpenReadStream(long.MaxValue);
        await fileStream.CopyToAsync(memoryStream);
        var bytes = memoryStream.ToArray();
        _profilePicHdr = "data:image/png;base64," + Convert.ToBase64String(bytes);
        StateHasChanged();
    }

    private readonly List<ToolbarItemModel> _tools =
    [
        new() { Command = ToolbarCommand.Bold },
        new() { Command = ToolbarCommand.Italic },
        new() { Command = ToolbarCommand.Underline },
        new() { Command = ToolbarCommand.Separator },
        new() { Command = ToolbarCommand.Formats },
        new() { Command = ToolbarCommand.Alignments },
        new() { Command = ToolbarCommand.OrderedList },
        new() { Command = ToolbarCommand.UnorderedList },
        new() { Command = ToolbarCommand.Separator },
        // new ToolbarItemModel() { Command = ToolbarCommand.CreateLink },
        // new ToolbarItemModel() { Command = ToolbarCommand.Image },
        // new ToolbarItemModel() { Command = ToolbarCommand.Separator },
        // new ToolbarItemModel() { Command = ToolbarCommand.SourceCode },
        // new ToolbarItemModel() { Command = ToolbarCommand.Separator },
        new() { Command = ToolbarCommand.Undo },
        new() { Command = ToolbarCommand.Redo }
    ];

    private void AddSrhTag()
    {
        if (_srhTag != "" && _srhTag != null)
        {
            PostSearchTagDTO p = new()
            {
                postSearchTagName = _srhTag,
                postSearchTagID = -1
            };
            searchTags.Add(p);
            _srhTag = "";
            StateHasChanged();
        }
    }

    private void RemoveSrhTag(string tagName)
    {
        var p = searchTags.SingleOrDefault(x => x.postSearchTagName == tagName)!;
        searchTags.Remove(p);
        StateHasChanged();
    }

    private void RemoveAttachFile(string fileName)
    {
        var a = attachmentsList!.SingleOrDefault(x => x.postAttachmentName == fileName);
        attachmentsList.Remove(a);
        StateHasChanged();
    }

    private async Task<string> SaveImageThumb(string postTitle, string fileName)
    {
        if (_imgFileThumb != null && _profilePicThumb != "images/upload.jpg")
        {
            postTitle = Guid.NewGuid().ToString();
            fileName = postTitle;
            var dirPath = $"{Env.WebRootPath}\\Posts\\{postTitle}";
            if (Directory.Exists(dirPath) == false)
            {
                Directory.CreateDirectory(dirPath);
            }

            var stream = _imgFileThumb.OpenReadStream(long.MaxValue);
            var path = dirPath + "\\" + fileName + "-thumb.png";
            var fs = File.Create(path);
            await stream.CopyToAsync(fs);
            stream.Close();
            fs.Close();
            _imgFileThumb = null;
            return await Task.FromResult(path);
        }

        return await Task.FromResult(_profilePicThumb);

        return await Task.FromResult("");
    }

    private async Task<string> SaveImageHdr(string postTitle, string fileName)
    {
        if (_imgFileHdr != null && _profilePicHdr != "images/upload.jpg")
        {
            postTitle = Guid.NewGuid().ToString();
            fileName = postTitle;
            var dirPath = $"{Env.WebRootPath}\\Posts\\{postTitle}";
            if (Directory.Exists(dirPath) == false)
            {
                Directory.CreateDirectory(dirPath);
            }

            var stream = _imgFileHdr.OpenReadStream(long.MaxValue);
            var path = dirPath + "\\" + fileName + "-hdr.png";

            var fs = File.Create(path);
            await stream.CopyToAsync(fs);
            stream.Close();
            fs.Close();
            _imgFileHdr = null;
            return await Task.FromResult(path);
        }

        return await Task.FromResult(_profilePicHdr);
        return await Task.FromResult("");
    }

    private async Task<string> saveAttachments(string postTitle)
    {
        postTitle = Guid.NewGuid().ToString();
        //fileName = postTitle;
        var dirPath = $"{Env.WebRootPath}\\Posts\\{postTitle}\\Attachments";
        if (Directory.Exists(dirPath) == false)
        {
            Directory.CreateDirectory(dirPath);
        }

        if (_attachments!.Any())
        {
            foreach (var attachfile in _attachments!)
            {
                var path = dirPath + "\\" + attachfile.Name;
                PostAttachmentDTO p = new()
                {
                    postAttachmentDocURL = path,
                    postAttachmentName = attachfile.Name,
                    postAttachmentDocSize = attachfile.Size.ToString(),
                    postAttachmentDocType = attachfile.ContentType,
                    postAttachmentDocExt = Path.GetExtension(attachfile.Name)
                };
                attachmentsList.Add(p);

                var stream = attachfile.OpenReadStream(30 * 1024 * 1024);
                var fs = File.Create(path);
                await stream.CopyToAsync(fs);
                stream.Close();
                fs.Close();
                // await using FileStream fs = new(path, FileMode.Create);
                // await attachfile.OpenReadStream(long.MaxValue).CopyToAsync(fs);
            }

            return await Task.FromResult("");
        }

        return await Task.FromResult("");
    }

    private async Task SavePost()
    {
        try
        {
            spinner = true;
            StateHasChanged();
            var msg = await service.checkPublishExpiryDate(postObj.postPublishDate ?? DateTime.Now, postObj.postExpiryDate ?? DateTime.Now, postObj.postId);
            if (msg != "")
            {
                ToastModel t1 = new()
                {
                    Title = "Information",
                    Content = msg,
                    ShowProgressBar = true,
                    ShowCloseButton = true,
                    Timeout = 5000
                };
                await _toastObj!.ShowAsync(t1);
                spinner = false;
                return;
            }

            if (string.IsNullOrEmpty(postObj.postName) || postObj.postName == "" || postObj.postContentText == "")
            {
                ToastModel t1 = new()
                {
                    Title = "Information",
                    Content = "Title, Description fields is required..",
                    ShowProgressBar = true,
                    ShowCloseButton = true,
                    Timeout = 5000
                };
                await _toastObj!.ShowAsync(t1);
                spinner = false;
                return;
            }

            // if (_imgFileThumb == null)
            // {
            //     // thmbnail image empty msg
            //     return;
            // }
            // if (_imgFileHdr == null)
            // {
            //     // header image empty msg
            //     return;
            // }
            // if (searchTags.Count <= 0)
            // {
            //     // searchtag empty msg
            //     return;
            // }
            var contentText = await _rTxt.GetTextAsync();
            var postName = postObj.postName + "-" + DateTime.Now.ToString("ddMMMyyyyHHmmssff");
            postObj.postThumbnailPath = await SaveImageThumb("", "");
            postObj.postHeadImagePath = await SaveImageHdr("", "");
            postObj.postIconImagePath = "";
            //await saveAttachments("");

            var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
            var user = authState.User;
            var userId = "";
            if (user.Identity is { Name: not null })
            {
                userId = user.Identity.Name;
            }

            await service.savePost(postObj, searchTags, attachmentsList, -1, contentText, userId, (int)Section.DigitalLearning);

            _allPosts = await service.getAllPosts((int)Section.DigitalLearning);

            postObj = new PostDTO();
            searchTags = new List<PostSearchTagDTO>();
            _profilePicThumb = "images/upload.jpg";
            _profilePicHdr = "images/upload.jpg";
            _imgFileThumb = null;
            _imgFileHdr = null;
            _attachments = new List<IBrowserFile>();
            attachmentsList = new List<PostAttachmentDTO>();
            _files = new List<IBrowserFile>();

            spinner = false;
            StateHasChanged();

            ToastModel t = new() { Title = "Information", Content = "Record Saved successfully." };
            await _toastObj.ShowAsync(t);

            _dlgForm.HideAsync();

            //nav.NavigateTo("/");
        }
        catch (Exception ex)
        {
            ToastModel t = new() { Title = "Error", Content = ex.Message };
            await _toastObj.ShowAsync(t);
        }
    }


    private List<SubCategoryDto> _subCategories = new();

    private async Task OnCategorySelect(ChangeEventArgs<int, Category> obj)
    {
        postObj.postSubCategoryId = null;
        _subCategories = await service.GetSubCategories(obj.Value);
    }

}

<style>
    #banner {
        position: fixed;
        left: 50%;
        margin-left: -240px;
        width: 480px;
        height: 115px;
        border-radius: 20px;
        text-align: center;
    }


    .e-grid td.e-active {
        background: #faa601 !important;
    }
</style>