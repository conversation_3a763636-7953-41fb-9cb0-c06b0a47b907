﻿@page "/setup/internalvacancies"
@using ButtonType = MudBlazor.ButtonType
@using ChangeEventArgs = Microsoft.AspNetCore.Components.ChangeEventArgs
@using FilterType = Syncfusion.Blazor.DropDowns.FilterType
@using Section = PakSuzuki.DTO.Section
@using Path = System.IO.Path
@inject IWebHostEnvironment env
@inject PostDataService service
@inject NavigationManager nav
@inject SfDialogService DialogService
@inject AuthenticationStateProvider AuthenticationStateProvider
@inject CorporateService cService

<SfToast @ref="toastObj"></SfToast>

<div id="banner">
    <MudProgressCircular Color="Color.Info" Indeterminate="@spinner" />
</div>

<h2>Internal Vacancies</h2>
<SfDialog @ref="dlgForm" Visible="false" Width="800px" ShowCloseIcon="true" CloseOnEscape="true" IsModal="true">
    <DialogTemplates>
        <Header>Internal Vacancies</Header>
        <Content>
            <div style="width:100%;margin-left: auto;margin-right:auto">

                <div style="height:12px;"></div>

                <div class="row mb-2">
                    <div class="col-md">
                        <SfTextBox Placeholder="Job Title" FloatLabelType="FloatLabelType.Always" @bind-Value="postObj.postName"
                                   style="width:100%" Enabled="IsFormEditable" />
                    </div>
                </div>
                <div class="row">
                    <div class="col-md mb-2">
                        @if (divisionsList!=null && divisionsList.Any())
                        {
                            <SfDropDownList FloatLabelType="FloatLabelType.Auto"
                                            AllowFiltering="true" FilterType="FilterType.Contains"
                                            TValue="string?" TItem="DivisionDto" Placeholder="Division" DataSource="@divisionsList" @bind-Value="postObj.JobDivisionName">
                                <DropDownListFieldSettings Text="Name" Value="Name"></DropDownListFieldSettings>
                                <DropDownListEvents TValue="string?" TItem="DivisionDto" ValueChange="FillDepartments"></DropDownListEvents>
                                @*<DropDownListEvents TValue="DivisionDto" TItem="string?" ValueChange="FillDepartments"> </DropDownListEvents>*@
                            </SfDropDownList>
                        }
                    </div>
                    <div class="col-md mb-2">
                        @if (departmentsList!=null && departmentsList.Any())
                        {
                            <SfDropDownList FloatLabelType="FloatLabelType.Auto"
                                            AllowFiltering="true" FilterType="FilterType.Contains"
                                            TValue="string?" TItem="DepartmentDto" Placeholder="Department" DataSource="@departmentsList" @bind-Value="postObj.JobDepartmentName">
                                <DropDownListFieldSettings Text="Name" Value="Name"></DropDownListFieldSettings>
                            </SfDropDownList>
                        }
                    </div>
                </div>
                <div class="row mb-2">
                    @*<div class="col-md">
                        <SfDropDownList DataSource="_jobDepartmentsList" @bind-Value="postObj.JobDepartmentId" Placeholder="Deaprtment" AllowFiltering="true" FilterType="FilterType.Contains"
                                        FloatLabelType="FloatLabelType.Always"
                                        Enabled="IsFormEditable"
                                        TValue="int?" TItem="JobDepartmentDto">
                            <DropDownListFieldSettings Value="Id" Text="Name"></DropDownListFieldSettings>
                        </SfDropDownList>
                    </div>*@

                    
                </div>
                <div class="row mb-2">
                    <div class="col-md">
                        <SfDropDownList DataSource="_jobCitiesList" @bind-Value="postObj.JobCityId" Placeholder="City" AllowFiltering="true" FilterType="FilterType.Contains"
                                        FloatLabelType="FloatLabelType.Always"
                                        Enabled="IsFormEditable"
                                        TValue="int?" TItem="JobCityDto">
                            <DropDownListFieldSettings Value="Id" Text="Name"></DropDownListFieldSettings>
                        </SfDropDownList>
                    </div>
                    <div class="col-md">
                        <SfDropDownList DataSource="_jobTypesList" @bind-Value="postObj.JobTypeId" Placeholder="Job Type" AllowFiltering="true" FilterType="FilterType.Contains"
                                        FloatLabelType="FloatLabelType.Always"
                                        Enabled="IsFormEditable"
                                        TValue="int?" TItem="JobTypeDto">
                            <DropDownListFieldSettings Value="Id" Text="Name"></DropDownListFieldSettings>
                        </SfDropDownList>
                    </div>

                    <div class="col-md">
                        <SfDatePicker Format="d-MMM-yyyy" Placeholder="Last Date to Apply" FloatLabelType="FloatLabelType.Always"
                                      Enabled="IsFormEditable"
                                      @bind-Value="postObj.JobLastDateToApply" ShowClearButton="true" ShowTodayButton="true">
                        </SfDatePicker>
                    </div>
                </div>
                <div class="row mb-2">
                    <div class="col-md">
                        <label>Job Description</label>
                        <SfRichTextEditor @ref="rTxt" Width="100%" Height="300px" @bind-Value="postObj.postContentText" EnableXhtml="true" Enabled="IsFormEditable">
                            <RichTextEditorToolbarSettings Items="@_tools" />
                        </SfRichTextEditor>
                    </div>
                </div>
                <div class="row mb-2">
                    <div class="col-md">
                        <SfTextBox Placeholder="Remarks" FloatLabelType="FloatLabelType.Always" @bind-Value="postObj.postRemarks" Multiline="true" Enabled="IsFormEditable" />

                    </div>
                </div>
                <div class="row mb-2">
                    <div class="col-md" style="display: flex;gap:10px;align-items: end;">
                        <SfTextBox Placeholder="Search Tags" FloatLabelType="FloatLabelType.Always" @bind-Value="srhTag" Enabled="IsFormEditable" />
                        <MudButton ButtonType="ButtonType.Button" Color="Color.Success" Disabled="!IsFormEditable"
                                   Variant="Variant.Filled" Size="Size.Small" OnClick="addSrhTag">
                            Add
                        </MudButton>
                    </div>
                </div>
                <div class="row mb-2">
                    <div class="col-md">

                        <MudText Typo="@Typo.caption">Total Search Tags : @(postSearchTags.Any() ? postSearchTags.Count : 0)</MudText>

                        <MudChipSet T="string" AllClosable OnClose="Closed">
                            @foreach (var item in postSearchTags)
                            {
                                <MudChip Variant="Variant.Outlined" Color="Color.Secondary" Text="@item.postSearchTagName"></MudChip>
                            }
                        </MudChipSet>
                    </div>
                </div>
                <div class="row mb-2">
                    <div class="col-md">

                        <MudFileUpload Style="margin-top:20px;" T="IReadOnlyList<IBrowserFile>" Accept=".png, .jpg, .jpeg, .pdf, .docx, .txt, .doc," FilesChanged="UploadAttachments" Disabled="!IsFormEditable">
                            <ButtonTemplate>
                                <MudButton HtmlTag="label" Style="width:100%"
                                           Variant="Variant.Filled"
                                           Color="Color.Primary"
                                           StartIcon="@Icons.Material.Filled.CloudUpload"
                                           Size="Size.Small"
                                           for="@context.Id">
                                    Attach
                                </MudButton>
                            </ButtonTemplate>
                        </MudFileUpload>
                    </div>
                    <div class="col-md">
                        <label>Publish Date</label>
                        <Syncfusion.Blazor.Calendars.SfDatePicker Enabled="@IsFormEditable" Placeholder="Publish Date" @bind-Value="postObj.postPublishDate" />
                    </div>
                    <div class="col-md">
                        <label>Expiry Date</label>
                        <Syncfusion.Blazor.Calendars.SfDatePicker Placeholder="Expiry Date" @bind-Value="postObj.postExpiryDate" />
                    </div>
                </div>
                <div class="row mb-2">
                    <div class="col-md">
                        <MudChipSet T="string" AllClosable OnClose="ClosedAttachment">
                            @foreach (var attachFile in attachmentDTO!)
                            {
                                <MudChip Variant="Variant.Outlined" Color="Color.Success" Text="@attachFile.postAttachmentName"></MudChip>
                            }
                        </MudChipSet>
                    </div>
                </div>
                <div class="row">
                    <dic class="col-md">
                        <MudButton ButtonType="ButtonType.Submit" Color="Color.Primary"
                                   Variant="Variant.Filled" Size="Size.Small" StartIcon="@Icons.Material.Filled.Save" OnClick="SavePost">
                            Save
                        </MudButton>
                        <MudButton ButtonType="ButtonType.Button" Color="Color.Success"
                                   Variant="Variant.Filled" Size="Size.Small" OnClick="() => dlgForm!.HideAsync()">
                            Cancel
                        </MudButton>
                    </dic>
                </div>
                <!--<div class="row mb-2">









                    <div class="row mb-2">
                        <div class="col-md">

                        </div>
                        <div class="col-md" style="padding-top:24px;">

                        </div>
                    </div>

                    <div>

                    </div>
                </div>
                <div>
                    <div class="row mb-2">
                        <div class="col-md-2">

                        </div>-->
                @*   <div class="col-md-4">
                    @{
                    int totalAttachment = 0;
                    if (attachments != null)
                    {
                    if (attachments.Any())
                    {
                    totalAttachment = attachments.Count;
                    }
                    }
                    <MudText Typo="@Typo.caption" Style="padding-top: 5px;">Total Attachments : @totalAttachment</MudText>
                    }
                    </div> *@
                <!--</div>-->
                @*       <MudStack Row="true">
                    <MudPaper Class="pa-3">
                    @if (attachments != null)
                    {
                    @foreach (var attachFile in attachments!)
                    {
                    <MudChip Label="true" Color="Color.Warning" Style="color:black;" Size="Size.Small">
                    <MudIconButton Icon="@Icons.Material.Filled.Delete" Color="Color.Primary" OnClick="(() => removeAttachFile(attachFile.Name))" />@attachFile.Name &nbsp;<code>@attachFile.Size bytes</code>
                    </MudChip>
                    }
                    }
                    </MudPaper>
                    </MudStack> *@
                <!--</div>
                <div style="height:10px;"></div>
                <div class="row mb-2">
                    <div class="col-md-4">

                    </div>
                    <div class="col-md-4">

                    </div>
                </div>
                <div style="height:12px;"></div>
                <div class="row">
                    <div class="mb-2 col-md">

                    </div>
                </div>-->
            </div>
        </Content>
    </DialogTemplates>
    <DialogEvents Opened="@DialogOpen"></DialogEvents>
</SfDialog>


<div class="row mb-2">
    <div class="col-md">
        <MudButton Size="Size.Small" Color="Color.Primary" StartIcon="@Icons.Material.Filled.Add" Variant="Variant.Filled" OnClick="OpenCreateForm">Add</MudButton>
        <MudButton Size="Size.Small" Color="Color.Success" StartIcon="@Icons.Material.Filled.Edit" Variant="Variant.Filled" OnClick="OpenEditForm">Edit</MudButton>
        <MudButton Size="Size.Small" Color="Color.Warning" StartIcon="@Icons.Material.Filled.Delete" Variant="Variant.Filled" OnClick="ConfirmDelete">Delete</MudButton>
        <MudButton Size="Size.Small" Color="Color.Info" StartIcon="@Icons.Material.Filled.Group" Variant="Variant.Filled" OnClick="openAssignRole">Assign Roles</MudButton>
    </div>
</div>

<div class="row">
    <div class="mb-2 col-md">
        @if (allPosts.Any())
        {
            <SfGrid @ref="dgMain" DataSource="allPosts" AllowSelection="true" AllowFiltering="true" AllowSorting="true" Width="100%">
                <GridFilterSettings Type="Syncfusion.Blazor.Grids.FilterType.Excel"></GridFilterSettings>
                <GridColumns>
                    <GridColumn AutoFit="true" Visible="false" HeaderText="ID" Field="@nameof(PostDTO.postId)"></GridColumn>
                    <GridColumn Width="450" HeaderText="Title" Field="@nameof(PostDTO.postName)"></GridColumn>
                    <GridColumn AutoFit="true" HeaderText="Publish Date" Field="@nameof(PostDTO.postPublishDate)" Format="dd-MMM-yyyy"></GridColumn>
                    <GridColumn AutoFit="true" HeaderText="Expiry Date" Field="@nameof(PostDTO.postExpiryDate)" Format="dd-MMM-yyyy"></GridColumn>
                    <GridColumn AutoFit="true" HeaderText="Status" Field="@nameof(PostDTO.postStatus)"></GridColumn>
                    <GridColumn AutoFit="true" HeaderText="Created By" Field="@nameof(PostDTO.CreatedBy)"></GridColumn>
                    <GridColumn AutoFit="true" HeaderText="Created Date" Field="@nameof(PostDTO.CreatedDate)" Format="dd-MMM-yyyy h:mm:ss tt"></GridColumn>
                    <GridColumn AutoFit="true" HeaderText="Modified By" Field="@nameof(PostDTO.ModifiedBy)"></GridColumn>
                    <GridColumn AutoFit="true" HeaderText="Modified Date" Field="@nameof(PostDTO.ModifiedDate)" Format="dd-MMM-yyyy h:mm:ss tt"></GridColumn>
                </GridColumns>
            </SfGrid>
        }
    </div>
</div>

<SfDialog @ref="dlgFormRole" Visible="false" Width="500px" ShowCloseIcon="true" CloseOnEscape="true" IsModal="true">
    <DialogTemplates>
        <Header>Assign Roles</Header>
        <Content>
            <div class="row" style="display:flex">
                <MudText Typo="Typo.caption">Selected Post: <b>@postObj.postName</b></MudText>
            </div>
            <div style="height:10px;"></div>
            <div class="row" style="text-align:right;">
                <SfCheckBox @bind-Checked="isChecked" Label="Select All" @onchange="selectAll">Select All</SfCheckBox>
            </div>
            <div style="height:10px;"></div>

            <div class="row">
                <SfGrid @ref="dgMainRole" Height="400px" DataSource="allRole" AllowSelection="true" AllowFiltering="true" AllowSorting="true" Width="500px">
                    <GridFilterSettings Type="Syncfusion.Blazor.Grids.FilterType.Excel"></GridFilterSettings>
                    <GridColumns>
                        <GridColumn AutoFit="true" Visible="false" HeaderText="Id" Field="@nameof(RoleDTO.Id)"></GridColumn>
                        <GridColumn Width="350" HeaderText="Roles" Field="@nameof(RoleDTO.Name)"></GridColumn>
                        <GridColumn Field="@nameof(RoleDTO.isSelect)" HeaderTextAlign="TextAlign.Center" HeaderText="Selected" Width="120" ClipMode="ClipMode.EllipsisWithTooltip">
                            <Template>
                                @{
                                    var ctx = context as RoleDTO;
                                    <div>
                                        <center>
                                            <SfCheckBox @bind-Checked="@ctx.isSelect"></SfCheckBox>
                                        </center>
                                    </div>
                                }
                            </Template>
                        </GridColumn>
                    </GridColumns>
                </SfGrid>

            </div>
            <div class="page-panel">
                <div>
                    <MudButton Size="Size.Small" Color="Color.Primary" StartIcon="@Icons.Material.Filled.Save" Variant="Variant.Filled" OnClick="SaveUserRole">Save</MudButton>
                    <MudButton Size="Size.Small" Color="Color.Success" StartIcon="@Icons.Material.Filled.Cancel" Variant="Variant.Filled" OnClick="() => dlgFormRole!.HideAsync()">Cancel</MudButton>
                </div>
            </div>
        </Content>
    </DialogTemplates>
</SfDialog>

@code {
    SfDialog? dlgForm, dlgFormRole;
    IList<IBrowserFile> files = new List<IBrowserFile>();
    DateTime? date = DateTime.Today;
    private List<RoleDTO> allRole = new();
    private RoleDTO Role = new();
    SfGrid<RoleDTO>? dgMainRole;
    private List<PostDTO> allPosts = new();
    private SfGrid<PostDTO> dgMain;
    private PostDTO postObj = new();
    private List<PostSearchTagDTO> postSearchTags = new();
    private string srhTag;
    SfRichTextEditor rTxt;
    private string profilePicThumb = "images/upload.jpg";
    private string profilePicHdr = "images/upload.jpg";
    private IBrowserFile? imgfileThumb;
    private IBrowserFile? imgfileHdr;
    private List<IBrowserFile>? attachments = new();
    private List<PostAttachmentDTO> attachmentDTO = new();
    private SfToast? toastObj = new();
    private bool spinner;
    private bool isPublish = true;
    bool isChecked;
    bool IsFormEditable = true;
    public List<DivisionDto> divisionsList { get; set; } = new();
    public List<DepartmentDto> departmentsList { get; set; } = new();

    private async void selectAll(ChangeEventArgs args)
    {
        try
        {
            foreach (var item in allRole)
            {
                item.isSelect = isChecked;
            }

            StateHasChanged();
        }
        catch (Exception ex)
        {
            var tm = new ToastModel { Content = ex.Message, Title = "Error", ShowCloseButton = true, Timeout = 0, CssClass = "e-toast-danger", ShowProgressBar = true };
            await toastObj!.ShowAsync(tm);
        }
    }

    private async Task SaveUserRole()
    {
        try
        {
            await service.savePostRolesInPostScreen(allRole, postObj.postId);
            var tm = new ToastModel { Content = "Roles assigned successfully.", Title = "Info", ShowCloseButton = true, Timeout = 4000, CssClass = "e-toast-success", ShowProgressBar = true };
            await toastObj!.ShowAsync(tm);
            await dlgFormRole!.HideAsync();
        }
        catch (Exception ex)
        {
            var tm = new ToastModel { Content = ex.Message, Title = "Error", ShowCloseButton = true, Timeout = 4000, CssClass = "e-toast-danger", ShowProgressBar = true };
            await toastObj!.ShowAsync(tm);
        }
    }

    private async Task openAssignRole()
    {
        try
        {
            if (dgMain!.SelectedRecords is { Count: > 0 })
            {
                var pId = dgMain.SelectedRecords[0].postId;
                allRole = await service.GetAllRolesAsyncWithSelectedPost(pId);
                postObj = await service.getPostWithId(pId);
                await dlgFormRole!.ShowAsync();
            }
            else
            {
                var tm = new ToastModel { Content = "Please select post.", Title = "Info", ShowCloseButton = true, Timeout = 5000, ShowProgressBar = true, CssClass = "e-toast-danger" };
                await toastObj!.ShowAsync(tm);
            }
        }
        catch (Exception ex)
        {
            var tm = new ToastModel { Content = ex.Message, Title = "Error", ShowCloseButton = true, Timeout = 5000, ShowProgressBar = true, CssClass = "e-toast-danger" };
            await toastObj!.ShowAsync(tm);
        }
    }

    private void DialogOpen()
    {
        rTxt.RefreshUI();
    }

    private async void OpenEditForm()
    {
        if (dgMain?.SelectedRecords.Count > 0)
        {
            if (dgMain?.SelectedRecords[0].postStatus != "Expired")
            {
                var pId = dgMain.SelectedRecords[0].postId;
                postObj = await service.getPostWithId(pId);
                await FillDepartmentByDivisionName(postObj.JobDivisionName ?? "");
                isPublish = postObj.postPublishDate != null && !(postObj.postPublishDate >= DateTime.Today);
                IsFormEditable = postObj.postStatus is "Draft" or "Published";
                if (postObj.postPublishDate == null)
                {
                    isPublish = true;
                }
                else
                {
                    isPublish = postObj.postPublishDate >= DateTime.Today;
                }

                IsFormEditable = postObj.postStatus is "Draft" or "Published";

                profilePicThumb = string.IsNullOrEmpty(postObj.postThumbnailPath) ? "images/upload.jpg" : AppDataService.ConvertToUrl(postObj.postThumbnailPath);
                profilePicHdr = string.IsNullOrEmpty(postObj.postHeadImagePath) ? "images/upload.jpg" : AppDataService.ConvertToUrl(postObj.postHeadImagePath);
                postSearchTags = await service.getPostSrhTagsWithPostId(pId);
                attachmentDTO = await service.getPostAttachmentWithPostId(pId);
                await dlgForm!.ShowAsync();
            }
            else
            {
                var tm = new ToastModel
                {
                    Content = "Post is expired, you can not edit.",
                    Title = "Info",
                    ShowCloseButton = true,
                    Timeout = 5000,
                    CssClass = "e-toast-danger",
                    ShowProgressBar = true
                };
                await toastObj!.ShowAsync(tm);
            }
        }
    }

    public void Closed(MudChip chip)
    {
        if (!IsFormEditable) return;
        var p = postSearchTags.SingleOrDefault(x => x.postSearchTagName == chip.Text)!;
        postSearchTags.Remove(p);
        StateHasChanged();
    }

    public void ClosedAttachment(MudChip chip)
    {
        if (!IsFormEditable) return;
        var a = attachmentDTO!.SingleOrDefault(x => x.postAttachmentName == chip.Text);
        attachmentDTO.Remove(a);
        StateHasChanged();
    }

    private void OpenCreateForm()
    {
        isPublish = true;


        postObj = new PostDTO { postExpiryDate = DateTime.Today.AddDays(60), JobDepartmentName = "", JobDivisionName = "", JobCityId = null};
        postSearchTags = new List<PostSearchTagDTO>();
        profilePicThumb = "images/upload.jpg";
        profilePicHdr = "images/upload.jpg";
        imgfileThumb = null;
        imgfileHdr = null;
        attachments = new List<IBrowserFile>();
        attachmentDTO = new List<PostAttachmentDTO>();
        files = new List<IBrowserFile>();
        IsFormEditable = true;
        departmentsList = new();
        dlgForm!.ShowAsync();

    }

    private async void ConfirmDelete()
    {
        try
        {
            if (dgMain!.SelectedRecords is { Count: > 0 })
            {
                var conf = await DialogService.ConfirmAsync("Are you sure want to delete this record", "Confirm");
                if (conf)
                {
                    var Id = dgMain.SelectedRecords[0].postId;
                    service.deletePost(Id);
                    allPosts = await service.getAllPosts((int)Section.InternalVacancies);
                    StateHasChanged();
                }
            }
        }
        catch (Exception ex)
        {
            ToastModel t = new() { Title = "Error", Content = ex.Message, ShowProgressBar = true, Timeout = 5000, CssClass = "e-toast-danger" };
            await toastObj!.ShowAsync(t);
        }
    }

    protected override async Task OnInitializedAsync()
    {
        allPosts = await service.getAllPosts((int)Section.InternalVacancies);
        _jobDepartmentsList = await service.GetJobsDepartments();
        _jobCitiesList = await service.GetJobCities();
        _jobTypesList = await service.GetJobTypes();
        divisionsList = await cService.GetDivisions();
        divisionsList = divisionsList.Where(c => c.Name != " - Any Division -").ToList();
        //_jobTypesList = await service.GetJobTypes();
    }

    private async void UploadAttachments(IReadOnlyList<IBrowserFile> files)
    {
        foreach (var file in files)
        {
            attachments!.Add(file);
            var postTitle = Guid.NewGuid().ToString();
            var dirPath = $"{env.WebRootPath}\\Posts\\{postTitle}\\Attachments";
            if (Directory.Exists(dirPath) == false)
            {
                Directory.CreateDirectory(dirPath);
            }

            var path = dirPath + "\\" + file.Name;
            PostAttachmentDTO p = new()
            {
                postAttachmentDocURL = path,
                postAttachmentName = file.Name,
                postAttachmentDocSize = file.Size.ToString(),
                postAttachmentDocType = file.ContentType,
                postAttachmentDocExt = Path.GetExtension(file.Name)
            };
            attachmentDTO.Add(p);

            var stream = file.OpenReadStream(30 * 1024 * 1024);
            var fs = File.Create(path);
            await stream.CopyToAsync(fs);
            stream.Close();
            fs.Close();
        }

        StateHasChanged();
    }

    private async void UploadThumbnail(IBrowserFile file)
    {
        imgfileThumb = file;
        var memoryStream = new MemoryStream();
        await using var fileStream = file.OpenReadStream(long.MaxValue);
        await fileStream.CopyToAsync(memoryStream);
        var bytes = memoryStream.ToArray();
        profilePicThumb = "data:image/png;base64," + Convert.ToBase64String(bytes);
        StateHasChanged();
    }

    private async void UploadHeaderImage(IBrowserFile file)
    {
        imgfileHdr = file;
        var memoryStream = new MemoryStream();
        await using (var fileStream = file.OpenReadStream(long.MaxValue))
        {
            await fileStream.CopyToAsync(memoryStream);
            var bytes = memoryStream.ToArray();
            profilePicHdr = "data:image/png;base64," + Convert.ToBase64String(bytes);
            StateHasChanged();
        }
    }

    private readonly List<ToolbarItemModel> _tools =
    [
        new() { Command = ToolbarCommand.Bold },
        new() { Command = ToolbarCommand.Italic },
        new() { Command = ToolbarCommand.Underline },
        new() { Command = ToolbarCommand.Separator },
        new() { Command = ToolbarCommand.Formats },
        new() { Command = ToolbarCommand.Alignments },
        new() { Command = ToolbarCommand.OrderedList },
        new() { Command = ToolbarCommand.UnorderedList },
        new() { Command = ToolbarCommand.Separator },
        // new ToolbarItemModel() { Command = ToolbarCommand.CreateLink },
        // new ToolbarItemModel() { Command = ToolbarCommand.Image },
        // new ToolbarItemModel() { Command = ToolbarCommand.Separator },
        // new ToolbarItemModel() { Command = ToolbarCommand.SourceCode },
        // new ToolbarItemModel() { Command = ToolbarCommand.Separator },
        new() { Command = ToolbarCommand.Undo },
        new() { Command = ToolbarCommand.Redo }
    ];

    private void addSrhTag()
    {
        if (!string.IsNullOrEmpty(srhTag))
        {
            PostSearchTagDTO p = new()
            {
                postSearchTagName = srhTag,
                postSearchTagID = -1
            };
            postSearchTags.Add(p);
            srhTag = "";
            StateHasChanged();
        }
    }

    private void removeSrhTag(string tagName)
    {
        var p = postSearchTags.SingleOrDefault(x => x.postSearchTagName == tagName)!;
        postSearchTags.Remove(p);
        StateHasChanged();
    }

    private void removeAttachFile(string fileName)
    {
        var a = attachmentDTO!.SingleOrDefault(x => x.postAttachmentName == fileName);
        attachmentDTO.Remove(a);
        StateHasChanged();
    }

    private async Task<string> saveImageThumb(string postTitle, string fileName)
    {
        if (imgfileThumb != null && profilePicThumb != "images/upload.jpg")
        {
            postTitle = Guid.NewGuid().ToString();
            fileName = postTitle;
            var dirPath = $"{env.WebRootPath}\\Posts\\{postTitle}";
            if (Directory.Exists(dirPath) == false)
            {
                Directory.CreateDirectory(dirPath);
            }

            var stream = imgfileThumb.OpenReadStream(long.MaxValue);
            var path = dirPath + "\\" + fileName + "-thumb.png";
            var fs = File.Create(path);
            await stream.CopyToAsync(fs);
            stream.Close();
            fs.Close();
            imgfileThumb = null;
            return await Task.FromResult(path);
        }

        return await Task.FromResult(profilePicThumb);

        return await Task.FromResult("");
    }

    private async Task<string> saveImageHDR(string postTitle, string fileName)
    {
        if (imgfileHdr != null && profilePicHdr != "images/upload.jpg")
        {
            postTitle = Guid.NewGuid().ToString();
            fileName = postTitle;
            var dirPath = $"{env.WebRootPath}\\Posts\\{postTitle}";
            if (Directory.Exists(dirPath) == false)
            {
                Directory.CreateDirectory(dirPath);
            }

            var stream = imgfileHdr.OpenReadStream(long.MaxValue);
            var path = dirPath + "\\" + fileName + "-hdr.png";

            var fs = File.Create(path);
            await stream.CopyToAsync(fs);
            stream.Close();
            fs.Close();
            imgfileHdr = null;
            return await Task.FromResult(path);
        }

        return await Task.FromResult(profilePicHdr);
        return await Task.FromResult("");
    }

    private async Task<string> saveAttachments(string postTitle)
    {
        postTitle = Guid.NewGuid().ToString();
        //fileName = postTitle;
        var dirPath = $"{env.WebRootPath}\\Posts\\{postTitle}\\Attachments";
        if (Directory.Exists(dirPath) == false)
        {
            Directory.CreateDirectory(dirPath);
        }

        if (attachments!.Any())
        {
            foreach (var attachfile in attachments!)
            {
                var path = dirPath + "\\" + attachfile.Name;
                PostAttachmentDTO p = new();
                p.postAttachmentDocURL = path;
                p.postAttachmentName = attachfile.Name;
                p.postAttachmentDocSize = attachfile.Size.ToString();
                p.postAttachmentDocType = attachfile.ContentType;
                p.postAttachmentDocExt = Path.GetExtension(attachfile.Name);
                attachmentDTO.Add(p);

                var stream = attachfile.OpenReadStream(30 * 1024 * 1024);
                var fs = File.Create(path);
                await stream.CopyToAsync(fs);
                stream.Close();
                fs.Close();
                // await using FileStream fs = new(path, FileMode.Create);
                // await attach file.OpenReadStream(long.MaxValue).CopyToAsync(fs);
            }

            return await Task.FromResult("");
        }

        return await Task.FromResult("");
    }

    private async Task SavePost()
    {
        if (string.IsNullOrEmpty(postObj.postName))
        {
            await ShowErrorMessage("Post title is required");
            return;
        }

        if (string.IsNullOrEmpty(postObj.postContentText))
        {
            await ShowErrorMessage("Post Content is required");
            return;
        }


        if (string.IsNullOrEmpty(postObj.postRemarks))
        {
            await ShowErrorMessage("Post Remarks is required");
            return;
        }

        if (string.IsNullOrEmpty(postObj.JobDivisionName))
        {
            await ShowErrorMessage("Division is Required");
            return;

        }

        if (string.IsNullOrEmpty(postObj.JobDepartmentName))
        {
            await ShowErrorMessage("Department is Required");
            return;
        }

        if (postObj.JobCityId == null)
        {
            await ShowErrorMessage("City is required");
            return;
        }

        if (postObj.JobTypeId == null)
        {
            await ShowErrorMessage("Job Type is required");
            return;
        }

        if (postObj.JobLastDateToApply == null)
        {
            await ShowErrorMessage("Last date to Apply is required");
            return;
        }

        if (postSearchTags.Count == 0)
        {
            await ShowErrorMessage("At least one search tag is required");
            return;
        }

        try
        {
            spinner = true;
            StateHasChanged();

            var msg = await service.checkPublishExpiryDate(postObj.postPublishDate ?? DateTime.Now, postObj.postExpiryDate ?? DateTime.Now, postObj.postId);
            if (msg != "")
            {
                ToastModel t1 = new()
                {
                    Title = "Information",
                    Content = msg,
                    ShowProgressBar = true,
                    ShowCloseButton = true,
                    Timeout = 5000,
                    CssClass = "e-toast-danger"
                };
                await toastObj!.ShowAsync(t1);
                spinner = false;
                return;
            }

            if (string.IsNullOrEmpty(postObj.postName) || postObj.postName == "" || postObj.postContentText == "")
            {
                ToastModel t1 = new()
                {
                    Title = "Information",
                    Content = "Title, Description fields is required..",
                    ShowProgressBar = true,
                    ShowCloseButton = true,
                    Timeout = 5000,
                    CssClass = "e-toast-danger"
                };
                await toastObj!.ShowAsync(t1);
                spinner = false;
                return;
            }

            var contentText = await rTxt.GetTextAsync();
            var postName = postObj.postName + "-" + DateTime.Now.ToString("ddMMMyyyyHHmmssff");
            postObj.postThumbnailPath = await saveImageThumb("", "");
            postObj.postHeadImagePath = await saveImageHDR("", "");
            postObj.postIconImagePath = "";
            //await saveAttachments("");

            var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
            var user = authState.User;
            var userId = "";
            if (user.Identity is { Name: not null })
            {
                userId = user.Identity.Name;
            }

            await service.savePost(postObj, postSearchTags, attachmentDTO, -1, contentText, userId, (int)Section.InternalVacancies);

            allPosts = await service.getAllPosts((int)Section.InternalVacancies);

            postObj = new PostDTO();
            postSearchTags = new List<PostSearchTagDTO>();
            profilePicThumb = "images/upload.jpg";
            profilePicHdr = "images/upload.jpg";
            imgfileThumb = null;
            imgfileHdr = null;
            attachments = new List<IBrowserFile>();
            attachmentDTO = new List<PostAttachmentDTO>();
            files = new List<IBrowserFile>();

            spinner = false;
            StateHasChanged();

            ToastModel t = new() { Title = "Information", Content = "Record Saved successfully.", ShowProgressBar = true, Timeout = 5000, CssClass = "e-toast-success" };
            await toastObj.ShowAsync(t);

            await dlgForm.HideAsync();

            //nav.NavigateTo("/");
        }
        catch (Exception ex)
        {
            var msg = "";
            msg = ex.Message;
            if (ex.InnerException != null)
                msg += " - Detail: " + ex.InnerException.Message;

            ToastModel t = new() { Title = "Error", Content = msg, Timeout = 5000, ShowProgressBar = true, ShowCloseButton = true, CssClass = "e-toast-danger" };
            await toastObj.ShowAsync(t);
        }
    }

    private async Task ShowErrorMessage(string mm)
    {
        var tm = new ToastModel { Title = "Error", Content = mm, ShowCloseButton = true, ShowProgressBar = true, Timeout = 5000, CssClass = "e-toast-danger" };
        await toastObj!.ShowAsync(tm);
    }


    private List<JobDepartmentDto> _jobDepartmentsList = new();
    private List<JobCityDto> _jobCitiesList = new();
    private List<JobTypeDto> _jobTypesList = new();
    private string? currentDepartment;
    private string? currentDivision;


    private async Task FillDepartmentByDivisionName(string name)
    {
        var div = divisionsList.FirstOrDefault(m => m.Name == name);
        departmentsList = div == null ? new() : await cService.GetDepartments(div.Code);
    }

    private async Task FillDepartments(ChangeEventArgs<string?, DivisionDto> obj)
    {
        if (obj is { ItemData: not null })
        {
            var code = (obj.ItemData.Code ?? "").Trim();

            //currentDivision = obj.ItemData.Code ?? "";
            departmentsList = string.IsNullOrEmpty(code) ? new List<DepartmentDto>() : await cService.GetDepartments(code);
            //departmentsList.Insert(0, new DepartmentDto() { Code = " ", Name = " - Any Department -" });
            currentDepartment = " ";
        }
    }

}

<style>
    #banner {
        position: fixed;
        left: 50%;
        margin-left: -240px;
        width: 480px;
        height: 115px;
        border-radius: 20px;
        text-align: center;
    }


    .e-grid td.e-active {
        background: #faa601 !important;
    }
</style>