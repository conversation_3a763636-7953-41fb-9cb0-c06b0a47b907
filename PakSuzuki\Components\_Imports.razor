﻿@using System.Net.Http
@using System.Net.Http.Json
@using Microsoft.AspNetCore.Components.Forms
@using Microsoft.AspNetCore.Components.Routing
@using Microsoft.AspNetCore.Components.Web
@using static Microsoft.AspNetCore.Components.Web.RenderMode
@using Microsoft.AspNetCore.Components.Web.Virtualization
@using Microsoft.JSInterop
@using PakSuzuki
@using PakSuzuki.Components
@using Microsoft.EntityFrameworkCore
@using Syncfusion.Blazor
@* @using Syncfusion *@
@using MudB<PERSON>zor
@using Syncfusion.Blazor.DropDowns
@using Syncfusion.Blazor.Popups
@using Syncfusion.Blazor.TreeGrid 
@using Syncfusion.Blazor.Inputs 
@using Syncfusion.Blazor.Buttons
@using Syncfusion.Blazor.Navigations 
@using Syncfusion.Blazor.Notifications 
@using Syncfusion.Blazor.Grids
@using Syncfusion.Blazor.RichTextEditor
@using Syncfusion.Blazor.Calendars
 
@using Smart.Blazor
@using Bit.BlazorUI 
@using PakSuzuki.DTO
@using PakSuzuki.Services
 
@inject SfDialogService DialogService

@using Microsoft.AspNetCore.Components.Authorization