﻿namespace PakSuzuki.Services;

public class TransformationDataService(ApplicationDbContext dc)
{
    // first get all user roles then get all sections by role and then 
    // get all section based on roles

    public Task<List<SectionDto>> GetUserSection(string userId, int moduleId)
    {
        // Get all relevant data in a single query using joins
        var sections = (from ur in dc.UserRoles
                        join sr in dc.SectionRoles on ur.RoleId equals sr.RoleId
                        join s in dc.Sections on sr.SectionId equals s.SectionId
                        where ur.User.UserId == userId
                              && s.ModuleId == moduleId
                              && s.IsActive == true
                              && s.Module.IsActive==true
                        select new SectionDto
                        {
                            Id = s.SectionId,
                            Name = s.SectionTitle,
                            Description = s.SectionDescription,
                            Module = s.Module.ModuleTitle,
                            ModuleId = s.ModuleId,
                            SortOrder = s.SortOrder,
                            PostType = s.PostType.PostTypeTitle,
                            PostTypeId = s.PostTypeId,
                            isSelect = true
                        })
                       .Distinct() // Remove duplicates if a user has multiple roles with same section
                       .OrderBy(s => s.SortOrder)
                       .ToList();

        return Task.FromResult(sections);
    }
    // get module name by module id
    public Task<string?> GetModuleName(int moduleId)
    {
        return Task.FromResult( dc.Modules.FirstOrDefault(m => m.Id == moduleId)?.ModuleTitle);
    }
}