﻿@page "/admin/sections"
@using PakSuzuki.Services
@using PakSuzuki.DTO
@using PakSuzuki.Model
@using Syncfusion.Blazor.Lists
@inject SfDialogService DialogService

@inject AdminDataService service

<SfToast @ref="toastObj"></SfToast>

<div class="row mb-2">
    <div class="col-md">
        <MudText Typo="Typo.h5">Sections</MudText>
    </div>
</div>
<div class="row mb-2">
    <div class="col-md">
        <MudButton Size="Size.Small" Color="Color.Primary" StartIcon="@Icons.Material.Filled.Add" Variant="Variant.Filled" OnClick="showHideSection">Show/Hide</MudButton>
        <MudButton Size="Size.Small" Color="Color.Info" StartIcon="@Icons.Material.Filled.Group" Variant="Variant.Filled" OnClick="openAssignRole">Assign Roles</MudButton>
        @* <MudButton Size="Size.Small" Color="Color.Success" StartIcon="@Icons.Material.Filled.Edit" Variant="Variant.Filled" OnClick="OpenEditForm">Edit</MudButton>
        <MudButton Size="Size.Small" Color="Color.Warning" StartIcon="@Icons.Material.Filled.Delete" Variant="Variant.Filled" OnClick="ConfirmDelete">Delete</MudButton> *@
    </div>
</div>
<div class="row">
    <div class="mb-2 col-md">
        @if (sectionLists.Any())
        {
            <SfGrid @ref="dgMain" Height="calc(100vh -220px)" DataSource="sectionLists" AllowSelection="true" AllowFiltering="true" AllowSorting="true" Width="100%">
                <GridFilterSettings Type="Syncfusion.Blazor.Grids.FilterType.Excel"></GridFilterSettings>
                <GridColumns>
                    <GridColumn AutoFit="true" Visible="false" HeaderText="ID" Field="@nameof(SectionDto.Id)"></GridColumn>
                    <GridColumn Width="250" HeaderText="Title" Field="@nameof(SectionDto.Name)"></GridColumn>
                    <GridColumn Width="250" AutoFit="true" HeaderText="Description" Field="@nameof(SectionDto.Description)"></GridColumn>
                    <GridColumn AutoFit="true" HeaderText="Module" Field="@nameof(SectionDto.Module)"></GridColumn>
                    <GridColumn AutoFit="true" HeaderText="Active" Field="@nameof(SectionDto.IsActive)"></GridColumn>
                </GridColumns>
            </SfGrid>
        }
    </div>
</div>

<SfDialog @ref="dlgFormRole" Visible="false" Width="500px" ShowCloseIcon="true" CloseOnEscape="true" IsModal="true">
    <DialogTemplates>
        <Header>Assign Roles</Header>
        <Content>
            <div class="row" style="display:flex">
                <MudText Typo="Typo.caption">Selected Section: <b>@selSection.Name</b></MudText>
            </div>
            <div style="height:10px;"></div>
            <div class="row" style="text-align:right;">
                <SfCheckBox @bind-Checked="isChecked" Label="Select All" @onchange="selectAll">Select All</SfCheckBox>
            </div>
            <div style="height:10px;"></div>
            <div class="row">
                <SfGrid @ref="dgMainRole" Height="400px" DataSource="allRole" AllowSelection="true" AllowFiltering="true" AllowSorting="true" Width="500px">
                    <GridFilterSettings Type="Syncfusion.Blazor.Grids.FilterType.Excel"></GridFilterSettings>
                    <GridColumns>
                        <GridColumn AutoFit="true" Visible="false" HeaderText="Id" Field="@nameof(RoleDTO.Id)"></GridColumn>
                        <GridColumn Width="350" HeaderText="Roles" Field="@nameof(RoleDTO.Name)"></GridColumn>
                        <GridColumn Field=@nameof(RoleDTO.isSelect) HeaderTextAlign="TextAlign.Center" HeaderText="Selected" Width="120" ClipMode="ClipMode.EllipsisWithTooltip">
                            <Template>
                                @{
                                    var ctx = (context as RoleDTO);
                                    <div>
                                        <center><SfCheckBox @bind-Checked="@ctx.isSelect"></SfCheckBox></center>
                                    </div>
                                }
                            </Template>
                        </GridColumn>
                    </GridColumns>
                </SfGrid>

            </div>
            <div class="page-panel">
                <div>
                    <MudButton Size="Size.Small" Color="Color.Primary" StartIcon="@Icons.Material.Filled.Save" Variant="Variant.Filled" OnClick="SaveUserRole">Save</MudButton>
                    <MudButton Size="Size.Small" Color="Color.Success" StartIcon="@Icons.Material.Filled.Cancel" Variant="Variant.Filled" OnClick="(() => dlgFormRole!.HideAsync())">Cancel</MudButton>
                </div>
            </div>
        </Content>
    </DialogTemplates>
</SfDialog>

@code {
    SfGrid<RoleDTO>? dgMainRole;
    private string FormTitle = "Sections";
    private SfToast? toastObj;
    SfGrid<SectionDto>? dgMain;
    List<SectionDto> sectionLists = new();
    SfDialog? dlgFormRole;
    private List<RoleDTO> allRole = new();
    private RoleDTO Role = new();
    private SectionDto selSection = new();
    bool isChecked = false;
    bool IsFormEditable = true;
    private async void selectAll(Microsoft.AspNetCore.Components.ChangeEventArgs args)
    {
        try
        {
            foreach (var item in allRole)
            {
                item.isSelect = isChecked;
            }
            this.StateHasChanged();
        }
        catch (Exception ex)
        {
            ToastModel tm = new ToastModel() { Content = ex.Message.ToString(), Title = "Error", ShowCloseButton = true, Timeout = 0 };
            await toastObj!.ShowAsync(tm);
        }
    }

    private async Task SaveUserRole()
    {
        try
        {
            await service.savePostRolesInSectionScreen(allRole, selSection.Id);
            ToastModel tm = new ToastModel() { Content = "Roles assigned successfully.", Title = "Info", ShowCloseButton = true, Timeout = 0 };
            await toastObj!.ShowAsync(tm);
            await dlgFormRole!.HideAsync();
            selSection = new();
        }
        catch (Exception ex)
        {
            ToastModel tm = new ToastModel() { Content = ex.Message.ToString(), Title = "Error", ShowCloseButton = true, Timeout = 0 };
            await toastObj!.ShowAsync(tm);
        }
    }

    private async Task openAssignRole()
    {
        try
        {
            if (dgMain!.SelectedRecords is { Count: > 0 })
            {
                int pId = dgMain.SelectedRecords[0].Id;
                allRole = await service.GetAllRolesAsyncWithSelectedSection(pId);
                selSection = await service.getSectionWithId(pId);
                await dlgFormRole!.ShowAsync();
            }
            else
            {
                ToastModel tm = new ToastModel() { Content = "Please select user.", Title = "Info", ShowCloseButton = true, Timeout = 0 };
                await toastObj!.ShowAsync(tm);
            }
        }
        catch (Exception ex)
        {
            ToastModel tm = new ToastModel() { Content = ex.Message.ToString(), Title = "Error", ShowCloseButton = true, Timeout = 0 };
            await toastObj!.ShowAsync(tm);
        }
    }

    private async Task showHideSection()
    {
        try
        {
            if (dgMain!.SelectedRecords is { Count: > 0 })
            {
                int sectionId = dgMain.SelectedRecords[0].Id;
                await service.showHideSection(sectionId);
                sectionLists = await service.getSections();
            }
            else
            {
                ToastModel tm = new ToastModel() { Content = "Please select user.", Title = "Info", ShowCloseButton = true, Timeout = 0 };
                await toastObj!.ShowAsync(tm);
            }
        }
        catch (Exception ex)
        {
            ToastModel tm = new ToastModel() { Content = ex.Message.ToString(), Title = "Error", ShowCloseButton = true, Timeout = 0 };
            await toastObj!.ShowAsync(tm);
        }
    }

    protected override async Task OnInitializedAsync()
    {
        try
        {
            sectionLists = await service.getSections();
        }
        catch (Exception ex)
        {
            ToastModel tm = new ToastModel() { Content = ex.Message.ToString(), Title = "Error", ShowCloseButton = true, Timeout = 0 };
            await toastObj!.ShowAsync(tm);
        }
    }
}

<style>
    .e-grid td.e-active {
        background: #faa601 !important;
    }

</style>