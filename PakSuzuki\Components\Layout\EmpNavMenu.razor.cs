﻿namespace PakSuzuki.Components.Layout;

public partial class EmpNavMenu
{
    private string? employee_shift_code;
    private string? location_code;
    private SfDialog locationDialog;
    private string? userCode = "000000";
    private string? userDepartment = string.Empty;
    private string? userDesignation = "";
    private string? userEmail = string.Empty;
    private string? userLocation = "";
    private List<MenuDTO> userMenu = new();
    private string? userName = "";
    private string? userShift = "";
    private string? userShiftTitle = "";
    private string? tooltip = "";

    protected override async Task OnInitializedAsync()

    {
        await base.OnInitializedAsync();

        var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        var user = authState.User;
        if (user.Identity is { IsAuthenticated: true, Name: not null })
            userMenu = await service.GetUserMenus(user.Identity.Name, 2);


        var userPrincipalName = user.Identity?.Name;
        if (userPrincipalName != null)
        {
            var EmpData = cpService.GetEmployeeData(userPrincipalName);
            if (EmpData.Rows.Count > 0)
            {
                userName = EmpData.Rows[0]["FullName"].ToString();
                userDesignation = EmpData.Rows[0]["JobTitle"].ToString();
                userLocation = EmpData.Rows[0]["location_text"].ToString();
                userShift = EmpData.Rows[0]["employee_shift_code"].ToString();
                userCode = EmpData.Rows[0]["empcode"].ToString();
                location_code = EmpData.Rows[0]["location_code"].ToString();
                employee_shift_code = EmpData.Rows[0]["employee_shift_code"].ToString();
                userEmail = EmpData.Rows[0]["employee_official_email"].ToString();
                userDepartment = EmpData.Rows[0]["department_name"].ToString();
                tooltip = EmpData.Rows[0]["tooltip"].ToString();
                // userShiftTitle = "--";

                userLocation = "Head Office & Plant";
                userShiftTitle = "7:15 AM to 5:15 PM";
            }

            (userLocation, userShiftTitle) =
                await cpService.GetShiftAndLocationInfo(location_code, employee_shift_code);
        }
    }
}