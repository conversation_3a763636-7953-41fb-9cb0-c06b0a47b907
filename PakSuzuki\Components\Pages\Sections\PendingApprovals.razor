﻿@page "/approvals"
@using Microsoft.AspNetCore.Authorization
@using PakSuzuki.DTO 
@inject AuthenticationStateProvider AuthenticationStateProvider
@inject CorporateService cService
@attribute [Authorize]

<MudText Typo="Typo.h4">Pending Approvals</MudText>

@if(approvalsList.Any())
{
    <SfGrid DataSource="@approvalsList" AllowFiltering="true">
        <GridFilterSettings Type="Syncfusion.Blazor.Grids.FilterType.Excel"></GridFilterSettings>
        <GridPageSettings PageSize="10"></GridPageSettings>
        <GridColumns>
            <GridColumn Field=@nameof(PendingApprovalDto.ProjectCode) AutoFit="true" HeaderText="Project Code"></GridColumn>
            <GridColumn Field=@nameof(PendingApprovalDto.ProjectName) AutoFit="true" HeaderText="Project Name"></GridColumn>
            <GridColumn Field=@nameof(PendingApprovalDto.Direct) AutoFit="true" HeaderText="Direct"></GridColumn>
            <GridColumn Field=@nameof(PendingApprovalDto.Indirect) AutoFit="true" HeaderText="Indirect"></GridColumn>
            <GridColumn Field=@nameof(PendingApprovalDto.Total) AutoFit="true" HeaderText="Total"></GridColumn>
            <GridColumn HeaderText="Link">
                <Template>
                    @{
                        var approval = (context as PendingApprovalDto);
                        <a href="@approval.Link" target="_blank">View</a>
                    }
                </Template>
            </GridColumn>
        </GridColumns>
    </SfGrid>
}
else
{
    <MudText Typo="Typo.h3">No Approvals Pending</MudText>
}

@code { 

    private List<PendingApprovalDto> approvalsList = new List<PendingApprovalDto>();

    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();
        var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        var user = authState.User;
        var userId = "";
        if (user.Identity is { Name: not null })
        {
            userId = user.Identity.Name;
        }

        approvalsList = await cService.GetEmpPendingApprovals(userId);
    }
}
