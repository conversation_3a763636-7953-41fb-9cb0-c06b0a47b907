﻿@page "/setup/importmenus"
@inject AuthenticationStateProvider AuthenticationStateProvider
@inject AppDataService Service
@inject ISnackbar SnackBar
<h3>ImportMenus</h3>

<MudFileUpload T="IBrowserFile" Accept=".xlsx" MaxFiles="1" OnFilesChanged="OnInputFileChanged">
    <ButtonTemplate>
        <MudButton HtmlTag="label"
                   Variant="Variant.Filled"
                   Color="Color.Primary"
                   Size="Size.Small"
                   StartIcon="@Icons.Material.Filled.CloudUpload"
                   for="@context">
            Browse CSV
        </MudButton>
    </ButtonTemplate>
</MudFileUpload>

<MudButton Color="Color.Secondary" OnClick="UploadFile" Disabled="@_isUploading" Variant="Variant.Filled" Size="Size.Small">
    @if (_isUploading)
    {
        <MudProgressCircular Class="ms-n1" Size="Size.Small" Indeterminate="true" />
        <MudText Class="ms-2">Uploading</MudText>
    }
    else
    {
        <MudText>Upload to Server</MudText>
    }
</MudButton>

@code {

    private IBrowserFile _file;
    private bool _isUploading;
    private void OnInputFileChanged(InputFileChangeEventArgs e)
    {
        _file = e.File;
    }



    private async Task UploadFile()
    {
        // Save file in CanteenMenu folder. if folder does not exist then first create it
        if (_file == null)
        {
            return;
        }
        _isUploading = true;

        //save stream to excel file in CanteenMenu folder
        //if folder does not exist then create it
        var folder = System.IO.Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "menusfiles");
        if (!Directory.Exists(folder))
        {
            Directory.CreateDirectory(folder);
        }

        var filePath = System.IO.Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "menusfiles", _file.Name);

        // if file already exist on "filePath" then delete it
        if (File.Exists(filePath))
        {
            File.Delete(filePath);
        }

        await using (var stream = new FileStream(filePath, FileMode.Create))
        {

            await _file.OpenReadStream().CopyToAsync(stream);
        }

        var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        var user = authState.User;
        var userId = "";
        if (user.Identity is { Name: not null })
        {
            userId = user.Identity.Name;
        }

        var msg = await Service.PopulateApplicationMenus(filePath, userId);

        if (msg != "OK")
        {
            SnackBar.Add(msg, Severity.Error);


        }
        else
        {
            SnackBar.Add("Excel File Uploaded", Severity.Info);

        }


        


        _isUploading = false;


    }

}
