﻿<style>
    .frm-fluid {
        width: 100%;
        height:320px;
        object-fit: cover;
        margin-top: 15px;
        border-radius: 10px;
        /*decent drop shadow and a good border outline */
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1), 0 1px 3px rgba(0, 0, 0, 0.08);
        border: 1px solid rgba(0, 0, 0, 0.125);
        
    }
</style>
@if (Items != null && Items.Any())
{
    // display first header image from the post as banner
    var firstPost = Items.FirstOrDefault();
    string style = "";
    if (!string.IsNullOrEmpty(firstPost.PostWidth))
    {
        style += "width:" + firstPost.PostWidth + ";";
    }
    if (!string.IsNullOrEmpty(firstPost.PostHeight))
    {
        style += "min-height:" + firstPost.PostHeight + ";";
    }
    if (firstPost != null && firstPost.PostExternalLink != null)
    {
        // display external link as iframe

            <iframe src="@firstPost.PostExternalLink" class="frm-fluid" title="@Title" style="@style"></iframe>
        
    }
}

@code {
    [Parameter] public List<PostDTO>? Items { get; set; }
    [Parameter] public string? Title { get; set; }
}
