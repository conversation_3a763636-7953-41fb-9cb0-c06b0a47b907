﻿namespace PakSuzuki.Services
{
    public class EmpSelfServiceDataService(ApplicationDbContext dc)
    {
        public Task<List<SelfServiceLinkDto>> GetAllLinks()
        {
            var q = (from a in dc.EmployeeSelfServiceLinks
                     select new SelfServiceLinkDto
                     {
                         Id = a.Id,
                         Text = a.Text,
                         Link = a.Link,
                         Icon = a.Icon,
                         IsActive = a.IsActive
                     }).OrderBy(a => a.Text);
            return Task.FromResult(q.ToList());
        }
        public Task<SelfServiceLinkDto> GetLinkById(int id)
        {
            var q = (from a in dc.EmployeeSelfServiceLinks
                     where a.Id == id
                     select new SelfServiceLinkDto
                     {
                         Id = a.Id,
                         Text = a.Text,
                         Link = a.Link,
                         Icon = a.Icon,
                         IsActive = a.IsActive
                     }).First();
            return Task.FromResult(q);
        }
        // Save and Update Link
        public Task<int> SaveLink(SelfServiceLinkDto link, string userId)
        {
            if (link.Id == 0)
            {
                var newLink = new EmployeeSelfServiceLink
                {
                    Text = link.Text,
                    Link = link.Link,
                    Icon = link.Icon,
                    IsActive = link.IsActive, CreatedBy = userId, CreatedDate = DateTime.Now
                };
                dc.EmployeeSelfServiceLinks.Add(newLink);
                dc.SaveChanges();
                return Task.FromResult(newLink.Id);
            }

            // check record exist
            var existLink = dc.EmployeeSelfServiceLinks.Find(link.Id);
            if (existLink == null)
            {
                return Task.FromResult(0);
            }
            existLink.Text = link.Text;
            existLink.Link = link.Link;
            existLink.Icon = link.Icon;
            existLink.IsActive = link.IsActive;
            existLink.ModifiedBy = userId;
            existLink.ModifiedDate = DateTime.Now;
            dc.SaveChanges();
            return Task.FromResult(link.Id);

        }
        public Task<int> DeleteLink(int id)
        {
            var q = (from a in dc.EmployeeSelfServiceLinks
                     where a.Id == id
                     select a).FirstOrDefault();
            if (q == null)
            {
                return Task.FromResult(0);
            }
            dc.EmployeeSelfServiceLinks.Remove(q);
            dc.SaveChanges();
            return Task.FromResult(id);
            //var link = dc.EmployeeSelfServiceLinks.Find(id);
            //if (link == null)
            //{
            //    return Task.FromResult(0);
            //}
            //dc.EmployeeSelfServiceLinks.Remove(link);
            //dc.SaveChanges();
            //return Task.FromResult(id);
        }
    }

}
