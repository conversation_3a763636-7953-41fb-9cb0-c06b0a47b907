﻿@using PakSuzuki.DTO
@using FilterType = Syncfusion.Blazor.DropDowns.FilterType
@inject AppDataService Service
@inject CorporateService cService

<style>
    .emp-directory-card {
        background-color: white;
        padding: 20px;
        border-radius: 10px;
        box-shadow: var(--mud-elevation-1);
        border-color: #bbb;
    }
    .header {
        display:flex;
        justify-content: space-between;
    }

</style> 
<div class="emp-directory-card">
    <div class="header mb-3">
        <h3 class="sec-title">Employee Dynamic Search</h3>
        @if (!string.IsNullOrEmpty(cc.Attachment))
        {
            <a class="view-all" target="og1" href="@cc.Attachment">Company Organogram</a>

        }
    </div>
    <div class="row mb-3">
        <div class="col-md">
            @* SfDropDownList DataSource=divisionsList, bind-value=currentDivision, AllowFilter, TItem=DivisionDto, TValue=string? *@
            @if (divisionsList.Any())
            {
                <SfDropDownList FloatLabelType="FloatLabelType.Auto"
                                AllowFiltering="true" FilterType="FilterType.Contains"
                                TValue="string?" TItem="DivisionDto" Placeholder="Division" DataSource="@divisionsList" @bind-Value="currentDivision">
                    <DropDownListFieldSettings Text="Name" Value="Code"></DropDownListFieldSettings>
                    <DropDownListEvents TValue="string?" TItem="DivisionDto" ValueChange="FillDepartments"></DropDownListEvents>
                    @*<DropDownListEvents TValue="DivisionDto" TItem="string?" ValueChange="FillDepartments"> </DropDownListEvents>*@
                </SfDropDownList>
            }

            @*<SfDropDownList CssClass="e-outline" TValue="string" TItem="DivisionDto" Placeholder="Division" AllowFiltering="true"
                            @bind-Value="currentDivision" ShowClearButton="true"
                            TValue="string?"
                            DataSource="divisionsList">
                <DropDownListFieldSettings Text="@nameof(DivisionDto.Name)" Value="@nameof(DivisionDto.Code)"></DropDownListFieldSettings>
                 On Change re-fill department list 
                <DropDownListEvents TValue="DivisionDto" TItem="string?" ValueChange="FillDepartments"> </DropDownListEvents>
            </SfDropDownList>*@

        </div>
        <div class="col-md mb-3">
            @if(departmentsList.Any())
            {
                <SfDropDownList FloatLabelType="FloatLabelType.Auto"
                                AllowFiltering="true" FilterType="FilterType.Contains"
                                TValue="string?" TItem="DepartmentDto" Placeholder="Department" DataSource="@departmentsList" @bind-Value="currentDepartment">
                    <DropDownListFieldSettings Text="Name" Value="Code"></DropDownListFieldSettings>
                </SfDropDownList>
            }

            @*<SfDropDownList CssClass="e-outline" TValue="string" TItem="DepartmentDto" Placeholder="Departments" AllowFiltering="true"
                            @bind-Value="currentDepartment"
                            TValue="string?"
                            DataSource="departmentsList">
                <DropDownListFieldSettings Text="@nameof(DepartmentDto.Name)" Value="@nameof(DepartmentDto.Code)"></DropDownListFieldSettings>
            </SfDropDownList>*@
        </div>
    </div>
    <div class="row mb-3">
        <div class="col-md">

            <SfTextBox CssClass="e-outline" Placeholder="Employee Search" @bind-Value="searchText" FloatLabelType="FloatLabelType.Auto"></SfTextBox>
        </div>
    </div>
    <div class="row mb-3">
        <div class="col-md" style="display:flex">

            <MudButton Color="Color.Primary" Variant="Variant.Filled" Size="Size.Medium" Style="width: 100%; background-color: #013582"
                       @onclick="SearchEmployee"
                       StartIcon="@Icons.Material.Filled.PersonSearch">Search</MudButton>
        </div>
    </div>
</div>

<SfDialog IsModal="true" ShowCloseIcon="true" CloseOnEscape="true" Width="800px"
          @ref="dlgSearchResult" Visible="IsSearchResultVisible">
    <DialogTemplates>
        <Header>Search Result</Header>
        <Content>
            @if (contacts.Any())
            {
                <div class="row" style="max-height: calc(100vh - 200px); overflow-y: auto;">
                    @foreach (var cc in contacts)
                    {
                        <div class="col-md-6 mb-4">
                            <MudCard Elevation="3">
                                <MudCardHeader>
                                    <CardHeaderContent>
                                        <MudText Typo="Typo.h5">@cc.Name</MudText>
                                        <MudText Typo="Typo.body2">@cc.Designation</MudText>
                                    </CardHeaderContent>
                                </MudCardHeader>
                                <MudCardContent>
                                    <MudText><strong>Email:</strong> @cc.Email</MudText>
                                    <MudText><strong>Employee No:</strong> @cc.EmpNo</MudText>
                                    <MudText><strong>Department:</strong> @cc.Department</MudText>
                                    <MudText><strong>Cell:</strong> @cc.EmpCell</MudText>
                                </MudCardContent>
                                
                            </MudCard>
                        </div>
                    }
                </div>
            }
            else
            {
                <MudText Typo="Typo.h3">No Record Found</MudText>
            }
        </Content>
    </DialogTemplates>
</SfDialog>


@code {
    private List<DivisionDto> divisionsList = new List<DivisionDto>();
    private List<DepartmentDto> departmentsList = new List<DepartmentDto>();
    private string? currentDivision = " ";
    private string? currentDepartment = " ";
    private string searchText = "";
    private bool IsSearchResultVisible = false;
    private SfDialog dlgSearchResult;

    private CalenderDto cc = new CalenderDto();
    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();
        divisionsList = await cService.GetDivisions();
        departmentsList = new List<DepartmentDto>();
        departmentsList.Insert(0, new DepartmentDto() { Code = " ", Name = " - Any Department -" });
        currentDepartment = " ";

        cc = await Service.GetOrganogram();


    }

    private List<EmpContactInfo> contacts = new List<EmpContactInfo>();
    private async Task SearchEmployee()
    {
        //Search Employee
        contacts = await cService.GetEmployeeContacts(searchText, currentDivision, currentDepartment);
        await dlgSearchResult.ShowAsync();
    }

    //private async Task FillDepartments<string, DivisionDto>(ChangeEventArgs<TValue, TItem> obj)
    //{
    //    // if currentDivision is empty, then set departmentList to empty
    //    var code = (currentDivision ?? "").Trim();
    //    if (string.IsNullOrEmpty(code) )
    //    {
    //        departmentsList = new List<DepartmentDto>();
    //        return;
    //    }
    //    else
    //    {
    //        departmentsList = await cService.GetDepartments(code);
    //    }
    //    divisionsList.Insert(0, new DivisionDto() { Code = " ", Name = " - Any Department -"});
    //}

    private async Task FillDepartments(ChangeEventArgs<string?, DivisionDto> obj)
    {
        var code = (obj.ItemData.Code ?? "").Trim();

        //currentDivision = obj.ItemData.Code ?? "";
        departmentsList = string.IsNullOrEmpty(code) ? new List<DepartmentDto>() : await cService.GetDepartments(code);
        departmentsList.Insert(0, new DepartmentDto() { Code = " ", Name = " - Any Department -" });
        currentDepartment = " ";
    }

}

