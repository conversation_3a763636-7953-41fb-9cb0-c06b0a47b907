﻿@page "/setup/meetingroomapprovals"
@using Syncfusion.Blazor.Schedule
@inject IJSRuntime js
@inject MeetingRoomService service

@using PakSuzuki.DTO
@using ButtonType = MudBlazor.ButtonType
@using FilterType = Syncfusion.Blazor.DropDowns.FilterType
@inject AuthenticationStateProvider AuthenticationStateProvider

<div class="row">
    <div class="col mb-2" style="display:flex;gap:10px;align-items:center">
        <MudText Typo="Typo.h5">Meeting Room Requisitions Approval</MudText>

        <MudButton StartIcon="@Icons.Material.Filled.Refresh" Color="Color.Primary" Size="Size.Small"
                   @onclick="RefreshList">Refresh</MudButton>
    </div>
</div>

<div class="row">
    <div class="col-md mb-2">
        <SfDropDownList DataSource="@AllLocations" FloatLabelType="FloatLabelType.Always" Placeholder="Location"
                        AllowFiltering="true" FilterType="FilterType.Contains"
                        ShowClearButton="true"
                        @bind-Value="filterLocationId" TValue="int?" TItem="OfficeLocationDto">
            <DropDownListFieldSettings Text="@nameof(OfficeLocationDto.Location)" Value="@nameof(OfficeLocationDto.Id)"></DropDownListFieldSettings>
            <DropDownListEvents TValue="int?" TItem="OfficeLocationDto" ValueChange="OnFilterLocationChange"></DropDownListEvents>
        </SfDropDownList>
    </div>
    <div class="col-md mb-2">
        
            <SfDropDownList DataSource="@AllFilterMeetingRooms" FloatLabelType="FloatLabelType.Always" Placeholder="Meeting Room"
                            AllowFiltering="true" FilterType="FilterType.Contains"
                            ShowClearButton="true"
                            @bind-Value="filterMeetingRoomId" TValue="int?" TItem="MeetingRoomDto">
                <DropDownListFieldSettings Text="@nameof(MeetingRoomDto.MeetingRoomName)" Value="@nameof(MeetingRoomDto.Id)"></DropDownListFieldSettings>
                <DropDownListEvents TValue="int?" TItem="MeetingRoomDto" ValueChange="OnFilterMeetingRoomChange"></DropDownListEvents>
            </SfDropDownList>
        
    </div>
</div>
<SfDialog @ref="@dialog" Width="600px" ShowCloseIcon="true" IsModal="true" Visible="IsFormVisible">
    <DialogTemplates>
        <Header>Request Detail</Header>
        <Content>
            <EditForm Model="request" OnValidSubmit="SaveRequest">
                <DataAnnotationsValidator />
                <ValidationSummary />
                <div class="row">
                    <div class="col">
                        <SfTextBox @bind-Value="request.Subject" Placeholder="Subject" FloatLabelType="FloatLabelType.Always"></SfTextBox>
                    </div>
                </div>
                @* Date From and Date To *@
                <div class="row">
                    <div class="col">
                        @* Syncfusion Date Time picker *@
                        <SfDateTimePicker Placeholder="From Date/Time" FloatLabelType="FloatLabelType.Always"
                                          ShowClearButton="true" ShowTodayButton="true"
                                          Min="DateTime.Now"
                                          @bind-Value="request.StartTime" Format="d-MMM-yyyy hh:mm tt"></SfDateTimePicker>
                    </div>
                    <div class="col">
                        <SfDateTimePicker Placeholder="Tp Date/Time" FloatLabelType="FloatLabelType.Always"
                                          Min="DateTime.Now"
                                          ShowClearButton="true" ShowTodayButton="true"
                                          @bind-Value="request.EndTime" Format="d-MMM-yyyy hh:mm tt"></SfDateTimePicker>
                    </div>
                </div>
                @* Location and Meeting Room Dropdown Selections *@
                <div class="row">
                    <div class="col">
                        <SfDropDownList DataSource="@AllLocations" FloatLabelType="FloatLabelType.Always" Placeholder="Location"
                                        AllowFiltering="true" FilterType="FilterType.Contains"
                                        @bind-Value="request.LocationId" TValue="int?" TItem="OfficeLocationDto">
                            <DropDownListFieldSettings Text="@nameof(OfficeLocationDto.Location)" Value="@nameof(OfficeLocationDto.Id)"></DropDownListFieldSettings>
                            <DropDownListEvents TValue="int?" TItem="OfficeLocationDto" ValueChange="OnLocationChange"></DropDownListEvents>
                        </SfDropDownList>
                    </div>
                    <div class="col">
                        <SfDropDownList DataSource="@AllMeetingRooms" FloatLabelType="FloatLabelType.Always" Placeholder="Meeting Room"
                                        AllowFiltering="true" FilterType="FilterType.Contains"
                                        @bind-Value="request.MeetingRoomId" TValue="int?" TItem="MeetingRoomDto">
                            <DropDownListFieldSettings Text="@nameof(MeetingRoomDto.MeetingRoomName)" Value="@nameof(MeetingRoomDto.Id)"></DropDownListFieldSettings>
                        </SfDropDownList>
                    </div>
                </div>
                <div class="row">
                    <div class="col">
                        <MudButton ButtonType="ButtonType.Submit"
                                   Size="Size.Small" Color="Color.Primary" Variant="Variant.Filled">Save</MudButton>
                    </div>
                </div>
            </EditForm>
        </Content>
    </DialogTemplates>
</SfDialog>


@* create syncfusion tabs has two tabs first is Schedule View and Second is Detail View *@


<SfTab>
    <TabItems>
        <Syncfusion.Blazor.Navigations.TabItem>
            <ChildContent>
                <TabHeader Text="Schedule View"></TabHeader>

            </ChildContent>
            <ContentTemplate>
                <SfSchedule TValue="MeetingRoomRequestDto" Height="calc(100vh - 205px)" Width="100%" AllowDragAndDrop="false"
                            Timezone="Asia/Karachi"
                            AllowMultiDrag="false" AllowResizing="false" Readonly="true">
                    <ScheduleQuickInfoTemplates TemplateType="TemplateType.Both">
                        <HeaderTemplate>
                            @{
                                if (context is MeetingRoomRequestDto obj)
                                {
                                    <h1 style="padding:5px; color:white;background: #0022df">@obj.Subject</h1>
                                }
                            }
                        </HeaderTemplate>
                        <ContentTemplate>
                            @{
                                if (context is MeetingRoomRequestDto obj)
                                {
                                    <div class="row">
                                        <div class="col">
                                            <SfTextBox @bind-Value="obj.MeetingRoomName"
                                                       Placeholder="Meeting Room"
                                                       FloatLabelType="FloatLabelType.Always"
                                                       Readonly="true"></SfTextBox>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col">
                                            <SfTimePicker @bind-Value="obj.StartTime" Format="hh:mm"
                                                          Placeholder="Start Time"
                                                          FloatLabelType="FloatLabelType.Always"
                                                          Readonly="true"></SfTimePicker>
                                        </div>
                                        <div class="col">
                                            <SfTimePicker @bind-Value="obj.EndTime" Format="hh:mm tt"
                                                          Placeholder="End Time"
                                                          FloatLabelType="FloatLabelType.Always"
                                                          Readonly="true"></SfTimePicker>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col">
                                            <SfTextBox @bind-Value="obj.CreatedByUserId"
                                                       Placeholder="Request By (Code)"
                                                       FloatLabelType="FloatLabelType.Always"
                                                       Readonly="true"></SfTextBox>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col">
                                            <SfTextBox @bind-Value="obj.RequestedByName"
                                                       Placeholder="Request By"
                                                       FloatLabelType="FloatLabelType.Always"
                                                       Readonly="true"></SfTextBox>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col">
                                            <SfTextBox @bind-Value="obj.RequestedByEmail"
                                                       Placeholder="Request By (Email)"
                                                       FloatLabelType="FloatLabelType.Always"
                                                       Readonly="true"></SfTextBox>
                                        </div>
                                    </div>
                                }
                            }
                        </ContentTemplate>
                        <FooterTemplate>

                        </FooterTemplate>
                    </ScheduleQuickInfoTemplates>
                    <ScheduleEventSettings DataSource="@MyBRRequests"></ScheduleEventSettings>

                    <ScheduleViews>
                        <ScheduleView Option="View.Day"></ScheduleView>
                        <ScheduleView Option="View.Week"></ScheduleView>
                        <ScheduleView Option="View.WorkWeek" IsSelected="true"></ScheduleView>
                        <ScheduleView Option="View.Month"></ScheduleView>
                        <ScheduleView Option="View.Agenda"></ScheduleView>
                    </ScheduleViews>
                </SfSchedule>

            </ContentTemplate>
        </Syncfusion.Blazor.Navigations.TabItem>
        <Syncfusion.Blazor.Navigations.TabItem>
            <ChildContent>
                <TabHeader Text="All Requests"></TabHeader>
            </ChildContent>
            <ContentTemplate>
                <SfGrid DataSource="@MyBRRequests" AllowFiltering="true" AllowSorting="true">
                    <GridFilterSettings Type="Syncfusion.Blazor.Grids.FilterType.Excel"></GridFilterSettings>
                    <GridColumns>

                        <GridColumn Field="@nameof(MeetingRoomRequestDto.Subject)" HeaderText="Subject" AutoFit="true"></GridColumn>
                        <GridColumn Field="@nameof(MeetingRoomRequestDto.StartTime)" Format="d-MMM-yyyy h:mm tt" HeaderText="Start Time" AutoFit="true"></GridColumn>
                        <GridColumn Field="@nameof(MeetingRoomRequestDto.EndTime)" Format="d-MMM-yyyy h:mm tt" HeaderText="End Time" AutoFit="true"></GridColumn>
                        <GridColumn Field="@nameof(MeetingRoomRequestDto.OfficeLocation)" HeaderText="Location" AutoFit="true"></GridColumn>
                        <GridColumn Field="@nameof(MeetingRoomRequestDto.MeetingRoomName)" HeaderText="Meeting Room" AutoFit="true"></GridColumn>
                        <GridColumn Field="@nameof(MeetingRoomRequestDto.MeetingRoomStatus)" HeaderText="Status" AutoFit="true"></GridColumn>
                        <GridColumn Field="@nameof(MeetingRoomRequestDto.CreatedByUserId)" HeaderText="Requested By" AutoFit="true"></GridColumn>
                        <GridColumn HeaderText="Actions" AutoFit="true">
                            <Template Context="cc">
                                @{
                                    if (cc is MeetingRoomRequestDto mm)
                                    {
                                        if (mm.MeetingRoomStatus == "Pending")
                                        {
                                            if (mm.CreatedByUserId == userId)
                                            {
                                                <MudButton Color="Color.Primary" Size="Size.Small" OnClick="@(() => ApproveRequest(mm.Id))" Variant="Variant.Filled">Approve</MudButton>
                                                <MudButton OnClick="@(()=>CancelRequest(mm.Id))" Color="Color.Error" Size="Size.Small" Variant="Variant.Filled">Reject</MudButton>

                                            }
                                        }
                                        else if (mm.MeetingRoomStatus == "Approved")
                                        {
                                            <MudButton OnClick="@(()=>CancelRequest(mm.Id))" Color="Color.Error" Size="Size.Small" Variant="Variant.Filled">Reject</MudButton>
                                        }
                                    }
                                }
                            </Template>
                        </GridColumn>
                    </GridColumns>
                </SfGrid>
            </ContentTemplate>
        </Syncfusion.Blazor.Navigations.TabItem>

    </TabItems>
</SfTab>





@code {
    private SfDialog dialog;
    private MeetingRoomRequestDto request = new MeetingRoomRequestDto();
    public bool IsFormVisible { get; set; } = false;
    public List<OfficeLocationDto> AllLocations { get; set; } = new List<OfficeLocationDto>();
    public List<MeetingRoomDto> AllMeetingRooms { get; set; } = new List<MeetingRoomDto>();
    public int? filterLocationId { get; set; }
    public List<MeetingRoomDto> AllFilterMeetingRooms { get; set; } = new();
    public int? filterMeetingRoomId { get; set; }
    private bool isEvent = false;

    List<MeetingRoomRequestDto> MyBRRequests = new();
    //    List<MeetingRoomRequestDto>
    //{
    //    new MeetingRoomRequestDto { Id = 1, Subject = "Paris", StartTime = new DateTime(2024, 9, 13, 10, 0, 0) , EndTime = new DateTime(2024, 9, 13, 12, 0, 0) },
    //    new MeetingRoomRequestDto { Id = 2, Subject = "Germany", StartTime = new DateTime(2024, 9, 14, 10, 0, 0) , EndTime = new DateTime(2024, 9, 15, 12, 0, 0) }
    //};

    // on initialize
    private string userId = "";
    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();

        var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        var user = authState.User;
        userId = "";
        if (user.Identity is { Name: not null })
        {
            userId = user.Identity.Name;
        }


        AllLocations = await service.GetLocations(userId);
        AllMeetingRooms = new();
        AllFilterMeetingRooms = new();
    }

    private async Task OpenRequestForm()
    {
        await dialog.ShowAsync();
    }

    private async Task SaveRequest()
    {
        // Save the request logic here
        var res = await service.SaveRequest(request, userId);
        if (res == "OK")
        {

            await dialog.HideAsync();
            MyBRRequests = await service.GetAllBookingRequests(request.MeetingRoomId ?? 0);
        }
        else
        {

        }

    }

    private async Task OnLocationChange(ChangeEventArgs<int?, OfficeLocationDto> obj)
    {
        //throw new NotImplementedException();
        request.LocationId = obj.Value;
        request.MeetingRoomId = null;
        //AllMeetingRooms = obj.Value == null ? [] : await service.GetAllMeetingRooms(obj.Value ?? 0);
        if (obj.Value == null)
        {
            AllMeetingRooms = new();
        }
        else
        {
            AllMeetingRooms = await service.GetMeetingRoomsForApprover(userId, obj.Value ?? 0);
        }
    }

    private int tempLocationId = 0;
    private async Task OnFilterLocationChange(ChangeEventArgs<int?, OfficeLocationDto> obj)
    {
        //AllFilterMeetingRooms = obj.Value == null ? [] : await service.GetAllMeetingRooms(obj.Value ?? 0, userId);
        if (obj.Value == null)
        {
            AllFilterMeetingRooms = new List<MeetingRoomDto>();
            tempLocationId = 0;
        }

        else
        {
            AllFilterMeetingRooms = await service.GetMeetingRoomsForApprover(userId ?? "", obj.ItemData.Id);
            tempLocationId = obj.ItemData.Id;
        }
        MyBRRequests = new();
    }

    private async Task OnFilterMeetingRoomChange(ChangeEventArgs<int?, MeetingRoomDto> obj)
    {
        if (obj.Value != null)
        {
            MyBRRequests = await service.GetAllBookingRequests(obj.Value ?? 0);
        }
        else
        {
            MyBRRequests = new();
        }
    }

    private async Task OpenCreateForm()
    {
        request = new();
        request.StartTime = DateTime.Today.AddHours(8);
        request.EndTime = DateTime.Today.AddHours(10);
        await dialog.ShowAsync();
    }

    private async Task OpenEditForm(MeetingRoomRequestDto mm)
    {
        request = await service.GetRequestById(mm.Id);
        await dialog.ShowAsync();
    }

    private async Task CancelRequest(int requestId)
    {
        //var conf = await DialogService.ConfirmAsync("Are you sure want to cancel this meeting", "Confirm");
        string reason = await DialogService.PromptAsync("Reason/Comment", "Reason/Comment");
        if (!string.IsNullOrEmpty(reason))
        {

            var res = await service.RejectRequest(requestId, userId, reason);
            MyBRRequests = await service.GetAllBookingRequests(filterMeetingRoomId ?? 0);
        }
    }

    private async Task RefreshList()
    {
        MyBRRequests = await service.GetAllBookingRequests(filterMeetingRoomId ?? 0);
    }

    private async Task ApproveRequest(int requestId)
    {
        var res = await service.ApproveMeetingRoomRequest(requestId, userId);
        MyBRRequests = await service.GetAllBookingRequests(filterMeetingRoomId ?? 0);
    }
}
