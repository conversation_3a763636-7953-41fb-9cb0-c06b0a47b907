@inject AuthenticationStateProvider AuthenticationStateProvider

@inject PollDataService service
<link href="/styles/poll.css" rel="stylesheet"/>
<style>
    .p-item {
        /*border: 1px solid green;*/
        display: inline-block !important;
        padding: 8px;
    }

    
    
</style>
<div class="row mb-3">
    <div class="col-md">
        <h3 class="sec-title">Employees Opinion </h3>
    </div>
</div>
<div class="survey-dashboard">
    <BitCarousel Style="height:460px;width:100%" VisibleItemsCount="3" ScrollItemsCount="3" ShowNextPrev="false">
        @foreach (var itm in AllPolls)
        {
            <BitCarouselItem Class="p-item">
                <CompPollCard Survey="@itm"/>
            </BitCarouselItem>
        }

    </BitCarousel>
    @*@foreach (var poll in AllPolls)
        {
            <CompPollCard Survey="@poll"/>

        }*@
</div>


@code {
    private List<PollQuestionDto> AllPolls = new();

    protected override async Task OnInitializedAsync()
    {
        await FillPolls();
    }


    private async Task FillPolls()
    {
        var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        var user = authState.User;

        if (user.Identity is { Name: not null })
        {
            AllPolls = await service.GetUsersPolls(user.Identity.Name);
        }

        StateHasChanged();
    }

}