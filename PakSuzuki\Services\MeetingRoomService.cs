﻿namespace PakSuzuki.Services;

public class MeetingRoomService(ApplicationDbContext dc)
{
    // get all locations GetAllOfficeLocations(List<OfficeLocationDto>)
    public Task<List<OfficeLocationDto>> GetAllOfficeLocations()
    {
        return Task.FromResult(dc.OfficeLocations.Select(x => new OfficeLocationDto
        {
            Id = x.Id,
            Location = x.Location,
            IsActive = x.IsActive
        }).ToList());

    }
    // get all boardroom of a location GetAllMeetingRooms(int officeLocationId)=>Task<List<MeetingRoomDto>>


    public Task<List<MeetingRoomDto>> GetAllMeetingRooms(int officeLocationId)
    {
        var q = (from a in dc.MeetingRooms
                     //join b in dc.MeetingRoomApprovars on a.Id equals b.MeetingRoomId
                 where //b.UserId == userId && 
                 a.IsActive && a.OfficeLocation.IsActive && a.OfficeLocationId == officeLocationId
                 orderby a.MeetingRoomName
                 select new MeetingRoomDto
                 {
                     Id = a.Id,
                     IsActive = a.IsActive,
                     MeetingRoomName = a.MeetingRoomName,
                     OfficeLocation = a.OfficeLocation.Location,
                     OfficeLocationId = a.OfficeLocationId
                 }).ToList();

        return Task.FromResult(q);


    }

    public Task<List<MeetingRoomDto>> GetAllMeetingRooms(int officeLocationId, string userId)
    {
        var q = (from a in dc.MeetingRooms
                 join b in dc.MeetingRoomApprovars on a.Id equals b.MeetingRoomId
                 where b.UserId == userId && a.IsActive && a.OfficeLocation.IsActive && a.OfficeLocationId == officeLocationId
                 orderby a.MeetingRoomName
                 select new MeetingRoomDto
                 {
                     Id = a.Id,
                     IsActive = a.IsActive,
                     MeetingRoomName = a.MeetingRoomName,
                     OfficeLocation = a.OfficeLocation.Location,
                     OfficeLocationId = a.OfficeLocationId
                 }).ToList();

        return Task.FromResult(q);

        //return Task.FromResult(dc.MeetingRooms.Where(x => x.OfficeLocationId == officeLocationId).Select(x => new MeetingRoomDto
        //{
        //    Id = x.Id,
        //    OfficeLocationId = x.OfficeLocationId,
        //    OfficeLocation = x.OfficeLocation.Location,
        //    MeetingRoomName = x.MeetingRoomName,
        //    IsActive = x.IsActive,

        //}).ToList());
    }

    public Task<int> CreateUpdateBoardRoomRequest(MeetingRoomRequestDto request, string userId)
    {
        var q = (from a in dc.MeetingRoomRequests
                 where a.Id == request.Id
                 select a).FirstOrDefault();
        if (q == null)
        {
            q = new MeetingRoomRequest
            {
                MeetingRoomId = request.MeetingRoomId ?? 0,
                MeetingRoomStatusId = (byte)MeetingRoomStatusEnum.Pending,
                CreatedDate = request.StartTime ?? DateTime.Now,
                EndTime = request.EndTime ?? DateTime.Now,
                StartTime = request.StartTime ?? DateTime.Now,
                UserId = userId,
                Notes = request.Subject
            };
            dc.MeetingRoomRequests.Add(q);
            dc.SaveChanges();
            return Task.FromResult(q.Id);
        }
        else
        {
            if (q.MeetingRoomId == (byte)MeetingRoomStatusEnum.Pending)
            {
                q.MeetingRoomId = request.MeetingRoomId ?? 0;
                q.MeetingRoomStatusId = (int)MeetingRoomStatusEnum.Pending;
                q.CreatedDate = request.StartTime ?? DateTime.Now;
                q.EndTime = request.EndTime ?? DateTime.Now;
                q.StartTime = request.StartTime ?? DateTime.Now;
                q.UserId = userId;
                q.Notes = request.Subject;
                dc.SaveChanges();
                return Task.FromResult(q.Id);
            }
            return Task.FromResult(0);
        }
    }
    public Task<string> ApproveRequest(int requestId, string userId)
    {
        var q = (from a in dc.MeetingRoomRequests
                 where a.Id == requestId && a.MeetingRoomStatusId == (byte)MeetingRoomStatusEnum.Pending
                 select a).FirstOrDefault();
        if (q != null)
        {
            q.MeetingRoomStatusId = (byte)MeetingRoomStatusEnum.Approved;
            q.ApproveRejectDate = DateTime.Now;
            q.AdminUserId = userId;
            dc.SaveChanges();
            return Task.FromResult("Request Approved");
        }
        return Task.FromResult("Request Not Found");
    }
    // reject request
    public Task<string> RejectRequest(int requestId, string userId, string reason)
    {
        var q = (from a in dc.MeetingRoomRequests
                 where a.Id == requestId //&& a.MeetingRoomStatusId == (byte)MeetingRoomStatus.Pending
                 select a).FirstOrDefault();
        if (q != null)
        {
            q.MeetingRoomStatusId = (byte)MeetingRoomStatusEnum.Rejected;
            q.Reason = reason;
            q.ApproveRejectDate = DateTime.Now;
            q.AdminUserId = userId;
            dc.SaveChanges();
            return Task.FromResult("Request Rejected");
        }
        return Task.FromResult("Request Not Found");
    }

    // Get all pending boardroom requests

    public Task<List<MeetingRoomRequestDto>> GetAllBookingRequests(int boardroomId)
    {
        var q = (from a in dc.MeetingRoomRequests
                 where a.MeetingRoomId == boardroomId
                 && a.EndTime >= DateTime.Now

                 select new MeetingRoomRequestDto
                 {
                     MeetingRoomId = boardroomId,
                     MeetingRoomName = a.MeetingRoom.MeetingRoomName,
                     OfficeLocation = a.MeetingRoom.OfficeLocation.Location,
                     StartTime = a.StartTime,
                     EndTime = a.EndTime,
                     LocationId = a.MeetingRoom.OfficeLocationId,
                     Location = a.MeetingRoom.OfficeLocation.Location + " - " + a.MeetingRoom.MeetingRoomName,
                     MeetingRoomStatusId = a.MeetingRoomStatusId,
                     Subject = a.Notes,
                     CreatedByUserId = a.UserId,
                     MeetingRoomStatus = a.MeetingRoomStatus.Name,
                     Description = a.Notes,
                     Id = a.Id,
                     IsAllDay = false,
                     CreatedDate = a.CreatedDate

                 }).ToList();
        foreach (var mr in q)
        {
            var mm = (from a in dc.Users
                      where a.UserId == (mr.CreatedByUserId ?? "")
                      select a).FirstOrDefault();
            if (mm != null)
            {
                //mr.Subject; += " - (" + mm.Email + ")";
                mr.RequestedByEmail = mm.Email ?? "";
                mr.RequestedByName = mm.Name ?? "";
            }
        }
        return Task.FromResult(q);


    }                // request cancel by user
    public Task<string> CancelRequest(int requestId, string userId)
    {
        var q = (from a in dc.MeetingRoomRequests
                 where a.Id == requestId && a.UserId == userId
                 select a).FirstOrDefault();
        if (q != null)
        {
            q.MeetingRoomStatusId = (byte)MeetingRoomStatusEnum.Cancelled;
            q.ApproveRejectDate = DateTime.Now;
            //q.ModifiedDate = DateTime.Now;
            dc.SaveChanges();
            return Task.FromResult("Request Canceled");
        }
        return Task.FromResult("Request Not Found");
    }

    public Task<List<MeetingRoomRequestDto>> GeAllUserBookingRequests(int boardRoomId)
    {
        var res = (dc.MeetingRoomRequests
            .Where(x => (x.StartTime >= DateTime.Now || x.EndTime >= DateTime.Now)
            && x.MeetingRoomId == boardRoomId
            && x.MeetingRoomStatusId != (byte)(MeetingRoomStatusEnum.Cancelled)
            && x.MeetingRoomStatusId != (byte)(MeetingRoomStatusEnum.Rejected))
            .Select(x => new MeetingRoomRequestDto
            {
                Id = x.Id,
                MeetingRoomId = x.MeetingRoomId,
                MeetingRoomName = x.MeetingRoom.MeetingRoomName + " - (" + x.UserId + ")",
                OfficeLocation = x.MeetingRoom.OfficeLocation.Location,
                StartTime = x.StartTime,
                EndTime = x.EndTime,
                LocationId = x.MeetingRoom.OfficeLocationId,
                Location = x.MeetingRoom.OfficeLocation.Location + " - " + x.MeetingRoom.MeetingRoomName,
                MeetingRoomStatusId = x.MeetingRoomStatusId,
                //Subject = x.UserId == userId ? x.Notes + " - " + ((MeetingRoomStatus)x.MeetingRoomStatus) .ToString()
                //    : "Booked by Someone else"
                Subject = x.Notes
            }).ToList());
        return Task.FromResult(res);
    }
    public Task<string> ApproveMeetingRoomRequest(int requestId, string userId)
    {
        var q = (from a in dc.MeetingRoomRequests
                 where a.Id == requestId // && a.MeetingRoomStatusId == (byte)MeetingRoomStatus.Pending
                 select a).FirstOrDefault();
        if (q != null)
        {
            q.MeetingRoomStatusId = (byte)MeetingRoomStatusEnum.Approved;
            q.ApproveRejectDate = DateTime.Now;
            q.AdminUserId = userId;
            dc.SaveChanges();
            return Task.FromResult("Request Approved");
        }
        return Task.FromResult("Request Not Found");
    }
    public Task<List<MeetingRoomRequestDto>> GetUserBookingRequests(string userId, int boardRoomId)
    {

        var res = (from x in dc.MeetingRoomRequests
                   where x.EndTime >= DateTime.Now
                   && x.MeetingRoomStatus.Name != "Cancelled"
                   //&& x.MeetingRoomStatus.Name != "Rejected"
                   && x.MeetingRoomId == boardRoomId && x.UserId == userId
                   select new MeetingRoomRequestDto
                   {
                       Id = x.Id,
                       MeetingRoomId = x.MeetingRoomId,
                       MeetingRoomName = x.MeetingRoom.MeetingRoomName,
                       OfficeLocation = x.MeetingRoom.OfficeLocation.Location,
                       StartTime = x.StartTime,
                       EndTime = x.EndTime,
                       LocationId = x.MeetingRoom.OfficeLocationId,
                       Location = x.MeetingRoom.OfficeLocation.Location + " - " + x.MeetingRoom.MeetingRoomName,
                       MeetingRoomStatusId = x.MeetingRoomStatusId,
                       MeetingRoomStatus = x.MeetingRoomStatus.Name,
                       Subject = x.Notes + $" ({x.MeetingRoomStatus.Name})",
                       CreatedByUserId = x.UserId,
                       Reason = x.Reason,
                       CssClass = x.MeetingRoomStatus.Name == "Approved" ? "myapproved" :
                                  x.MeetingRoomStatus.Name == "Pending" ? "mypending" :
                                  x.MeetingRoomStatus.Name == "Rejected" ? "myreject" : ""
                   }
                   ).ToList();
        // union all other requests by other users whose status is approved
        var approvedList = (from x in dc.MeetingRoomRequests
                            where x.EndTime >= DateTime.Now
                            && (x.MeetingRoomStatus.Name == "Approved" || x.MeetingRoomStatus.Name == "Pending")
                            && x.MeetingRoomId == boardRoomId && x.UserId != userId
                            select new MeetingRoomRequestDto
                            {
                                Id = x.Id,
                                MeetingRoomId = x.MeetingRoomId,
                                MeetingRoomName = x.MeetingRoom.MeetingRoomName,
                                OfficeLocation = x.MeetingRoom.OfficeLocation.Location,
                                StartTime = x.StartTime,
                                EndTime = x.EndTime,
                                LocationId = x.MeetingRoom.OfficeLocationId,
                                Location = x.MeetingRoom.OfficeLocation.Location + " - " + x.MeetingRoom.MeetingRoomName,
                                MeetingRoomStatusId = x.MeetingRoomStatusId,
                                MeetingRoomStatus = x.MeetingRoomStatus.Name + " *",
                                Subject = x.Notes + $" ({x.MeetingRoomStatus.Name})",
                                CreatedByUserId = x.UserId,
                                Reason = x.Reason,
                                CssClass = x.MeetingRoomStatus.Name=="Approved" ? "otherapproved" :
                                           x.MeetingRoomStatus.Name=="Pending" ? "otherpending" : ""
                            }
                   ).ToList();
        foreach (var ar in approvedList)
        {
            var qq = (from a in dc.Users
                      where a.UserId == ar.CreatedByUserId
                      select a).FirstOrDefault();
            ar.Subject = qq != null
                ? "Meeting Scheduled By - " + qq.Email + " (" + ar.MeetingRoomStatus + ")"
                : "Meeting Scheduled By Other User (" + ar.CreatedByUserId + ")";
        }
        res.AddRange(approvedList);


        foreach (var al in res)
        {
            var q = (from a in dc.Users
                where a.UserId.ToLower() == al.CreatedByUserId.ToLower()
                select a).FirstOrDefault();
            if (q != null)
            {
                al.RequestedByName = q.Name;
                al.RequestedByEmail = q.Email ?? "";
            }
        }

        // order by location and boardroom name
        return Task.FromResult(res);



    }

    //public Task<List<MeetingRoomRequestDto>> GetPendingApprovalRequests()
    //{
    //    return Task.FromResult(dc.MeetingRoomRequests
    //        .Where(x => x.MeetingRoomStatusId == (int)MeetingRoomStatus.Pending &&
    //                    x.StartTime >= DateTime.Now &&
    //                    x.EndTime >= DateTime.Now &&
    //                    x.by)
    //        .OrderBy(x => new { x.MeetingRoom.OfficeLocation.Location, x.MeetingRoom.MeetingRoomName })
    //        .Select(x => new MeetingRoomRequestDto
    //        {
    //            Id = x.Id,
    //            MeetingRoomId = x.MeetingRoomId,
    //            MeetingRoomName = x.MeetingRoom.MeetingRoomName,
    //            OfficeLocation = x.MeetingRoom.OfficeLocation.Location,
    //            StartTime = x.StartTime,
    //            EndTime = x.EndTime,
    //            MeetingRoomStatusId = x.MeetingRoomStatusId,
    //            Subject = x.Notes
    //        }).ToList());
    //}

    public Task<List<OfficeLocationDto>> GetLocations(string userId = "")
    {
        //var q = (from a in dc.MeetingRooms
        //         orderby a.OfficeLocation.Location
        //         select new OfficeLocationDto
        //         {
        //             Id = a.OfficeLocationId,
        //             Location = a.OfficeLocation.Location,
        //             IsActive = a.IsActive
        //         }).Distinct().ToList();
        if (userId == "")
        {
            var q = (from a in dc.OfficeLocations
                     orderby a.Location
                     select new OfficeLocationDto
                     {
                         Id = a.Id,
                         Location = a.Location,
                         IsActive = a.IsActive,

                     }).ToList();
            return Task.FromResult(q);
        }
        else
        {
            var q = (from a in dc.MeetingRoomApprovars
                     join mr in dc.MeetingRooms on a.MeetingRoomId equals mr.Id
                     where a.UserId == userId && mr.IsActive
                     orderby mr.OfficeLocation.Location
                     select new OfficeLocationDto()
                     {
                         Id = mr.OfficeLocationId,
                         IsActive = mr.IsActive,
                         Location = mr.OfficeLocation.Location
                     }).Distinct().ToList();
            return Task.FromResult(q);
        }

    }

    public Task<List<MeetingRoomDto>> GetLocationBoardRooms(int locationId)
    {
        // get all boardroom by location id
        var q = (from a in dc.MeetingRooms
                 where a.OfficeLocationId == locationId
                 orderby a.MeetingRoomName
                 select new MeetingRoomDto()
                 {
                     Id = a.Id,
                     MeetingRoomName = a.MeetingRoomName,
                     IsActive = a.IsActive,
                     OfficeLocation = a.OfficeLocation.Location,
                     OfficeLocationId = a.OfficeLocationId
                 }).ToList();
        return Task.FromResult(q);
    }

    public Task<string> SaveRequest(MeetingRoomRequestDto request, string userId)
    {
        var q = (from a in dc.MeetingRoomRequests
                 where a.Id == request.Id
                 select a).FirstOrDefault();
        if (q == null)
        {
            q = new MeetingRoomRequest()
            {
                MeetingRoomId = request.MeetingRoomId ?? 0,
                MeetingRoomStatusId = (byte)MeetingRoomStatusEnum.Pending,
                CreatedDate = request.StartTime ?? DateTime.Now,
                EndTime = request.EndTime ?? DateTime.Now,
                StartTime = request.StartTime ?? DateTime.Now,
                UserId = userId,
                Notes = request.Subject
            };
            dc.MeetingRoomRequests.Add(q);
            dc.SaveChanges();
            return Task.FromResult("OK");
        }
        else
        {
            q.StartTime = request.StartTime!.Value;
            q.EndTime = request.EndTime!.Value;
            q.UserId = userId;
            q.Notes = request.Subject;
            q.MeetingRoomId = request.MeetingRoomId ?? 0;
            dc.SaveChanges();
            return Task.FromResult("OK");

        }
    }

    public Task<MeetingRoomRequestDto> GetRequestById(int mmId)
    {
        var q = (from a in dc.MeetingRoomRequests
                 where a.Id == mmId
                 select new MeetingRoomRequestDto
                 {
                     Id = a.Id,
                     MeetingRoomId = a.MeetingRoomId,
                     MeetingRoomName = a.MeetingRoom.MeetingRoomName,
                     OfficeLocation = a.MeetingRoom.OfficeLocation.Location,
                     StartTime = a.StartTime,
                     EndTime = a.EndTime,
                     MeetingRoomStatusId = a.MeetingRoomStatusId,
                     Subject = a.Notes,
                     LocationId = a.MeetingRoom.OfficeLocationId,
                     Location = a.MeetingRoom.OfficeLocation.Location + " - " + a.MeetingRoom.MeetingRoomName
                 }).First();
        return Task.FromResult(q);
    }

    public Task<List<MeetingRoomDto>> GetMeetingRoomsForApprover(string userId, int locationId)
    {

        var amr = (from a in dc.MeetingRoomApprovars where a.UserId.ToLower() == userId select a.MeetingRoomId).ToList();

        var mm = (from a in dc.MeetingRooms
                  where a.IsActive && a.OfficeLocationId == locationId
                  orderby a.MeetingRoomName
                  select new MeetingRoomDto()
                  {
                      Id = a.Id,
                      MeetingRoomName = a.MeetingRoomName,
                      IsActive = a.IsActive,
                      OfficeLocation = a.OfficeLocation.Location,
                      OfficeLocationId = a.OfficeLocationId
                  }
            ).ToList();

        return Task.FromResult(mm);
    }
}
