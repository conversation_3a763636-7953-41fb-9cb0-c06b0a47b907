﻿using Syncfusion.Blazor.CircularGauge;
using System.ComponentModel.DataAnnotations;

namespace PakSuzuki.DTO;

public class MenuDTO
{
    public int Id { get; set; }

    //[Required(ErrorMessage = "* Required")]
    public int? ModuleId { get; set; }

    [MaxLength(200, ErrorMessage = "* Max 200 characters are allowed")]
    public string Code { get; set; } = null!;

    [Required(ErrorMessage = "* Required")]
    [MaxLength(500, ErrorMessage = "* Max 500 characters are allowed")]
    public string Name { get; set; } = null!;

    [MaxLength(500, ErrorMessage = "* Max 500 characters are allowed")]
    public string Url { get; set; } = null!;

    [MaxLength(500, ErrorMessage = "* Max 500 characters are allowed")]
    public string Device { get; set; } = null!;

    public bool IsActive { get; set; }

    public string Active => IsActive ? "Yes" : "No";

    public string? ModuleName { get; set; }

        public int? ParentId { get; set; }
    public string? ParentMenu { get; set; }

    public string? URLTarget {  get; set; }

    public bool HasSubMenu { get; set; }
    public int? SortOrder { get; set; }
    public string? Icon { get; set; }

}
