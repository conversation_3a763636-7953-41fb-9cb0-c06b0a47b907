﻿@inject AppDataService service
<style>
    .nl-item {
        padding: 5px;
    }


    .nl-card {
        /* height: 350px;

        border-color: #bbb;
        border-radius: 10px;
        text-align: center;
        padding: 10px;
        background: white;*/
        background: white;
        width: 100%;
        display: flex;
        flex-direction: column;
        box-shadow: var(--mud-elevation-1);
        padding: 10px;
        border-radius: 5px;
        justify-content: space-between;
        height: 100%;
        display: flex !important;
        align-items: center;
    }

    .bit-csl-cnt :first-child {
        display: inline-block;
    }

    .nl-card .pic-container {
        background-color: #013582;
        display: flex;
        align-items: center;
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        margin: 5px;
        padding: 4px;
        border: 1px solid darkgray;
        border-radius: 15px;
        width: 100%;
    }

    .nl-card .pic {
        width: 200px;
        height: 300px;
        object-fit: cover;
    }

    .nl-card p {
        color: #013582;
        font-size: 16px;
        font-weight: bold;
        padding: 10px;
    }

    .nl-card .info {
        display: flex;
        flex-direction: column;
        width: 100%;
        align-items: center;
    }

    .nl-card .view {
        display: block;
        background-color: #d8e8ff;
        display: inline-block;
        padding: 10px 20px;
        width: 50%;
    }
    /*.bit-crsi{
        width:31% !important;
    }*/
</style>
@if (NewsLettersList.Any())
{
    <div class="row mb-2">
        <div class="col-md" style="display: flex; justify-content: space-between">
            <h3 class="sec-title">Newsletters</h3>
            @if (!string.IsNullOrEmpty(DetailPageLink))
            {
                <a style="color: #013582; font-weight: bold;text-decoration: underline">View All</a>
            }
        </div>
    </div>
    <div class="row mb-2">
        <div class="col">

            <BitCarousel Class="nl-container" Style="height:500px" VisibleItemsCount="3" ScrollItemsCount="3">
                @foreach (var itm in NewsLettersList)
                {
                    var tg = $"nl{itm.postId}";
                    <BitCarouselItem Class="nl-item">


                        <div class="nl-card">
                            <div class="pic-container">
                                <img class="pic" src="@itm.postThumbnailPath" alt="1"/>
                            </div>
                            <div class="info">

                                <p>@itm.postName</p>
                                <button @onclick="() => ShowDetail(itm.postId, itm.postName)" style="" class="view">

                                    <span>View</span>

                                </button>

                            </div>

                        </div>
                        @*<div class="card-outer">
                                <div class="card" @onclick="() => ShowDetail(itm.postId)">
                                    <img src="@itm.postThumbnailPath" class="card-image" alt="">
                                    <div class="card-content">

                                        <h2 class="card-title">@itm.postName</h2>
                                        <p class="card-description">
                                            @if (itm.postContentText!.Length > 250)
                                            {
                                                @itm.postContentText.Substring(0, 250)
                                                <span>...&nbsp;</span> <span @onclick="() => ShowDetail(itm.postId)" class="m-link">more</span>
                                            }
                                            else
                                            {
                                                @itm.postContentText
                                            }

                                        </p>
                                        <p class="card-date">Posted on <span>@itm.postPublishDateStr</span></p>
                                    </div>
                                </div>
                            </div>*@
                    </BitCarouselItem>
                }

            </BitCarousel>
        </div>


    </div>
}

<SfDialog @ref="detailForm" ShowCloseIcon="true" IsModal="true" Width="90%" Header="80%" CloseOnEscape="true" Visible="IsDetailVisible">
    <DialogTemplates>
        <Header>Newsletter</Header>
        <Content>

            <embed src="@pdfFile" width="100%" height="600px"
                   type="application/pdf">

        </Content>
    </DialogTemplates>

</SfDialog>

@code {
    private bool CheckVal1 = false;

    private PostDTO? postDetail;
    private SfDialog detailForm;
    private bool IsDetailVisible = false;
    private string Title = "Newsletter";
    private string pdfFile = "";


    [Parameter] public List<PostDTO> NewsLettersList { get; set; }

    [Parameter] public string? DetailPageLink { get; set; }

    private async Task ShowDetail(int postId, string postName)
    {
        pdfFile = "";
        postDetail = await service.GetPostDetail(postId);

        if (postDetail != null)
        {
            if (!string.IsNullOrEmpty(postDetail.Attachments[0].Url))
            {
                pdfFile = postDetail.Attachments[0].Url;
                Title = postName;
                await detailForm.ShowAsync();
            }
        }
    }

}