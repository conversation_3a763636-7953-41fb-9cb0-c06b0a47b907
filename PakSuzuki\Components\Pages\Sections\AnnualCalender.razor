﻿@page "/setup/calender"
@using ButtonType = MudBlazor.ButtonType
@using ChangeEventArgs = Microsoft.AspNetCore.Components.ChangeEventArgs
@using FilterType = Syncfusion.Blazor.Grids.FilterType
@using Path = Path
@inject IWebHostEnvironment env
@inject PostDataService service
@inject NavigationManager nav
@inject SfDialogService DialogService
@inject AuthenticationStateProvider AuthenticationStateProvider

<SfToast @ref="toastObj"></SfToast>

<div id="banner">
    <MudProgressCircular Color="Color.Info" Indeterminate="@spinner"/>
</div>

<h2>Annual Calender</h2>
<SfDialog @ref="dlgForm" Visible="false" Width="800px" ShowCloseIcon="true" CloseOnEscape="true" IsModal="true">
    <DialogTemplates>
        <Header>Annual Calender</Header>
        <Content>
            <div style="width:90%;margin-left: auto;margin-right:auto">

                <div style="height:12px;"></div>

                <div class="row mb-2">
                    <div class="col-md-6">
                        <MudImage style="width:100%;height:130px;object-fit:cover" Src="@profilePicThumb"
                                  Alt="Thumbnail" Elevation="25" Class="rounded-lg"/>
                        <MudFileUpload T="IBrowserFile" Accept=".png, .jpg, .jpeg" FilesChanged="UploadThumbnail"
                                       MaximumFileCount="1" Disabled="!IsFormEditable">
                            <ButtonTemplate Context="ctx">
                                <MudButton HtmlTag="label"
                                           Style="width:100%"
                                           Variant="Variant.Filled"
                                           Color="Color.Primary"
                                           Size="Size.Small"
                                           StartIcon="@Icons.Material.Filled.CloudUpload"
                                           for="@ctx.Id">
                                    <div style="display:flex; flex-direction:column;">
                                        <div>Thumbnail</div>
                                        <div style="font-size:10px"> (310 px x 500px)</div>

                                    </div>
                                </MudButton>
                            </ButtonTemplate>
                        </MudFileUpload>
                    </div>
                    @*   <div class="col-md">
                            <MudImage Width="120" Height="70" Src="@profilePicHdr" Alt="Header Image" Elevation="25" Class="rounded-lg" />
                            <MudFileUpload T="IBrowserFile" Accept=".png, .jpg, .jpeg" FilesChanged="UploadHeaderImage" MaximumFileCount="1">
                                <ButtonTemplate Context="ctx">
                                    <MudButton HtmlTag="label"
                                               Size="Size.Small"
                                               Variant="Variant.Filled"
                                               Color="Color.Primary"
                                               StartIcon="@Icons.Material.Filled.CloudUpload"
                                               for="@ctx.Id">
                                        Header Picture
                                    </MudButton>
                                </ButtonTemplate>
                            </MudFileUpload>
                        </div> *@

                    @* <div class="row mb-2">
                            <div class="col-md">
                                <SfTextBox Placeholder="Title" FloatLabelType="FloatLabelType.Auto" @bind-Value="postDTO.postName" />
                            </div>
                        </div> *@


                    @*  <div class="row mb-2">
                            <div class="col-md">
                                <label>Content</label>
                                <SfRichTextEditor @ref="rTxt" Enabled="true" Readonly="false" Width="100%" Height="300px" @bind-Value="postDTO.postContentText" EnableXhtml="true">
                                    <RichTextEditorToolbarSettings Items="@Tools" />
                                </SfRichTextEditor>
                            </div>
                        </div> *@




                    @*    <div class="row mb-2">
                            <div class="col-md">
                                <SfTextBox Placeholder="Search Tags" FloatLabelType="FloatLabelType.Auto" @bind-Value="srhTag" />
                            </div>
                            <div class="col-md" style="padding-top:24px;">
                                <MudButton ButtonType="MudBlazor.ButtonType.Button" Color="Color.Success"
                                           Variant="Variant.Filled" Size="Size.Small" OnClick="addSrhTag">Add</MudButton>
                            </div>
                        </div>

                        <div>
                            <MudText Typo="@Typo.caption">Total Search Tags : @(postSrhDTO.Any() ? @postSrhDTO.Count : 0)</MudText>

                            <MudChipSet T="string" AllClosable OnClose="Closed">
                                @foreach (var item in postSrhDTO)
                                {
                                    <MudChip Variant="Variant.Outlined" Color="Color.Secondary" Text="@item.postSearchTagName"></MudChip>
                                }
                            </MudChipSet>
                        </div> *@
                </div>
                <div class="row mb-2">
                    <div class="col-md">
                        <SfTextBox Placeholder="Remarks" FloatLabelType="FloatLabelType.Always"
                                   @bind-Value="postDTO.postRemarks" Multiline="true" Enabled="IsFormEditable"/>
                    </div>
                </div>
                <div>
                    <div class="row mb-2">
                        <div class="col-md-2">
                            <MudFileUpload T="IReadOnlyList<IBrowserFile>"
                                           Accept=".png, .jpg, .jpeg, .pdf, .docx, .txt, .doc,"
                                           FilesChanged="UploadAttachments" Disabled="!IsFormEditable">
                                <ButtonTemplate>
                                    <MudButton HtmlTag="label"
                                               Variant="Variant.Filled"
                                               Color="Color.Primary"
                                               StartIcon="@Icons.Material.Filled.CloudUpload"
                                               Size="Size.Small"
                                               for="@context.Id">
                                        Attach
                                    </MudButton>
                                </ButtonTemplate>
                            </MudFileUpload>
                        </div>
                        @*   <div class="col-md-4">
                            @{
                            int totalAttachment = 0;
                            if (attachments != null)
                            {
                            if (attachments.Any())
                            {
                            totalAttachment = attachments.Count;
                            }
                            }
                            <MudText Typo="@Typo.caption" Style="padding-top: 5px;">Total Attachments : @totalAttachment</MudText>
                            }
                            </div> *@
                    </div>

                    <MudChipSet T="string" AllClosable OnClose="ClosedAttachment">
                        @foreach (var attachFile in attachmentDTO!)
                        {
                            <MudChip Variant="Variant.Outlined" Color="Color.Success"
                                     Text="@attachFile.postAttachmentName"></MudChip>
                        }
                    </MudChipSet>

                    @*       <MudStack Row="true">
                        <MudPaper Class="pa-3">
                        @if (attachments != null)
                        {
                        @foreach (var attachFile in attachments!)
                        {
                        <MudChip Label="true" Color="Color.Warning" Style="color:black;" Size="Size.Small">
                        <MudIconButton Icon="@Icons.Material.Filled.Delete" Color="Color.Primary" OnClick="(() => removeAttachFile(attachFile.Name))" />@attachFile.Name &nbsp;<code>@attachFile.Size bytes</code>
                        </MudChip>
                        }
                        }
                        </MudPaper>
                        </MudStack> *@
                </div>
                <div style="height:10px;"></div>
                <div class="row mb-2">
                    <div class="col-md-4">
                        <label>Publish Date</label>
                        <Syncfusion.Blazor.Calendars.SfDatePicker Placeholder="Publish Date"
                                                                  @bind-Value="postDTO.postPublishDate"
                                                                  Enabled="IsFormEditable"/>
                    </div>
                    <div class="col-md-4">
                        <label>Expiry Date</label>
                        <Syncfusion.Blazor.Calendars.SfDatePicker Placeholder="Expiry Date"
                                                                  @bind-Value="postDTO.postExpiryDate"/>
                    </div>
                </div>
                <div style="height:12px;"></div>
                <div class="row">
                    <div class="mb-2 col-md">
                        <MudButton ButtonType="ButtonType.Submit" Color="Color.Primary"
                                   Variant="Variant.Filled" Size="Size.Small" StartIcon="@Icons.Material.Filled.Save"
                                   OnClick="SavePost">
                            Save
                        </MudButton>
                        <MudButton ButtonType="ButtonType.Button" Color="Color.Success"
                                   Variant="Variant.Filled" Size="Size.Small" OnClick="() => dlgForm!.HideAsync()">
                            Cancel
                        </MudButton>
                    </div>
                </div>
            </div>
        </Content>
    </DialogTemplates>
    @* <DialogEvents Opened="@DialogOpen"></DialogEvents> *@
</SfDialog>


<div class="row mb-2">
    <div class="col-md">
        <MudButton Size="Size.Small" Color="Color.Primary" StartIcon="@Icons.Material.Filled.Add"
                   Variant="Variant.Filled" OnClick="OpenCreateForm">Add
        </MudButton>
        <MudButton Size="Size.Small" Color="Color.Success" StartIcon="@Icons.Material.Filled.Edit"
                   Variant="Variant.Filled" OnClick="OpenEditForm">Edit
        </MudButton>
        <MudButton Size="Size.Small" Color="Color.Warning" StartIcon="@Icons.Material.Filled.Delete"
                   Variant="Variant.Filled" OnClick="ConfirmDelete">Delete
        </MudButton>
        <MudButton Size="Size.Small" Color="Color.Info" StartIcon="@Icons.Material.Filled.Group"
                   Variant="Variant.Filled" OnClick="OpenAssignRole">Assign Roles
        </MudButton>
    </div>
</div>

<div class="row">
    <div class="mb-2 col-md">
        @if (allPosts.Any())
        {
            <SfGrid @ref="dgMain" DataSource="allPosts" AllowSelection="true" AllowFiltering="true" AllowSorting="true"
                    Width="100%">
                <GridFilterSettings Type="FilterType.Excel"></GridFilterSettings>
                <GridColumns>
                    <GridColumn AutoFit="true" Visible="false" HeaderText="ID"
                                Field="@nameof(PostDTO.postId)"></GridColumn>
                    <GridColumn Width="450" HeaderText="Remarks" Field="@nameof(PostDTO.postRemarks)"></GridColumn>
                    <GridColumn AutoFit="true" HeaderText="Publish Date" Field="@nameof(PostDTO.postPublishDate)"
                                Format="dd-MMM-yyyy"></GridColumn>
                    <GridColumn AutoFit="true" HeaderText="Expiry Date" Field="@nameof(PostDTO.postExpiryDate)"
                                Format="dd-MMM-yyyy"></GridColumn>
                    <GridColumn AutoFit="true" HeaderText="Status" Field="@nameof(PostDTO.postStatus)"></GridColumn>
                    <GridColumn AutoFit="true" HeaderText="Created By" Field="@nameof(PostDTO.CreatedBy)"></GridColumn>
                    <GridColumn AutoFit="true" HeaderText="Created Date" Field="@nameof(PostDTO.CreatedDate)"
                                Format="dd-MMM-yyyy h:mm:ss tt"></GridColumn>
                    <GridColumn AutoFit="true" HeaderText="Modified By"
                                Field="@nameof(PostDTO.ModifiedBy)"></GridColumn>
                    <GridColumn AutoFit="true" HeaderText="Modified Date" Field="@nameof(PostDTO.ModifiedDate)"
                                Format="dd-MMM-yyyy h:mm:ss tt"></GridColumn>
                </GridColumns>
            </SfGrid>
        }
    </div>
</div>

<SfDialog @ref="dlgFormRole" Visible="false" Width="500px" ShowCloseIcon="true" CloseOnEscape="true" IsModal="true">
    <DialogTemplates>
        <Header>Assign Roles</Header>
        <Content>
            <div class="row" style="display:flex">
                <MudText Typo="Typo.caption">Selected Post: <b>@postDTO.postName</b></MudText>
            </div>
            <div style="height:10px;"></div>
            <div class="row" style="text-align:right;">
                <SfCheckBox @bind-Checked="isChecked" Label="Select All" @onchange="SelectAll">Select All</SfCheckBox>
            </div>
            <div style="height:10px;"></div>
            <div class="row">
                <SfGrid @ref="dgMainRole" Height="400px" DataSource="allRole" AllowSelection="true"
                        AllowFiltering="true" AllowSorting="true" Width="500px">
                    <GridFilterSettings Type="FilterType.Excel"></GridFilterSettings>
                    <GridColumns>
                        <GridColumn AutoFit="true" Visible="false" HeaderText="Id"
                                    Field="@nameof(RoleDTO.Id)"></GridColumn>
                        <GridColumn Width="350" HeaderText="Roles" Field="@nameof(RoleDTO.Name)"></GridColumn>
                        <GridColumn Field="@nameof(RoleDTO.isSelect)" HeaderTextAlign="TextAlign.Center"
                                    HeaderText="Selected" Width="120" ClipMode="ClipMode.EllipsisWithTooltip">
                            <Template>
                                @{
                                    var ctx = context as RoleDTO;
                                    <div>
                                        <center>
                                            <SfCheckBox @bind-Checked="@ctx.isSelect"></SfCheckBox>
                                        </center>
                                    </div>
                                }
                            </Template>
                        </GridColumn>
                    </GridColumns>
                </SfGrid>

            </div>
            <div class="page-panel">
                <div>
                    <MudButton Size="Size.Small" Color="Color.Primary" StartIcon="@Icons.Material.Filled.Save"
                               Variant="Variant.Filled" OnClick="SaveUserRole">Save
                    </MudButton>
                    <MudButton Size="Size.Small" Color="Color.Success" StartIcon="@Icons.Material.Filled.Cancel"
                               Variant="Variant.Filled" OnClick="() => dlgFormRole!.HideAsync()">Cancel
                    </MudButton>
                </div>
            </div>
        </Content>
    </DialogTemplates>
</SfDialog>

@code {
    SfDialog? dlgForm, dlgFormRole;
    IList<IBrowserFile> files = new List<IBrowserFile>();
    private List<RoleDTO> allRole = new();
    SfGrid<RoleDTO>? dgMainRole;
    private List<PostDTO> allPosts = new();
    private SfGrid<PostDTO> dgMain;
    private PostDTO postDTO = new();
    private List<PostSearchTagDTO> postSrhDTO = new();
    private string srhTag;
    SfRichTextEditor rTxt;
    private string profilePicThumb = "images/upload.jpg";
    private string profilePicHdr = "images/upload.jpg";
    private IBrowserFile? imgfileThumb;
    private IBrowserFile? imgfileHdr;
    private List<IBrowserFile>? attachments = new();
    private List<PostAttachmentDTO> attachmentDTO = new();
    private SfToast? toastObj = new();
    private bool spinner;
    bool isChecked;
    bool IsFormEditable = true;

    private async void SelectAll(ChangeEventArgs args)
    {
        try
        {
            foreach (var item in allRole)
            {
                item.isSelect = isChecked;
            }

            StateHasChanged();
        }
        catch (Exception ex)
        {
            var tm = new ToastModel { Content = ex.Message, Title = "Error", ShowCloseButton = true, Timeout = 0 };
            await toastObj!.ShowAsync(tm);
        }
    }

    private async Task SaveUserRole()
    {
        try
        {
            await service.savePostRolesInPostScreen(allRole, postDTO.postId);
            var tm = new ToastModel { Content = "Roles assigned successfully.", Title = "Info", ShowCloseButton = true, Timeout = 5000 };
            await toastObj!.ShowAsync(tm);
            await dlgFormRole!.HideAsync();
        }
        catch (Exception ex)
        {
            var tm = new ToastModel { Content = ex.Message, Title = "Error", ShowCloseButton = true, Timeout = 5000 };
            await toastObj!.ShowAsync(tm);
        }
    }

    private async Task OpenAssignRole()
    {
        try
        {
            if (dgMain!.SelectedRecords is { Count: > 0 })
            {
                var pId = dgMain.SelectedRecords[0].postId;
                allRole = await service.GetAllRolesAsyncWithSelectedPost(pId);
                postDTO = await service.getPostWithId(pId);
                await dlgFormRole!.ShowAsync();
            }
            else
            {
                var tm = new ToastModel { Content = "Please select post.", Title = "Info", ShowCloseButton = true, Timeout = 5000 };
                await toastObj!.ShowAsync(tm);
            }
        }
        catch (Exception ex)
        {
            var tm = new ToastModel { Content = ex.Message, Title = "Error", ShowCloseButton = true, Timeout = 5000 };
            await toastObj!.ShowAsync(tm);
        }
    }

    // private void DialogOpen()
    // {
    //     this.rTxt.RefreshUI();
    // }

    private async void OpenEditForm()
    {
        if (dgMain?.SelectedRecords.Count > 0)
        {
            if (dgMain?.SelectedRecords[0].postStatus != "Expired")
            {
                //isPublish = false;

                var pId = dgMain.SelectedRecords[0].postId;
                postDTO = await service.getPostWithId(pId);
                IsFormEditable = postDTO.postStatus is "Draft" or "Published";
                profilePicThumb = string.IsNullOrEmpty(postDTO.postThumbnailPath) ? "images/upload.jpg" : AppDataService.ConvertToUrl(postDTO.postThumbnailPath);
                profilePicHdr = string.IsNullOrEmpty(postDTO.postHeadImagePath) ? "images/upload.jpg" : AppDataService.ConvertToUrl(postDTO.postHeadImagePath);
                postSrhDTO = await service.getPostSrhTagsWithPostId(pId);
                attachmentDTO = await service.getPostAttachmentWithPostId(pId);
                await dlgForm!.ShowAsync();
            }
            else
            {
                var tm = new ToastModel
                {
                    Content = "Post is expired, you can not edit.",
                    Title = "Info",
                    ShowCloseButton = true,
                    Timeout = 5000,
                    CssClass = "e-warning",
                    ShowProgressBar = true
                };
                await toastObj!.ShowAsync(tm);
            }
        }
    }

    public void Closed(MudChip chip)
    {
        if (!IsFormEditable) return;
        var p = postSrhDTO.SingleOrDefault(x => x.postSearchTagName == chip.Text)!;
        postSrhDTO.Remove(p);
        StateHasChanged();
    }

    public void ClosedAttachment(MudChip chip)
    {
        if (!IsFormEditable) return;
        var a = attachmentDTO!.SingleOrDefault(x => x.postAttachmentName == chip.Text);
        attachmentDTO.Remove(a);
        StateHasChanged();
    }

    private void OpenCreateForm()
    {
        IsFormEditable = true;
        postDTO = new PostDTO { postExpiryDate = DateTime.Today.AddDays(60) };
        postSrhDTO = new List<PostSearchTagDTO>();
        profilePicThumb = "images/upload.jpg";
        profilePicHdr = "images/upload.jpg";
        imgfileThumb = null;
        imgfileHdr = null;
        attachments = new List<IBrowserFile>();
        attachmentDTO = new List<PostAttachmentDTO>();
        files = new List<IBrowserFile>();
        dlgForm!.ShowAsync();
    }

    private async void ConfirmDelete()
    {
        try
        {
            if (dgMain!.SelectedRecords is { Count: > 0 })
            {
                var conf = await DialogService.ConfirmAsync("Are you sure want to delete this record", "Confirm");
                if (conf)
                {
                    var Id = dgMain.SelectedRecords[0].postId;
                    service.deletePost(Id);
                    allPosts = await service.getAllPosts((int)Section.Calender);
                    StateHasChanged();
                }
            }
        }
        catch (Exception ex)
        {
            ToastModel t = new() { Title = "Error", Content = ex.Message };
            await toastObj!.ShowAsync(t);
        }
    }

    protected override async Task OnInitializedAsync()
    {
        allPosts = await service.getAllPosts((int)Section.Calender);
    }

    private async void UploadAttachments(IReadOnlyList<IBrowserFile> files)
    {
        foreach (var file in files)
        {
            attachments!.Add(file);
            var postTitle = Guid.NewGuid().ToString();
            var dirPath = $"{env.WebRootPath}\\Posts\\{postTitle}\\Attachments";
            if (Directory.Exists(dirPath) == false)
            {
                Directory.CreateDirectory(dirPath);
            }

            var path = dirPath + "\\" + file.Name;
            PostAttachmentDTO p = new()
            {
                postAttachmentDocURL = path,
                postAttachmentName = file.Name,
                postAttachmentDocSize = file.Size.ToString(),
                postAttachmentDocType = file.ContentType,
                postAttachmentDocExt = Path.GetExtension(file.Name)
            };
            attachmentDTO.Add(p);

            var stream = file.OpenReadStream(30 * 1024 * 1024);
            var fs = File.Create(path);
            await stream.CopyToAsync(fs);
            stream.Close();
            fs.Close();
        }

        StateHasChanged();
    }

    private async void UploadThumbnail(IBrowserFile file)
    {
        imgfileThumb = file;
        var memoryStream = new MemoryStream();
        await using var fileStream = file.OpenReadStream(long.MaxValue);
        await fileStream.CopyToAsync(memoryStream);
        var bytes = memoryStream.ToArray();
        profilePicThumb = "data:image/png;base64," + Convert.ToBase64String(bytes);
        StateHasChanged();
    }

    private async void UploadHeaderImage(IBrowserFile file)
    {
        imgfileHdr = file;
        var memoryStream = new MemoryStream();
        await using var fileStream = file.OpenReadStream(long.MaxValue);
        await fileStream.CopyToAsync(memoryStream);
        var bytes = memoryStream.ToArray();
        profilePicHdr = "data:image/png;base64," + Convert.ToBase64String(bytes);
        StateHasChanged();
    }

    private List<ToolbarItemModel> Tools =
    [
        new() { Command = ToolbarCommand.Bold },
        new() { Command = ToolbarCommand.Italic },
        new() { Command = ToolbarCommand.Underline },
        new() { Command = ToolbarCommand.Separator },
        new() { Command = ToolbarCommand.Formats },
        new() { Command = ToolbarCommand.Alignments },
        new() { Command = ToolbarCommand.OrderedList },
        new() { Command = ToolbarCommand.UnorderedList },
        new() { Command = ToolbarCommand.Separator },
        // new ToolbarItemModel() { Command = ToolbarCommand.CreateLink },
        // new ToolbarItemModel() { Command = ToolbarCommand.Image },
        // new ToolbarItemModel() { Command = ToolbarCommand.Separator },
        // new ToolbarItemModel() { Command = ToolbarCommand.SourceCode },
        // new ToolbarItemModel() { Command = ToolbarCommand.Separator },
        new() { Command = ToolbarCommand.Undo },
        new() { Command = ToolbarCommand.Redo }
    ];

    private void AddSrhTag()
    {
        if (srhTag != "")
        {
            PostSearchTagDTO p = new()
            {
                postSearchTagName = srhTag,
                postSearchTagID = -1
            };
            postSrhDTO.Add(p);
            srhTag = "";
            StateHasChanged();
        }
    }

    private void RemoveSrhTag(string tagName)
    {
        var p = postSrhDTO.SingleOrDefault(x => x.postSearchTagName == tagName)!;
        postSrhDTO.Remove(p);
        StateHasChanged();
    }

    private void RemoveAttachFile(string fileName)
    {
        var a = attachmentDTO!.SingleOrDefault(x => x.postAttachmentName == fileName);
        attachmentDTO.Remove(a);
        StateHasChanged();
    }

    private async Task<string> SaveImageThumb(string postTitle, string fileName)
    {
        if (imgfileThumb != null && profilePicThumb != "images/upload.jpg")
        {
            postTitle = Guid.NewGuid().ToString();
            fileName = postTitle;
            var dirPath = $"{env.WebRootPath}\\Posts\\{postTitle}";
            if (Directory.Exists(dirPath) == false)
            {
                Directory.CreateDirectory(dirPath);
            }

            var stream = imgfileThumb.OpenReadStream(long.MaxValue);
            var path = dirPath + "\\" + fileName + "-thumb.png";
            var fs = File.Create(path);
            await stream.CopyToAsync(fs);
            stream.Close();
            fs.Close();
            imgfileThumb = null;
            return await Task.FromResult(path);
        }

        return await Task.FromResult(profilePicThumb);

        return await Task.FromResult("");
    }

    private async Task<string> SaveImageHdr(string postTitle, string fileName)
    {
        if (imgfileHdr != null && profilePicHdr != "images/upload.jpg")
        {
            postTitle = Guid.NewGuid().ToString();
            fileName = postTitle;
            var dirPath = $"{env.WebRootPath}\\Posts\\{postTitle}";
            if (Directory.Exists(dirPath) == false)
            {
                Directory.CreateDirectory(dirPath);
            }

            var stream = imgfileHdr.OpenReadStream(long.MaxValue);
            var path = dirPath + "\\" + fileName + "-hdr.png";

            var fs = File.Create(path);
            await stream.CopyToAsync(fs);
            stream.Close();
            fs.Close();
            imgfileHdr = null;
            return await Task.FromResult(path);
        }

        return await Task.FromResult(profilePicHdr);
        return await Task.FromResult("");
    }

    private async Task<string> SaveAttachments(string postTitle)
    {
        postTitle = Guid.NewGuid().ToString();
        //fileName = postTitle;
        var dirPath = $@"{env.WebRootPath}\Posts\{postTitle}\Attachments";
        if (Directory.Exists(dirPath) == false)
        {
            Directory.CreateDirectory(dirPath);
        }

        if (attachments!.Any())
        {
            foreach (var attachFile in attachments!)
            {
                var path = dirPath + "\\" + attachFile.Name;
                PostAttachmentDTO p = new()
                {
                    postAttachmentDocURL = path,
                    postAttachmentName = attachFile.Name,
                    postAttachmentDocSize = attachFile.Size.ToString(),
                    postAttachmentDocType = attachFile.ContentType,
                    postAttachmentDocExt = Path.GetExtension(attachFile.Name)
                };
                attachmentDTO.Add(p);

                var stream = attachFile.OpenReadStream(30 * 1024 * 1024);
                var fs = File.Create(path);
                await stream.CopyToAsync(fs);
                stream.Close();
                fs.Close();
                // await using FileStream fs = new(path, FileMode.Create);
                // await attachFile.OpenReadStream(long.MaxValue).CopyToAsync(fs);
            }

            return await Task.FromResult("");
        }

        return await Task.FromResult("");
    }

    private async Task SavePost()
    {
        try
        {
            spinner = true;
            StateHasChanged();
            var msg = await service.checkPublishExpiryDate(postDTO.postPublishDate ?? DateTime.Now, postDTO.postExpiryDate ?? DateTime.Now, postDTO.postId);
            if (msg != "")
            {
                ToastModel t1 = new()
                {
                    Title = "Information",
                    Content = msg,
                    ShowProgressBar = true,
                    ShowCloseButton = true,
                    Timeout = 5000
                };
                await toastObj!.ShowAsync(t1);
                spinner = false;
                return;
            }

            if (string.IsNullOrEmpty(postDTO.postRemarks))
            {
                ToastModel t1 = new()
                {
                    Title = "Information",
                    Content = "Remarks field is required..",
                    ShowProgressBar = true,
                    ShowCloseButton = true,
                    Timeout = 5000
                };
                await toastObj!.ShowAsync(t1);
                spinner = false;
                return;
            }


            var contentText = ""; //await rTxt.GetTextAsync();
            var postName = postDTO.postName + "-" + DateTime.Now.ToString("ddMMMyyyyHHmmssff");
            postDTO.postThumbnailPath = await SaveImageThumb("", "");
            postDTO.postHeadImagePath = await SaveImageHdr("", "");
            postDTO.postIconImagePath = "";
            //await saveAttachments("");

            var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
            var user = authState.User;
            var userId = "";
            if (user.Identity is { Name: not null })
            {
                userId = user.Identity.Name;
            }

            await service.savePost(postDTO, postSrhDTO, attachmentDTO, -1, contentText, userId, (int)Section.Calender);

            allPosts = await service.getAllPosts((int)Section.Calender);

            postDTO = new PostDTO();
            postSrhDTO = [];
            profilePicThumb = "images/upload.jpg";
            profilePicHdr = "images/upload.jpg";
            imgfileThumb = null;
            imgfileHdr = null;
            attachments = new List<IBrowserFile>();
            attachmentDTO = new List<PostAttachmentDTO>();
            files = new List<IBrowserFile>();

            spinner = false;
            StateHasChanged();

            ToastModel t = new() { Title = "Information", Content = "Record Saved successfully." };
            await toastObj!.ShowAsync(t);

            await dlgForm!.HideAsync();

            //nav.NavigateTo("/");
        }
        catch (Exception ex)
        {
            ToastModel t = new() { Title = "Error", Content = ex.Message };
            await toastObj!.ShowAsync(t);
        }
    }

}

<style>
    #banner {
        position: fixed;
        left: 50%;
        margin-left: -240px;
        width: 480px;
        height: 115px;
        border-radius: 20px;
        text-align: center;
    }


    .e-grid td.e-active {
        background: #faa601 !important;
    }
</style>