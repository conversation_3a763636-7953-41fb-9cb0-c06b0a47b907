﻿@using PakSuzuki.Components.Layout
@using PakSuzuki.DTO
@rendermode InteractiveServer
@inject AuthenticationStateProvider AuthenticationStateProvider
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Authorization
@inject CorporateService cService
@*@inject EmployeeDataService empService*@
@layout EmpMainLayout
@page "/employee"

<style>
    .tab-leave-sum {
        width: 100%;
        border-collapse: collapse;
    }

        .tab-leave-sum th, td {
            text-align: center;
            padding: 5px;
        }

    .mud-card-header {
        padding: 10px 16px;
        border-bottom: 1px solid #8080807a;
    }
</style>

@* Syncfusion Model Dialog for Menu Item Selection *@
<SfDialog Width="700px" IsModal="true" CloseOnEscape="true" ShowCloseIcon="true" Visible="IsQuickLinkDglOpen" @ref="dlgQuickLink">
    <DialogTemplates>
        <Header>Quick Link Menu Selection</Header>
        <Content>
            <SfGrid DataSource="AllQuickLinks" AllowFiltering="true" AllowSorting="true" Height="500px">
                <GridFilterSettings Type="Syncfusion.Blazor.Grids.FilterType.Excel"></GridFilterSettings>
                <GridColumns>
                    <GridColumn AutoFit="true">
                        <Template Context="cc">
                            @{ 
                                if(cc is UserQuickLinkDto mm)
                                {
                                    <SfCheckBox @bind-Checked="mm.Checked"></SfCheckBox>
                                }
                            }
                        </Template>
                    </GridColumn>
                    <GridColumn Field="@nameof(UserQuickLinkDto.MenuName)" HeaderText="Menu"></GridColumn>
                    <GridColumn Field="@nameof(UserQuickLinkDto.URL)" HeaderText="URL"></GridColumn>
                    

                </GridColumns>
            </SfGrid>
            <MudButton Color="Color.Primary" Size="Size.Small" Variant="Variant.Filled"
                       Class="mt-2 mb-2"
                       @onclick="UpdateUserQuickLinkMenus"
                       StartIcon="@Icons.Material.Filled.Save">Update</MudButton>
        </Content>
    </DialogTemplates>
</SfDialog>
<h3 class="sec-title mb-3 mt-2">Employee Attendance and Leave Summary</h3>
<div class="row">
    <div class="col-md" style="display: flex;">
        <MudCard>
            <MudCardHeader>
                <CardHeaderContent>
                    <p class="sec-title" style="font-size:16px">Yearly Attendance</p>

                </CardHeaderContent>
            </MudCardHeader>
            <MudCardContent>
                <div class="row">
                    <div class="col">
                        <MudChart ChartType="MudBlazor.ChartType.Donut" InputData="@Ydata" InputLabels="@Ylabels">
                            <CustomGraphics>
                                <text class="donut-inner-text" x="47%" y="35%" dominant-baseline="middle" text-anchor="middle" fill="black" font-family="Helvetica" font-size="2">Total</text>
                                <text class="donut-inner-text" x="47%" y="50%" dominant-baseline="middle" text-anchor="middle" fill="black" font-family="Helvetica" font-size="5">@Ydata.Sum().ToString()</text>
                            </CustomGraphics>
                        </MudChart>
                    </div>
                    <div class="col">
                        <div class="row" style="font-size:14px">
                            @foreach(var aa in empYearAttd)
                            {
                                <div class="col-8 mb-2">@aa.Head</div><div class="col-4 mb-2">@aa.Value</div>

                            }
                            @* <div class="col-8 mb-2">Absents</div> *@
                            @* <div class="col-4 mb-2">4</div> *@
                            @* <div class="col-8 mb-2">Presents</div> *@
                            @* <div class="col-4 mb-2">245</div> *@
                            @* <div class="col-8 mb-2">Off Day</div> *@
                            @* <div class="col-4 mb-2">3</div> *@
                            @* <div class="col-8 mb-2">Late In</div> *@
                            @* <div class="col-4 mb-2">24</div> *@
                            @* <div class="col-8 mb-2">Late Hours</div> *@
                            @* <div class="col-4 mb-2">0.0</div> *@
                        </div>
                    </div>
                </div>
            </MudCardContent>
        </MudCard>
    </div>
    <div class="col-md" style="display:flex">
        <MudCard>
            <MudCardHeader>
                <CardHeaderContent>
                    <p class="sec-title" style="font-size:16px">Monthly Attendance</p>
                </CardHeaderContent>
            </MudCardHeader>
            <MudCardContent>
                <div class="row">
                    <div class="col">
                        <MudChart ChartType="MudBlazor.ChartType.Donut" InputData="@Mdata" InputLabels="@Mlabels">
                            <CustomGraphics>
                                <text class="donut-inner-text" x="47%" y="35%" dominant-baseline="middle" text-anchor="middle" fill="black" font-family="Helvetica" font-size="2">Total</text>
                                <text class="donut-inner-text" x="47%" y="50%" dominant-baseline="middle" text-anchor="middle" fill="black" font-family="Helvetica" font-size="5">@Mdata.Sum().ToString()</text>
                            </CustomGraphics>
                        </MudChart>
                    </div>
                    <div class="col">
                        <div class="row" style="font-size:14px">
                            @foreach (var aa in empMonthAttd)
                            {
                                <div class="col-8 mb-2">@aa.Head</div><div class="col-4 mb-2">@aa.Value</div>

                            }
                            @*<div class="col-8 mb-2">Presents</div><div class="col-4 mb-2">245</div>
                            <div class="col-8 mb-2">Off Day</div><div class="col-4 mb-2">3</div>
                            <div class="col-8 mb-2">Late In</div><div class="col-4 mb-2">24</div>
                            <div class="col-8 mb-2">Late Hours</div><div class="col-4 mb-2">0.0</div>*@
                        </div>
                    </div>
                </div>
            </MudCardContent>
        </MudCard>
    </div>
    <div class="col-md" style="display:flex">
        <MudCard Style="width:100%">
            <MudCardHeader>
                <CardHeaderContent>
                    <p class="sec-title" style="font-size:16px">Leaves Summary</p>
                </CardHeaderContent>
            </MudCardHeader>
            <MudCardContent>
                <table class="tab-leave-sum" style="height:100%;width:100%">
                    <thead>
                        <tr>
                            <th>Leave Type</th>
                            <th>Opening</th>
                            <th>Availed</th>
                            <th>Balance</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var mm in empAttSummary)
                        {
                            <tr><td>@mm.LeaveType</td><td>@mm.Opening</td><td>@mm.Availed</td><td>@mm.Balance</td></tr>

                        }
                        @*<tr><td>Casual Leaves</td><td>76</td><td>6</td><td>70</td></tr>
                        <tr><td>Sick Leaves</td><td>76</td><td>6</td><td>70</td></tr>
                        <tr><td>Special Leaves</td><td>76</td><td>6</td><td>70</td></tr>*@
                    </tbody>
                </table>
            </MudCardContent>
        </MudCard>
    </div>
</div>
<div class="row">
    <div class="col-md" style="display:flex;justify-content: space-between">
        <h3 class="sec-title mb-2 mt-3">Quick Links</h3>
        <MudButton Size="Size.Small" Variant="Variant.Text" Color="Color.Primary"
                   @onclick="OpenCustomizeQuickLinkForm"
                   StartIcon="@Icons.Material.Filled.Edit">Customize</MudButton>
    </div>
</div>

<div class="row">
    @foreach (var mm in AllQuickLinks.Where(mm => mm.Checked))
    {
        <div class="col-md-2 mb-2" style="max-height:120xp">
            <a target="tg2" href="@mm.URL">
                <MudCard>
                    <MudCardContent Style="text-align: center;height:130px;">
                        <MudIcon Icon="@Icons.Material.Filled.AddTask" Size="Size.Large"></MudIcon>
                        <br>
                        @mm.MenuName

                    </MudCardContent>
                </MudCard>
            </a>
        </div>
    }

</div>
<h3 class="sec-title mt-3">Employee Self Service</h3>
<div class="row mt-3">
    @foreach (var ssl in AllSelfServiceLinks)
    {
        <div class="col-md-4 mb-2">
            <a href="@ssl.Url" target="@($"ess{ssl.Id}")">
                <MudCard>
                    <MudCardContent Style="text-align: center">
                        <div style="display: flex; height: 50px; justify-content: space-between;align-content: center; align-items: center; padding: 10px;">

                            <p>@ssl.Name</p>
                            <MudIcon Icon="@IconHelper.GetIconByName(ssl.Icon)" Size="Size.Large"></MudIcon>
                        </div>

                    </MudCardContent>
                </MudCard>
            </a>
        </div>    
    }
    
    
    @*<div class="col-md-4 mb-2">
        <MudCard>
            <MudCardContent Style="text-align: center">
                <a href="http://portal/regularize.asp" style="display: flex; height: 50px; justify-content: space-between;align-content: center; align-items: center; padding: 10px;" target="ar">

                    <p>Attendance Regularization</p>
                    <MudIcon Icon="@Icons.Material.Filled.Checklist" Size="Size.Large"></MudIcon>
                </a>

            </MudCardContent>
        </MudCard>
    </div>
    <div class="col-md-4 mb-2">
        <MudCard>
            <MudCardContent Style="text-align: center">
                <a href="http://portal:2007/mlsa/LSABuilder.jsp" style="display: flex; height: 50px; justify-content: space-between;align-content: center; align-items: center; padding: 10px;" target="lss">

                    <p>Late Sitting Request</p>
                    <MudIcon Icon="@Icons.Material.Filled.AccessTime" Size="Size.Large"></MudIcon>
                </a>

            </MudCardContent>
        </MudCard>
    </div>
    <div class="col-md-4 mb-2">
        <MudCard>
            <MudCardContent Style="text-align: center">
                <a href="http://myportal.psmcl.com.pk/trs/" style="display: flex; height: 50px; justify-content: space-between;align-content: center; align-items: center; padding: 10px;" target="tran">

                    <p>Transport Request</p>
                    <MudIcon Icon="@Icons.Material.Filled.CarRental" Size="Size.Large"></MudIcon>
                </a>

            </MudCardContent>
        </MudCard>
    </div>
    <div class="col-md-4 mb-2">
        <MudCard>
            <MudCardContent Style="text-align: center">
                <a href="http://myportal.psmcl.com.pk/tfs" style="display: flex; height: 50px; justify-content: space-between;align-content: center; align-items: center; padding: 10px;" target="trv">

                    <p>Travel Request</p>
                    <MudIcon Icon="@Icons.Material.Filled.AirplanemodeActive" Size="Size.Large"></MudIcon>
                </a>

            </MudCardContent>
        </MudCard>
    </div>
    <div class="col-md-4 mb-2">
        <MudCard>
            <MudCardContent Style="text-align: center">
                <a href="http://myportal.psmcl.com.pk/Myportal/myInformation.aspx" style="display: flex; height: 50px; justify-content: space-between;align-content: center; align-items: center; padding: 10px;" target="mi">

                    <p>My Information</p>
                    <MudIcon Icon="@Icons.Material.Filled.Info" Size="Size.Large"></MudIcon>
                </a>

            </MudCardContent>
        </MudCard>
    </div>*@
</div>

@*<h3 class="sec-title mt-3">Personal Information Update Request</h3>
<div class="row mt-3">
    <div class="col-md-4 mb-2">
        <MudCard>
            <MudCardContent Style="text-align: center">
                <a href="#" style="display: flex; height: 50px; justify-content: space-between;align-content: center; align-items: center; padding: 10px;">

                    <p>Update Qualification Request</p>
                    <MudIcon Icon="@Icons.Material.Filled.Book" Size="Size.Large"></MudIcon>
                </a>

            </MudCardContent>
        </MudCard>
    </div>
    <div class="col-md-4 mb-2">
        <MudCard>
            <MudCardContent Style="text-align: center">
                <a href="#" style="display: flex; height: 50px; justify-content: space-between;align-content: center; align-items: center; padding: 10px;">

                    <p>Update Certification Request</p>
                    <MudIcon Icon="@Icons.Material.Filled.Tag" Size="Size.Large"></MudIcon>
                </a>

            </MudCardContent>
        </MudCard>
    </div>
    <div class="col-md-4 mb-2">
        <MudCard>
            <MudCardContent Style="text-align: center">
                <a href="#" style="display: flex; height: 50px; justify-content: space-between;align-content: center; align-items: center; padding: 10px;">

                    <p>Update Address Request</p>
                    <MudIcon Icon="@Icons.Material.Filled.PinDrop" Size="Size.Large"></MudIcon>
                </a>

            </MudCardContent>
        </MudCard>
    </div>
</div>
<h3 class="sec-title mt-3">Meeting Room Booking</h3>
<div class="row mt-3">
    <div class="col">
        <MudCard>
            <MudCardContent Style="text-align: center">
                Meeting Image
            </MudCardContent>
        </MudCard>
    </div>
</div>*@

<div class="row mt-3">
    <div class="col">
        <MudCard>
            <MudCardHeader>
                <CardHeaderContent>
                    <p class="sec-title" style="font-size:16px">Target Information</p>

                </CardHeaderContent>
            </MudCardHeader>

            <MudCardContent>
                @if (TargetInfoList.Any())
                {
                    <Syncfusion.Blazor.Grids.SfGrid DataSource="TargetInfoList"
                                                    AllowFiltering="true"
                                                    AllowSorting="true">
                        <Syncfusion.Blazor.Grids.GridFilterSettings Type="Syncfusion.Blazor.Grids.FilterType.Excel"></Syncfusion.Blazor.Grids.GridFilterSettings>
                        <GridColumns>
                            <GridColumn HeaderText="S.No." Field="sNo" AutoFit="true" />
                            <GridColumn HeaderText="Target" Field="targetTitle" AutoFit="true" />
                            <GridColumn HeaderText="From" Field="fromDate" AutoFit="true" />
                            <GridColumn HeaderText="To" Field="toDate" AutoFit="true" />
                        </GridColumns>
                    </Syncfusion.Blazor.Grids.SfGrid>
                }
            </MudCardContent>
        </MudCard>




        
    </div>
</div>
<div class="row mt-3">
    <div class="col">
        <MudCard>
            <MudCardHeader>
                <CardHeaderContent>
                    <p class="sec-title" style="font-size:16px">Training Need Identification (TNI)</p>

                </CardHeaderContent>
            </MudCardHeader>

            <MudCardContent>
                @if (TrainingInfoList.Any())
                {
                    <Syncfusion.Blazor.Grids.SfGrid DataSource="TrainingInfoList"
                                                    AllowFiltering="true"
                                                    AllowSorting="true">
                        <Syncfusion.Blazor.Grids.GridFilterSettings Type="Syncfusion.Blazor.Grids.FilterType.Excel"></Syncfusion.Blazor.Grids.GridFilterSettings>
                        <GridColumns>
                            <GridColumn HeaderText="S.No." Field="sNo" AutoFit="true" />
                            <GridColumn HeaderText="Description" Field="descriptions" AutoFit="true" />
                            <GridColumn HeaderText="Reason" Field="reasons" AutoFit="true" />
                        </GridColumns>
                    </Syncfusion.Blazor.Grids.SfGrid>
                }
            </MudCardContent>
        </MudCard>




        
    </div>
</div>
<div class="row mt-3">
    <div class="col-md-4">
        <MudCard>
            <MudCardHeader>
                <CardHeaderContent>
                    <p class="sec-title" style="font-size:16px">Medical OPD Utilization</p>

                </CardHeaderContent>
            </MudCardHeader>
            @if (MedicalInfo.Count > 0)
            {

                <MudCardContent>
                    <div class="row pb-3" style="border-bottom: 1px solid #cccc">
                        <div class="col" style="display: flex; justify-content: center">
                            <div>

                                <p>Utilized Amount</p>
                                <h3 style="font-size: 1.5rem;font-weight: bold;">@MedicalInfo[0].expenseAmount.ToString("###,###,###") PKR</h3>

                            </div>
                        </div>
                        <div class="col" style="display: flex; justify-content: center; align-items: center;">
                            <MudIcon Style="width:70px;height:70px" Icon="@Icons.Material.Filled.Medication" Size="Size.Large" />
                        </div>
                    </div>
                    <div class="row pb-2 pt-2">
                        <div class="col" style="display: flex; flex-direction: column; align-items: center">
                            <p>Medical OPD Expense</p>
                            <p style="font-weight:bold">@MedicalInfo[0].entitlelimit.ToString("###,###,###") PKR</p>
                            <p style="font-weight: bold;font-size: 10px;">Annual Limit</p>
                        </div>
                        <div class="col" style="display: flex; flex-direction: column; align-items: center">
                            <p>Utilized %</p>
                            <p>@MedicalInfo[0].usagepercentage.ToString("##0.00") %</p>
                        </div>
                    </div>
                </MudCardContent>
            }
        </MudCard>
    </div>
</div>
@code {
    public double[] Ydata = { 25, 77 };
    public string[] Ylabels = {  "Absent" , "Present" };
    public double[] Mdata = { 25, 77 };
    public string[] Mlabels = { "Absent", "Present" };
    private List<MedicalOPDExpDto> MedicalInfo = new();
    private List<TargetInfoDto> TargetInfoList = new();
    private List<TrainingInfoDto> TrainingInfoList = new();
    private List<AttendanceItemDto> empMonthAttd = new ();
    private List<AttendanceItemDto> empYearAttd = new ();
    private List<LeaveBalanceDto> empAttSummary = new ();
    private List<MenuDTO> AllSelfServiceLinks = new();
    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();

        var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        var user = authState.User;
        if (user.Identity is { Name: not null })
        {

            try
            {
                MedicalInfo = await cService.GetMedicalOPDExp(user.Identity.Name);
                AllQuickLinks = await cService.GetQuickLinks(user.Identity.Name);
                AllSelfServiceLinks = await cService.GetSelfServiceLinks();
            }
            catch (Exception e)
            {
                MedicalInfo = new();
                //var tm = new ToastModel() { Content = e.Message, Title = "Error", ShowCloseButton = true };
            }

            try
            {
                TargetInfoList = await cService.GetTargetInfo(user.Identity.Name);

            }
            catch (Exception e)
            {
                TargetInfoList = new();
                //var tm = new ToastModel() { Content = e.Message, Title = "Error", ShowCloseButton = true };
            }

            try
            {
                TrainingInfoList = await cService.GetTrainingInfo(user.Identity.Name);
                Mdata = new double[2];



            }
            catch (Exception e)
            {
                TrainingInfoList = new List<TrainingInfoDto>();
                //var tm = new ToastModel() {Content = e.Message, Title = "Error", ShowCloseButton = true};
            }
            try
            {
                empMonthAttd = await cService.GetAttendanceData(user.Identity.Name, "M");
                empYearAttd = await cService.GetAttendanceData(user.Identity.Name, "Y");
                Ydata = new double[2];

                if (empMonthAttd.Any())
                {

                    double.TryParse(empMonthAttd[0].Value,out Mdata[0]);
                    double.TryParse(empMonthAttd[1].Value, out Mdata[1]);


                }

                if (empYearAttd.Any())
                {
                    double.TryParse(empYearAttd[0].Value, out Ydata[0]);
                    double.TryParse(empYearAttd[1].Value, out Ydata[1]);

                }

            }
            catch (Exception e)
            {
                TrainingInfoList = new List<TrainingInfoDto>();
                //var tm = new ToastModel() { Content = e.Message, Title = "Error", ShowCloseButton = true };
            }



            try
            {
                empAttSummary = await cService.GetAttendanceSummary(user.Identity.Name);
            }
            catch (Exception)
            {

                empAttSummary = new ();
            }

        }


    }

    private bool IsQuickLinkDglOpen = false;
    private SfDialog dlgQuickLink;
    private List<UserQuickLinkDto> AllQuickLinks = new();

    private async Task OpenCustomizeQuickLinkForm()
    {
        await dlgQuickLink.ShowAsync();
    }

    private async Task UpdateUserQuickLinkMenus()
    {
        // get current user id
        var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        var user = authState.User;

        if (user.Identity.IsAuthenticated)
        {
            string res = await cService.UpdateUserQuickLinks(AllQuickLinks, user.Identity.Name??"");
            await dlgQuickLink.HideAsync();
        }


    }

}
