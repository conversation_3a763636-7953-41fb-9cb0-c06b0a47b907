﻿using Dapper;

namespace PakSuzuki.Services
{
    public class PollDataService(ApplicationDbContext dc)
    {

        public Task<List<PollQuestionDto>> GetUsersPolls(string userId)
        {
            var pram = new { userId };
            var con = dc.Database.GetDbConnection();
            List<UserPollDto> userPolls = con.Query<UserPollDto>("dbo.GetUserPolls", pram, commandType: System.Data.CommandType.StoredProcedure)
                .ToList();


            var q = new List<PollQuestionDto>();

            foreach (var up in userPolls)
            {
                if (!q.Any(c=>c.Id==up.QuestionId))
                {
                    q.Add(new PollQuestionDto
                    {
                        Id = up.QuestionId,
                        Question = up.Question,
                        Deadline = up.Deadline,
                        Department = up.CategoryTitle??"",
                        IsOpen = up.IsOpen == 1,
                        IsSubmitted = up.IsOpen == 0,
                        Status = up.IsOpen == 1 ? "OPEN" : "CLOSED"
                    });
                }
            }


            foreach (var qq in q)
            {
                qq.Options = (from m in userPolls
                              where m.QuestionId == qq.Id
                              select new PollQuestionResponseDto
                              {
                                  Id = m.OptionId,
                                  Label = m.Option,
                                  ResponseCount = m.Count
                              }).ToList();
            }

            return Task.FromResult( q );

            
        }

        public Task<List<PollQuestionDto>> GetUsersPolls_old(string userId)
        {
            var q = (from a in dc.Posts
                where a.PostSectionId == (int)DTO.Section.Poll
                      //&& DateTime.Now < a.PostExpiryDate
                      //&& DateTime.Now>=a.PostPublishDate
                select new PollQuestionDto()
                {
                    Id = a.PostId, Question = a.PostName, Deadline = a.PostExpiryDate, Department = "TEST",
                    Status = "Open", IsSubmitted = false, IsOpen = DateTime.Now<a.PostExpiryDate
                }).ToList();

            foreach (var p in q)
            {
                p.IsOpen = (from b in dc.PostPollResults
                    where b.PostPollResultCreatedBy == userId && b.PostPollResultId == p.Id
                    select b).Any();
                p.Options = (from a in dc.PostPolls
                    where a.PostId == p.Id
                    select new PollQuestionResponseDto()
                    {
                        Id = a.PostPollId, Label = a.PostPollName, 
                        ResponseCount = dc.PostPollResults.Count(c => c.PostPollOptionId==a.PostId),
                        
                    }).ToList();
            }
            return Task.FromResult(q);
        }


        public Task<string> SavePoll(string userId, int pollId, int optionId)
        {
            var obj = new PostPollResult
            {
                PostPollId = pollId,
                PostPollResultCreatedBy = userId,
                PostPollResultCreatedDate = DateTime.Now,
                PostPollOptionId = optionId,
                PostPollResultIsActive = true,
                PostPollResultIsDel = false
            };
            dc.PostPollResults.Add(obj);
            dc.SaveChanges();
            return Task.FromResult("OK");

            
        }


        public Task<List<PollQuestionResponseDto>> GetPollsOptions(int pollId)
        {
            // all options
            var q = (from a in dc.PostPolls
                     where a.PostId==pollId &&
                     a.PostPollIsDel=="NO" &&
                     a.PostPollIsActive=="ACTIVE"
                     select new PollQuestionResponseDto
                     {
                         Id = a.PostPollId,
                         Label = a.PostPollName,
                         ResponseCount = dc.PostPollResults.Count(c => c.PostPollOptionId == a.PostPollId)
                     }).ToList();
            return Task.FromResult(q);
        }
    }
}
