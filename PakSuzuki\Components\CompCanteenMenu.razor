﻿@using FilterType = Syncfusion.Blazor.Grids.FilterType
@inject AppDataService service
@* Syncfusion Dialog Display This Month Canteen Menu *@
<SfDialog @ref="dlgMonthlyMenu" ShowCloseIcon="true" IsModal="true" Width="85vw" Visible="IsMonthlyDialogOpen" CloseOnEscape="true">
    <DialogTemplates>
        <Header>
            This Month Menu
        </Header>
        <Content>
            <Syncfusion.Blazor.Grids.SfGrid DataSource="@MonthlyMenuList" AllowSorting="true" AllowFiltering="true" Height="calc(100vh - 200px)" Width="100%">
                <GridFilterSettings Type="FilterType.Excel"></GridFilterSettings>
                <GridColumns>
                    <GridColumn Field="@nameof(CanteenMenuDto.Day)" HeaderText="Day" AutoFit="true"></GridColumn>
                    <GridColumn Visible="false" Field="@nameof(CanteenMenuDto.Code)" HeaderText="Code" AutoFit="true"></GridColumn>
                    <GridColumn Field="@nameof(CanteenMenuDto.Date)" Format="d-MMM-yyyy" HeaderText="Date" AutoFit="true"></GridColumn>
                    <GridColumn Field="@nameof(CanteenMenuDto.Category)" HeaderText="Category" AutoFit="true"></GridColumn>
                    <GridColumn Field="@nameof(CanteenMenuDto.MainDish)" HeaderText="Main Dish" AutoFit="true"></GridColumn>
                    <GridColumn Field="@nameof(CanteenMenuDto.SideDish)" HeaderText="Side Dish" AutoFit="true"></GridColumn>
                    <GridColumn Field="@nameof(CanteenMenuDto.Sweet)" HeaderText="Sweet" AutoFit="true"></GridColumn>
                    <GridColumn Field="@nameof(CanteenMenuDto.Fruit)" HeaderText="Fruit" AutoFit="true"></GridColumn>
                    <GridColumn Field="@nameof(CanteenMenuDto.Salad)" HeaderText="Salad" AutoFit="true"></GridColumn>
                </GridColumns>
            </Syncfusion.Blazor.Grids.SfGrid>
        </Content>
    </DialogTemplates>
</SfDialog>

<div class="row mb-2">
    <div class="col-md" style="display: flex;gap:10px;">
        <h3 class="sec-title">Canteen Menu:</h3>
        <MudButton Size="Size.Small" Color="Color.Primary"
                   OnClick="GetThisMonthMenu" Variant="Variant.Filled">
            View Monthly Menu
        </MudButton>

    </div>
</div>
@if (TodaysCanteenMenu.Any())
{
    <div class="row mb-2">


        @{
            var categories = TodaysCanteenMenu.Select(x => x.Category).Distinct().ToList();
            foreach (var cat in categories)
            {
                <div class="col-md-4">
                    <MudCard Style="height:100%">
                        <MudCardHeader Style="border-bottom: 1px solid gray">
                            <h3 class="sec-title">@cat.ToUpper()</h3>
                        </MudCardHeader>
                        <MudContainer>
                            <MudText Typo="Typo.h6" Class="mb-3 mt-3" Style="font-weight: bold;">@TodaysCanteenMenu[0].Date.ToString("d-MMM-yyyy")</MudText>

                            @foreach (var item in TodaysCanteenMenu.Where(x => x.Category == cat))
                            {
                                <div class="row">
                                    <div class="col-md">
                                        <p style="margin-bottom:15px"><b>Main Dish</b>:<br/> <MudText Typo="Typo.h6">@item.MainDish</MudText></p>
                                        @if (!string.IsNullOrEmpty(item.SideDish))
                                        {
                                            <p style="margin-bottom:15px"><b>Side Dish</b>:<br/> <MudText Typo="Typo.h6">@item.SideDish</MudText></p>
                                        }
                                        @if (!string.IsNullOrEmpty(item.Sweet))
                                        {
                                            <p style="margin-bottom:15px"><b>Sweet</b>:<br/> <MudText Typo="Typo.h6">@item.Sweet</MudText></p>
                                        }
                                        @if (!string.IsNullOrEmpty(item.Salad))
                                        {
                                            <p style="margin-bottom:15px"><b>Salad</b>:<br/> <MudText Typo="Typo.h6">@item.Salad</MudText></p>
                                        }
                                        @if (!string.IsNullOrEmpty(item.Fruit))
                                        {
                                            <p style="margin-bottom:15px"><b>Fruit</b>:<br/> <MudText Typo="Typo.h6">@item.Fruit</MudText></p>
                                        }
                                    </div>
                                </div>
                            }

                        </MudContainer>
                        @*<MudCardActions>
                            
                        </MudCardActions>*@
                    </MudCard>
                </div>
            }
        }


    </div>
}


@code
{
    private List<CanteenMenuDto> TodaysCanteenMenu { get; set; } = new();
    private SfDialog dlgMonthlyMenu;
    private bool IsMonthlyDialogOpen = false;
    private List<CanteenMenuDto> MonthlyMenuList = new();

    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();

        TodaysCanteenMenu = await service.GetTodaysMenu(DateTime.Today);
    }

    private async Task GetThisMonthMenu()
    {
        MonthlyMenuList = await service.GetThisMonthMenu();
        await dlgMonthlyMenu.ShowAsync();
    }
}