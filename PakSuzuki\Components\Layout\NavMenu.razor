﻿@using MudBlazor
@using PakSuzuki.DTO
@inject PakSuzuki.Services.AppDataService service
@inject CorporateService cpService
@inject IWebHostEnvironment env
@using System.Data

@inject AuthenticationStateProvider AuthenticationStateProvider


<style>
    .mud-nav-link {
        line-height: 1;
        font-size: 12px;
        display: flex;
        align-items: center;
    }
</style>

<SfDialog Width="700" @ref="locationDialog" ShowCloseIcon="true" CloseOnEscape="true" IsModal="true" Visible="isLocationShiftVisible">
    <DialogTemplates>
        <Header>All Shifts and Locations</Header>
        <Content>
            <SfGrid DataSource="allLocationsList" AllowFiltering="true" AllowSorting="true" Height="300px">
                <GridFilterSettings Type="Syncfusion.Blazor.Grids.FilterType.Excel"></GridFilterSettings>
                <GridColumns>
                    <GridColumn AutoFit="true" Field="@nameof(Model.ShiftLocation.LocationCode)" HeaderText="Location Code" Width="100"></GridColumn>
                    <GridColumn AutoFit="true" Field="@nameof(Model.ShiftLocation.LocationTxt)" HeaderText="Location" Width="100"></GridColumn>
                    <GridColumn AutoFit="true" Field="@nameof(Model.ShiftLocation.EmployeeShiftCode)" HeaderText="Shift Code" Width="100"></GridColumn>
                    <GridColumn AutoFit="true" Field="@nameof(Model.ShiftLocation.ShiftStart)" HeaderText="Shift Start" Width="100"></GridColumn>
                    <GridColumn AutoFit="true" Field="@nameof(Model.ShiftLocation.ShiftEnd)" HeaderText="Shift End" Width="100"></GridColumn>
                </GridColumns>
            </SfGrid>
        </Content>
    </DialogTemplates>
</SfDialog>
<div class="userprofilecard">
    <AuthorizeView>
        <Authorized>
            @{
                var profilePic = "/uploadUserPics/missing.jpg";
                var userId = context.User.Identity.Name.Replace("\\", "-");

                string path = $"{env.WebRootPath}\\uploadUserPics\\" + userId + ".png";

                if (File.Exists(path))
                {
                    profilePic = "/uploadUserPics/" + userId + ".png";

                }

                <a href="/employee">

                    <img src="@profilePic" alt="Alternate Text"
                         style="width:70px;height:70px;object-fit:cover;border-radius: 50%" />
                </a>
                <a href="/employee">
                    <h3>@userName</h3>
                </a>
                <p>@userDesignation</p>
                <div class="userlocationcard" style="display:flex;align-items:center">
                    <div style="flex:1">


                        <div>@userLocation</div>
                        <div>@userShiftTitle</div>
                    </div>
                    <div style="padding:10px;cursor:pointer" @onclick="OpenAllShiftsAndLocations">
                        <MudIcon Icon="@Icons.Material.Filled.Visibility" Size="Size.Small"></MudIcon>
                    </div>
                </div>
            }
        </Authorized>
    </AuthorizeView>
    <SfTextBox CssClass="m-1" Width="95%" @bind-Value="searchText" Placeholder="Search menu..." FloatLabelType="FloatLabelType.Never" />
</div>

<MudNavMenu>

    @if (userMenu.Any())
    {
        var filteredMenu = GetFilteredMenu(userMenu, searchText);
        var topLevelMenus = filteredMenu.Where(m => m.ParentId == 0).OrderBy(m => m.SortOrder).ThenBy(m => m.Name).ToList();

        foreach (var mi in topLevelMenus)
        {
            var subMenu = filteredMenu.Where(mm => mm.ParentId == mi.Id).OrderBy(mm => mm.SortOrder).ThenBy(mm => mm.Name).ToList();
            <CompMenuItem menu="@mi" userSubMenu="@subMenu" userMenu="@filteredMenu" />
        }
    }


</MudNavMenu>

@code  {

    private string searchText = "";

    private List<MenuDTO> userMenu = new List<MenuDTO>();
    private string? userShift = "";
    private string? userShiftTitle = "";
    private string? userLocation = "";
    private string? userName = "";
    private string? userDesignation = "";
    private string? location_code;
    private string? employee_shift_code;
    private string? department_name;
    private SfDialog locationDialog;
    private bool isLocationShiftVisible = false;
    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();

        var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        var user = authState.User;
        if (user.Identity is { IsAuthenticated: true, Name: not null })
            userMenu = await service.GetUserMenus(user.Identity.Name, 1);


        var userPrincipalName = user.Identity?.Name;
        if (userPrincipalName != null)
        {
            var EmpData = cpService.GetEmployeeData(userPrincipalName);
            if (EmpData.Rows.Count > 0)
            {
                userName = EmpData.Rows[0]["FullName"].ToString();
                userDesignation = EmpData.Rows[0]["JobTitle"].ToString();
                userLocation = EmpData.Rows[0]["location_text"].ToString();
                userShift = EmpData.Rows[0]["employee_shift_code"].ToString();

                location_code = EmpData.Rows[0]["location_code"].ToString();
                employee_shift_code = EmpData.Rows[0]["employee_shift_code"].ToString();
                department_name = EmpData.Rows[0]["department_name"].ToString();
                // userShiftTitle = "--";

                userLocation = "Head Office & Plant";
                userShiftTitle = "7:15 AM to 5:15 PM";
            }

            (userLocation, userShiftTitle) = await cpService.GetShiftAndLocationInfo(location_code, employee_shift_code);

        }


    }
    private List<LocationDto> allLocationsList = new List<LocationDto>();
    private async Task OpenAllShiftsAndLocations()
    {
        //var parameters = new DialogParameters();
        //parameters.Add("location_code", location_code);
        //parameters.Add("employee_shift_code", employee_shift_code);
        //var dialog = DialogService.Show<AllShiftsAndLocations>("All Shifts and Locations", parameters);
        //var result = await dialog.Result;
        allLocationsList = await cpService.GetAllShiftsAndLocations();
        await locationDialog.ShowAsync();
    }
    private List<MenuDTO> GetFilteredMenu(List<MenuDTO> menu, string searchText)
    {
        var filteredMenu = new List<MenuDTO>();

        foreach (var item in menu)
        {
            if (item.Name.Contains(searchText, StringComparison.OrdinalIgnoreCase))
            {
                filteredMenu.Add(item);
                AddParentMenus(menu, filteredMenu, item.ParentId);
            }
        }

        return filteredMenu.Distinct().ToList();
    }

    private void AddParentMenus(List<MenuDTO> menu, List<MenuDTO> filteredMenu, int? parentId)
    {
        if (parentId == null || parentId == 0)
            return;

        var parentMenu = menu.FirstOrDefault(m => m.Id == parentId);
        if (parentMenu != null && !filteredMenu.Contains(parentMenu))
        {
            filteredMenu.Add(parentMenu);
            AddParentMenus(menu, filteredMenu, parentMenu.ParentId);
        }
    }
}