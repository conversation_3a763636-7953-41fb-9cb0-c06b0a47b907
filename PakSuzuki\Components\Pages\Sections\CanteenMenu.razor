﻿@page "/setup/canteenmenu"
@inject ISnackbar Snackbar
@inject AppDataService service
@inject AuthenticationStateProvider AuthenticationStateProvider

@using PakSuzuki.DTO
<div style="display:flex; gap: 10px;align-items: center; justify-content: space-between">

<MudText Typo="Typo.h5">Canteen Menu</MudText> <a href="/CanteenMenu/Sample Canteen Menu.xlsx">Download Sample Sheet</a>

</div>
<div class="row mb-2">
    <div class="col-md-2">
        <Syncfusion.Blazor.Calendars.SfDatePicker @bind-Value="DateFrom" FloatLabelType="FloatLabelType.Always"
                                                  Placeholder="From Date" ShowClearButton="true" ShowTodayButton="true"></Syncfusion.Blazor.Calendars.SfDatePicker>
    </div>
    <div class="col-md-2">
        <Syncfusion.Blazor.Calendars.SfDatePicker @bind-Value="DateTo" FloatLabelType="FloatLabelType.Always"
                                                  Placeholder="To Date" ShowClearButton="true" ShowTodayButton="true"></Syncfusion.Blazor.Calendars.SfDatePicker>
    </div>
    <div class="col-md pt-4">
        <label>&nbsp;</label>
        <MudButton OnClick="SearchCanteenMenu" Size="Size.Small" Variant="Variant.Filled" Color="Color.Primary" StartIcon="@Icons.Material.Filled.Search">Search</MudButton>
    </div>

    <div class="col-md pt-4">
        @* Input file - user have to browse to attach .csv file only *@
        <MudFileUpload T="IBrowserFile" Accept=".xlsx" MaxFiles="1" OnFilesChanged="OnInputFileChanged">
            <ButtonTemplate>
                <MudButton HtmlTag="label"
                           Variant="Variant.Filled"
                           Color="Color.Primary"
                           Size="Size.Small"
                           StartIcon="@Icons.Material.Filled.CloudUpload"
                           for="@context">
                    Browse Excel
                </MudButton>
            </ButtonTemplate>
        </MudFileUpload>
    </div>
    <div class="col-md-4">
        @if (_file is { } file)
        {
            <MudText Class="">Selected file: @_file.Name</MudText>
            <MudButton Color="Color.Secondary" OnClick="UploadFile" Disabled="@_isUploading" Variant="Variant.Filled" Size="Size.Small">
                @if (_isUploading)
                {
                    <MudProgressCircular Class="ms-n1" Size="Size.Small" Indeterminate="true" />
                    <MudText Class="ms-2">Uploading</MudText>
                }
                else
                {
                    <MudText>Upload to Server</MudText>
                }
            </MudButton>
        }
    </div>
</div>
<div class="row">
    <div class="col">
        @* syncfusion menus grid filterable and sortable *@
        <Syncfusion.Blazor.Grids.SfGrid DataSource="@menuList" AllowSorting="true" AllowFiltering="true" Width="100%">
            <GridFilterSettings Type="Syncfusion.Blazor.Grids.FilterType.Excel"></GridFilterSettings>
            <GridColumns>
                <GridColumn Field="@nameof(CanteenMenuDto.Day)" HeaderText="Day" AutoFit="true"></GridColumn>
                <GridColumn Field="@nameof(CanteenMenuDto.Code)" HeaderText="Code" AutoFit="true"></GridColumn>
                <GridColumn Field="@nameof(CanteenMenuDto.Date)" Format="d-MMM-yyyy" HeaderText="Date" AutoFit="true"></GridColumn>
                <GridColumn Field="@nameof(CanteenMenuDto.Category)" HeaderText="Category" AutoFit="true"></GridColumn>
                <GridColumn Field="@nameof(CanteenMenuDto.MainDish)" HeaderText="Main Dish" AutoFit="true"></GridColumn>
                <GridColumn Field="@nameof(CanteenMenuDto.SideDish)" HeaderText="Side Dish" AutoFit="true"></GridColumn>
                <GridColumn Field="@nameof(CanteenMenuDto.Sweet)" HeaderText="Sweet" AutoFit="true"></GridColumn>
                <GridColumn Field="@nameof(CanteenMenuDto.Fruit)" HeaderText="Fruit" AutoFit="true"></GridColumn>
                <GridColumn Field="@nameof(CanteenMenuDto.Salad)" HeaderText="Salad" AutoFit="true"></GridColumn>
            </GridColumns>
        </Syncfusion.Blazor.Grids.SfGrid>
    </div>
</div>
@code {
    private IBrowserFile _file;
    private bool _isUploading;
    private DateTime DateFrom = new DateTime(DateTime.Today.Year, DateTime.Today.Month, 1);
    private DateTime DateTo = (new DateTime(DateTime.Today.Year, DateTime.Today.Month, 1)).AddMonths(1).AddDays(-1);
    private List<PakSuzuki.DTO.CanteenMenuDto> menuList = new List<DTO.CanteenMenuDto>();
    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();
        // Set DateFrom as first day of this month
        DateFrom = new DateTime(DateTime.Today.Year, DateTime.Today.Month, 1);
        // Set DateTo as last day of this month
        DateTo = (new DateTime(DateTime.Today.Year, DateTime.Today.Month, 1)).AddMonths(1).AddDays(-1);
        menuList = await service.GetCanteenMenu(DateFrom, DateTo);
    }

    private void OnInputFileChanged(InputFileChangeEventArgs e)
    {
        _file = e.File;
    }
    private async Task UploadFile()
    {
        // Save file in CandteenMenu folder. if folder does not exist then first create it
        if (_file == null)
        {
            return;
        }
        _isUploading = true;

        //save stream to excel file in CanteenMenu folder
        //if folder does not exist then create it
        var folder = System.IO.Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "CanteenMenu");
        if (!Directory.Exists(folder))
        {
            Directory.CreateDirectory(folder);
        }

        var filePath = System.IO.Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "CanteenMenu", _file.Name);

        // if file already exist on "filePath" then delete it
        if (System.IO.File.Exists(filePath))
        {
            System.IO.File.Delete(filePath);
        }
        using (var stream = new FileStream(filePath, FileMode.Create))
        {

            await _file.OpenReadStream().CopyToAsync(stream);
        }
        Snackbar.Add($"Excel File Uploaded", Severity.Info);
        var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        var user = authState.User;
        var userId = "";
        if (user.Identity is { Name: not null })
        {
            userId = user.Identity.Name;
        }

        var msg = await service.UploadMenuByExcelFile(filePath, userId);

        if (msg != "OK")
        {
            Snackbar.Add(msg, Severity.Error);


        }
        else
        {
            Snackbar.Add($"Excel File Uploaded", Severity.Info);

        }


        menuList = await service.GetCanteenMenu(DateFrom, DateTo);


        _isUploading = false;


    }

    public async Task SearchCanteenMenu()
    {
        menuList = await service.GetCanteenMenu(DateFrom, DateTo);
    }


    public async Task DeleteMenuItem(int id)
    {
        var result = await service.DeleteCanteenMenuItem(id);
        if (result == "OK")
        {
            menuList = await service.GetCanteenMenu(DateFrom, DateTo);
            Snackbar.Add("Record deleted successfully", Severity.Success);
        }
        else
        {
            Snackbar.Add("Error deleting record", Severity.Error);
        }
    }
}

