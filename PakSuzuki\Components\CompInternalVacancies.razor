﻿@using PakSuzuki.DTO
@inject AppDataService service

<link href="/styles/news-card.css" rel="stylesheet" />
<style>
    .mm {
        display: flex;
        gap: 10px;
        align-items: center;
    }
</style>

<SfDialog @ref="detailForm" ShowCloseIcon="true" IsModal="true" Width="900px" CloseOnEscape="true"
          Visible="IsDetailVisible">
    <DialogTemplates>
        <Header>@Title</Header>
        <Content>
            <div class="popup">


                <div class="row">
                    <div class="col-md">
                        <h2 class="card-title" style="font-size: 18px;">@_postDetail.postName</h2>
                        <div>
                            <div class="mm"><MudIcon Size="Size.Small" Icon="@Icons.Material.Filled.Factory"></MudIcon> <b>Division:</b> @_postDetail.JobDivisionName</div>
                            <div class="mm"><MudIcon Size="Size.Small" Icon="@Icons.Material.Filled.Diversity3"></MudIcon> <b>Department:</b> @_postDetail.JobDepartmentName</div>
                            <div class="mm"><MudIcon Size="Size.Small" Icon="@Icons.Material.Filled.LocationOn"></MudIcon> <b>Location:</b> @_postDetail.JobCity | <MudIcon Size="Size.Small" Icon="@Icons.Material.Filled.Tag"></MudIcon> @_postDetail.JobType</div>
                            <div class="mm"><MudIcon Size="Size.Small" Icon="@Icons.Material.Filled.Schedule"></MudIcon> <b>Last Date to Apply</b> @_postDetail.JobLastDateToApply!.Value.ToString("d-MMM-yyyy")</div>
                        </div>

                        <h3 class="mt-3"><b>Job Description</b></h3>
                        @((MarkupString)_postDetail.postContentHTML)
                    </div>
                </div>
                <div class="row">
                    <div class="col-md">
                        <h4 style="font-size:17px">Tags</h4>
                        <p></p>
                        @foreach (var tag in _postDetail.Tags)
                        {
                            <span class="tag">@tag</span>
                        }
                    </div>
                </div>
                
                @if (_postDetail.Attachments.Any())
                {

                    <div class="row mt-2">
                        <div class="col-md">
                            <h4 style="font-size:17px;margin-bottom:15px">Attachments</h4>
                            <p></p>
                            @foreach (var tag in _postDetail.Attachments)
                            {
                                <a target="_blank" style="margin:5px;" class="tag" href="@tag.Url">@tag.Name</a>
                            }
                        </div>
                    </div>

                }
            </div>
            <div class="row mt-3">
                <div class="col">
                    <p>Publish Date: @_postDetail.postPublishDateStr</p>

                </div>
            </div>
        </Content>
    </DialogTemplates>

</SfDialog>
@if (Items.Any())
{
    @if (!string.IsNullOrEmpty(Title))
    {
        <div class="view-all-container mb-2">
            <h3 class="sec-title">@Title</h3>
            @if (!string.IsNullOrEmpty(DetailPageUrl))
            {
                <a class="view-all" href="@DetailPageUrl">View All</a>
            }
        </div>

    }
    <div class="row">
        <div class="col-md">
            <div class="newscard">
                @foreach (var k in Items)
                {
                    <div class="news" @onclick="() => ShowDetail(k.postId)">
                        <div>

                            <img src="/images/joblgo.png" alt="" />
                        </div>
                        <div class="news-content">
                            <h3>@k.postName</h3>
                            <div>
                                <div class="mm"><MudIcon Size="Size.Small" Icon="@Icons.Material.Filled.Factory"></MudIcon> <b>Division:</b> @k.JobDivisionName</div>
                                <div class="mm"><MudIcon Size="Size.Small" Icon="@Icons.Material.Filled.Diversity3"></MudIcon> <b>Department:</b> @k.JobDepartmentName</div>
                                <div class="mm"><MudIcon Size="Size.Small" Icon="@Icons.Material.Filled.LocationOn"></MudIcon> <b>Location:</b> @k.JobCity | <MudIcon Size="Size.Small" Icon="@Icons.Material.Filled.Tag"></MudIcon> @k.JobType</div>
                                <div class="mm"><MudIcon Size="Size.Small" Icon="@Icons.Material.Filled.Schedule"></MudIcon> <b>Last Date to Apply</b> @k.JobLastDateToApply!.Value.ToString("d-MMM-yyyy")</div>
                            </div>

                        </div>
                    </div>
                    <div class="separator"></div>

                }



            </div>
        </div>
    </div>
}




@code {

    [Parameter]
    public string? Title { get; set; }

    [Parameter] public string? DetailPageUrl { get; set; }
    [Parameter]
    public List<PostDTO> Items { get; set; } = new();

    public bool IsDetailVisible { get; set; }
    private PostDTO? _postDetail;

    private SfDialog detailForm;

    private async Task ShowDetail(int postId)
    {
        _postDetail = await service.GetPostDetail(postId);
        if (_postDetail != null)
        {
            await detailForm.ShowAsync();
        }
    }

}