﻿@page "/myprofile"
@rendermode InteractiveServer
@inject IWebHostEnvironment env
@inject AuthenticationStateProvider AuthenticationStateProvider
@inject NavigationManager NavMgr
@inject CorporateService cpService
<style>
    .profile-container {
        max-width: 800px;
        margin: 0 auto;
        background-color: #fff;
        padding: 20px;
        border-radius: 5px;
        box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    }

    .profile-header {
        display: flex;
        align-items: center;
        margin-bottom: 20px;
    }

    .profile-picture {
        width: 150px;
        height: 150px;
        border-radius: 50%;
        object-fit: cover;
        margin-right: 20px;
    }

    .profile-name {
        font-size: 24px;
        font-weight: bold;
    }

    .profile-info {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 10px;
    }

    .info-item {
        margin-bottom: 10px;
    }

    .info-label {
        font-weight: bold;
        display: block;
    }

    .change-picture-btn {
        background-color: #4CAF50;
        color: white;
        padding: 10px 15px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 16px;
        margin-top: 10px;
    }

    .change-picture-btn:hover {
        background-color: #45a049;
    }
</style>
<SfToast @ref="toastObj"/>
<div class="profile-container mt-4">
    <h3 class="sec-title mt-3 mb-4">My Profile</h3>
    <div class="profile-header">
        @*<img src="@(_imageData ?? "/employees/1.jpeg")" alt="Profile Picture" class="profile-picture" id="profilePicture" />*@
        <MudImage Src="@profilePic" Alt="User Picture" Elevation="25" Class="profile-picture"/>

        <div>
            <h1 class="profile-name">@employee.Name</h1>
            @*<InputFile class="change-picture-btn" accept=".jpg,.jpeg,.png" />*@
            <MudFileUpload T="IBrowserFile" Accept=".png, .jpg, .jpeg" FilesChanged="UploadFiles2" MaximumFileCount="1"
                           Class="change-picture-btn">
                <ButtonTemplate Context="ctx">
                    <MudButton HtmlTag="label"
                               Variant="Variant.Filled"
                               Color="Color.Primary"
                               StartIcon="@Icons.Material.Filled.CloudUpload"
                               for="@ctx.Id">
                        Add Picture
                    </MudButton>
                </ButtonTemplate>
            </MudFileUpload>

        </div>
    </div>
    <div class="profile-info">
        <div class="info-item">
            <span class="info-label">Employee Code:</span>
            <span>@userCode</span>
        </div>
        <div class="info-item">
            <span class="info-label">Department:</span>
            <span>@userDepartment</span>
        </div>
        <div class="info-item">
            <span class="info-label">Designation:</span>
            <span>@userDesignation</span>
        </div>
        <div class="info-item">
            <span class="info-label">Shift:</span>
            <span>@userShiftTitle</span>
        </div>
        <div class="info-item">
            <span class="info-label">Location:</span>
            <span>@userLocation</span>
        </div>

        <div class="info-item">

        </div>
        <div class="info-item">
            <MudButton Size="Size.Small" Variant="Variant.Filled" Color="Color.Primary"
                       StartIcon="@Icons.Material.Filled.Save" OnClick="SaveImage">Save
            </MudButton>
        </div>
    </div>
</div>

@code {

    private readonly EmployeeDto employee = new();
    private SfToast? toastObj;

    private string profilePic = "images/profilepic.png";

    private IBrowserFile? imgfile;

    private string userDesignation = "";
    private string userLocation = "";
    private string userShiftTitle = "";
    private string locationCode = "";
    private string employeeShiftCode = "";
    private string userDepartment = "";
    private string userCode = "";

    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();

        var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        var user = authState.User;
        profilePic = "/uploadUserPics/missing.jpg";
        if (user.Identity is { Name: not null })
        {
            using var empData = cpService.GetEmployeeData(user.Identity.Name);

            if (empData.Rows.Count > 0)
            {
                userCode = empData.Rows[0]["empcode"].ToString()??"";
                userDesignation = empData.Rows[0]["JobTitle"].ToString()??"";
                userLocation = empData.Rows[0]["location_text"].ToString()??"";

                locationCode = empData.Rows[0]["location_code"].ToString()??"";
                employeeShiftCode = empData.Rows[0]["employee_shift_code"].ToString()??"";
                userDepartment = empData.Rows[0]["department_name"].ToString()??"";

                // userShiftTitle = "--";

                userLocation = "Head Office & Plant";
                userShiftTitle = "7:15 AM to 5:15 PM";
            }

            (userLocation, userShiftTitle) = await cpService.GetShiftAndLocationInfo(locationCode, employeeShiftCode);

            var userId = user.Identity.Name.Replace("\\", "-");
            var path = $@"{env.WebRootPath}\uploadUserPics\" + userId + ".png";
            if (File.Exists(path))
            {
                profilePic = "/uploadUserPics/" + userId + ".png";
            }
        }
    }

    private async void UploadFiles2(IBrowserFile file)
    {
        imgfile = file;
        var memoryStream = new MemoryStream();
        await using var fileStream = file.OpenReadStream(long.MaxValue);
        await fileStream.CopyToAsync(memoryStream);
        var bytes = memoryStream.ToArray();
        profilePic = "data:image/png;base64," + Convert.ToBase64String(bytes);
        //fileSize = await uploadObj.BytesToSizeAsync(file.Size);
        Console.WriteLine("test");
        StateHasChanged();
    }


    private async Task SaveImage()
    {
        var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        var user = authState.User;
        if (user.Identity is { Name: not null })
        {
            //string dirPath = $"{env.WebRootPath}\\uploadUserPics";
            var dirPath = $"{env.WebRootPath}\\uploadUserPics";
            if (Directory.Exists(dirPath) == false)
            {
                Directory.CreateDirectory(dirPath);
            }

            if (imgfile != null)
            {
                var stream = imgfile.OpenReadStream(30 * 1024 * 1024);
                var path = dirPath + "\\" + user.Identity.Name.Replace("\\", "-") + ".png";

                var fs = File.Create(path);
                await stream.CopyToAsync(fs);
                stream.Close();
                fs.Close();
                imgfile = null;
                var mm = new ToastModel
                {
                    Title = "Profile Pic",
                    Content = "Profile picture has saved successfully",
                    ShowCloseButton = true,
                    ShowProgressBar = true,
                    Timeout = 5000
                };
                await toastObj!.ShowAsync(mm);
            }
        }
    }

}


