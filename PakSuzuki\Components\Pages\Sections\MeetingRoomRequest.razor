﻿@page "/meetingroomrequests"
@inject MeetingRoomService Service
@inject CorporateService CService
@using PakSuzuki.DTO
@using Syncfusion.Blazor.Schedule
@using ButtonType = MudBlazor.ButtonType
@using FilterType = Syncfusion.Blazor.DropDowns.FilterType
@inject AuthenticationStateProvider AuthenticationStateProvider

<div class="row">
    <div class="col mb-2" style="display:flex;gap:10px;align-items:center">
        <MudText Typo="Typo.h5">Meeting Room Requisitions</MudText>
        <MudButton StartIcon="@Icons.Material.Filled.AddTask" Color="Color.Primary" Size="Size.Small"
                   @onclick="OpenCreateForm">
            Add New Request
        </MudButton> |
        <MudButton StartIcon="@Icons.Material.Filled.Refresh" Color="Color.Primary" Size="Size.Small"
                   @onclick="RefreshList">
            Refresh
        </MudButton>
    </div>
</div>

<div class="row">
    <div class="col-md mb-2">
        <SfDropDownList DataSource="@AllLocations" FloatLabelType="FloatLabelType.Always" Placeholder="Location"
                        AllowFiltering="true" FilterType="FilterType.Contains"
                        @bind-Value="FilterLocationId" TValue="int?" TItem="OfficeLocationDto">
            <DropDownListFieldSettings Text="@nameof(OfficeLocationDto.Location)" Value="@nameof(OfficeLocationDto.Id)"></DropDownListFieldSettings>
            <DropDownListEvents TValue="int?" TItem="OfficeLocationDto" ValueChange="OnFilterLocationChange"></DropDownListEvents>
        </SfDropDownList>
    </div>
    <div class="col-md mb-2">
        <SfDropDownList DataSource="@AllFilterMeetingRooms" FloatLabelType="FloatLabelType.Always" Placeholder="Meeting room"
                        AllowFiltering="true" FilterType="FilterType.Contains"
                        @bind-Value="FilterMeetingRoomId" TValue="int?" TItem="MeetingRoomDto">
            <DropDownListFieldSettings Text="@nameof(MeetingRoomDto.MeetingRoomName)" Value="@nameof(MeetingRoomDto.Id)"></DropDownListFieldSettings>
            <DropDownListEvents TValue="int?" TItem="MeetingRoomDto" ValueChange="OnFilterMeetingRoomChange"></DropDownListEvents>
        </SfDropDownList>
    </div>
</div>
<SfDialog @ref="@_dialog" Width="600px" ShowCloseIcon="true" IsModal="true" Visible="IsFormVisible">
    <DialogTemplates>
        <Header>Request Detail</Header>
        <Content>
            <EditForm Model="_request" OnValidSubmit="SaveRequest">
                <DataAnnotationsValidator/>
                @*<ValidationSummary />*@
                <div class="row">
                    <div class="col">
                        <SfTextBox @bind-Value="_request.Subject" Placeholder="Subject" FloatLabelType="FloatLabelType.Always"></SfTextBox>
                        <ValidationMessage For="@(() => _request.Subject)"/>
                    </div>
                </div>
                
                @* Location and Meeting Room Dropdown Selections *@
                <div class="row">
                    <div class="col">
                        <SfDropDownList DataSource="@AllLocations" FloatLabelType="FloatLabelType.Always" Placeholder="Location"
                                        AllowFiltering="true" FilterType="FilterType.Contains"
                                        @bind-Value="_request.LocationId" TValue="int?" TItem="OfficeLocationDto">
                            <DropDownListFieldSettings Text="@nameof(OfficeLocationDto.Location)" Value="@nameof(OfficeLocationDto.Id)"></DropDownListFieldSettings>
                            <DropDownListEvents TValue="int?" TItem="OfficeLocationDto" ValueChange="OnLocationChange"></DropDownListEvents>
                        </SfDropDownList>
                        <ValidationMessage For="@(() => _request.LocationId)"/>
                    </div>
                    <div class="col">
                        <SfDropDownList DataSource="@AllMeetingRooms" FloatLabelType="FloatLabelType.Always" Placeholder="Meeting Room"
                                        AllowFiltering="true" FilterType="FilterType.Contains"
                                        @bind-Value="_request.MeetingRoomId" TValue="int?" TItem="MeetingRoomDto">
                            <DropDownListFieldSettings Text="@nameof(MeetingRoomDto.MeetingRoomName)" Value="@nameof(MeetingRoomDto.Id)"></DropDownListFieldSettings>
                        </SfDropDownList>
                        <ValidationMessage For="@(() => _request.MeetingRoomId)"/>
                    </div>
                </div>
                @* Date From and Date To *@
                <div class="row">
                    <div class="col">
                        @* Syncfusion Date Time picker *@
                        <SfDateTimePicker Placeholder="From Date/Time" FloatLabelType="FloatLabelType.Always"
                                          ShowClearButton="true" ShowTodayButton="true"
                                          Min="DateTime.Now"
                                          @bind-Value="_request.StartTime" Format="d-MMM-yyyy hh:mm tt">
                        </SfDateTimePicker>
                        <ValidationMessage For="@(() => _request.StartTime)"/>
                    </div>
                    <div class="col">
                        <SfDateTimePicker Placeholder="Tp Date/Time" FloatLabelType="FloatLabelType.Always"
                                          Min="DateTime.Now"
                                          ShowClearButton="true" ShowTodayButton="true"
                                          @bind-Value="_request.EndTime" Format="d-MMM-yyyy hh:mm tt">
                        </SfDateTimePicker>
                        <ValidationMessage For="@(() => _request.EndTime)"/>
                    </div>
                </div>
                <div class="row mt-2">
                    <div class="col">
                        <MudButton ButtonType="ButtonType.Submit"
                                   Size="Size.Small" Color="Color.Primary" Variant="Variant.Filled">
                            Save
                        </MudButton>
                    </div>
                </div>
            </EditForm>
        </Content>
    </DialogTemplates>
</SfDialog>


@* create syncfusion tabs has two tabs first is Schedule View and Second is Detail View *@


<SfTab>
    <TabItems>
        <Syncfusion.Blazor.Navigations.TabItem >
            <ChildContent>
                <TabHeader Text="Schedule View"></TabHeader>

            </ChildContent>
            <ContentTemplate>
                <SfSchedule TValue="MeetingRoomRequestDto" Height="calc(100vh - 205px)" Width="100%" AllowDragAndDrop="false"
                            Timezone="Asia/Karachi"
                            AllowMultiDrag="false" AllowResizing="false" Readonly="true">
                    <ScheduleEventSettings DataSource="@_myBrRequests"></ScheduleEventSettings>
                    <ScheduleQuickInfoTemplates TemplateType="TemplateType.Both">
                        <HeaderTemplate>
                            @{
                                if (context is MeetingRoomRequestDto obj)
                                {
                                    <h1 style="padding:5px; color:white;background: #0022df">@obj.Subject</h1>
                                }
                            }
                        </HeaderTemplate>
                        <ContentTemplate>
                            @{
                                if (context is MeetingRoomRequestDto obj)
                                {
                                    <div class="row">
                                        <div class="col">
                                            <SfTextBox @bind-Value="obj.MeetingRoomName"
                                                       Placeholder="Meeting Room"
                                                       FloatLabelType="FloatLabelType.Always"
                                                       Readonly="true"></SfTextBox>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col">
                                            <SfTimePicker @bind-Value="obj.StartTime" Format="hh:mm"
                                                          Placeholder="Start Time"
                                                          FloatLabelType="FloatLabelType.Always"
                                                          Readonly="true"
                                            ></SfTimePicker>
                                        </div>
                                        <div class="col">
                                            <SfTimePicker @bind-Value="obj.EndTime" Format="hh:mm tt"
                                                          Placeholder="End Time"
                                                          FloatLabelType="FloatLabelType.Always"
                                                          Readonly="true"
                                            ></SfTimePicker>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col">
                                            <SfTextBox @bind-Value="obj.CreatedByUserId"
                                                       Placeholder="Request By (Code)"
                                                       FloatLabelType="FloatLabelType.Always"
                                                       Readonly="true"></SfTextBox>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col">
                                            <SfTextBox @bind-Value="obj.RequestedByName"
                                                       Placeholder="Request By"
                                                       FloatLabelType="FloatLabelType.Always"
                                                       Readonly="true"></SfTextBox>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col">
                                            <SfTextBox @bind-Value="obj.RequestedByEmail"
                                                       Placeholder="Request By (Email)"
                                                       FloatLabelType="FloatLabelType.Always"
                                                       Readonly="true"></SfTextBox>
                                        </div>
                                    </div>
                                }
                            }
                        </ContentTemplate>
                        <FooterTemplate>
                            
                        </FooterTemplate>
                    </ScheduleQuickInfoTemplates>

                    <ScheduleViews>
                        <ScheduleView Option="View.Day"></ScheduleView>
                        <ScheduleView Option="View.Week"></ScheduleView>
                        <ScheduleView Option="View.WorkWeek" IsSelected="true"></ScheduleView>
                        <ScheduleView Option="View.Month"></ScheduleView>
                        <ScheduleView Option="View.Agenda"></ScheduleView>
                    </ScheduleViews>
                </SfSchedule>

            </ContentTemplate>
        </Syncfusion.Blazor.Navigations.TabItem>
        <Syncfusion.Blazor.Navigations.TabItem >
            <ChildContent>
                <TabHeader Text="My Requests"></TabHeader>
            </ChildContent>
            <ContentTemplate>
                <SfGrid DataSource="@_myBrRequests" AllowFiltering="true" AllowSorting="true">
                    <GridFilterSettings Type="Syncfusion.Blazor.Grids.FilterType.Excel"></GridFilterSettings>
                    <GridColumns>

                        <GridColumn Field="@nameof(MeetingRoomRequestDto.Subject)" HeaderText="Subject" AutoFit="true"></GridColumn>
                        <GridColumn Field="@nameof(MeetingRoomRequestDto.StartTime)" Format="d-MMM-yyyy h:mm tt" HeaderText="Start Time" AutoFit="true"></GridColumn>
                        <GridColumn Field="@nameof(MeetingRoomRequestDto.EndTime)" Format="d-MMM-yyyy h:mm tt" HeaderText="End Time" AutoFit="true"></GridColumn>
                        <GridColumn Field="@nameof(MeetingRoomRequestDto.OfficeLocation)" HeaderText="Location" AutoFit="true"></GridColumn>
                        <GridColumn Field="@nameof(MeetingRoomRequestDto.MeetingRoomName)" HeaderText="Meeting Room" AutoFit="true"></GridColumn>
                        <GridColumn Field="@nameof(MeetingRoomRequestDto.MeetingRoomStatus)" HeaderText="Status" AutoFit="true"></GridColumn>
                        <GridColumn Field="@nameof(MeetingRoomRequestDto.Reason)" HeaderText="Approver Comments" AutoFit="true"></GridColumn>
                        <GridColumn HeaderText="Actions" AutoFit="true">
                            <Template Context="cc">
                                @{
                                    if (cc is MeetingRoomRequestDto mm)
                                    {
                                        if (mm.MeetingRoomStatus == "Pending")
                                        {
                                            if (mm.CreatedByUserId == _userId)
                                            {
                                                <MudButton Color="Color.Primary" Size="Size.Small" OnClick="@(() => OpenEditForm(mm))" Variant="Variant.Filled">Edit</MudButton>
                                                <MudButton OnClick="@(() => CancelRequest(mm.Id))" Color="Color.Error" Size="Size.Small" Variant="Variant.Filled">Cancel</MudButton>
                                            }
                                        }
                                        else if (mm.MeetingRoomStatus == "Approved")
                                        {
                                            <MudButton OnClick="@(() => CancelRequest(mm.Id))" Color="Color.Error" Size="Size.Small" Variant="Variant.Filled">Cancel</MudButton>
                                        }
                                    }
                                }
                            </Template>
                        </GridColumn>
                    </GridColumns>
                </SfGrid>
            </ContentTemplate>
        </Syncfusion.Blazor.Navigations.TabItem>

    </TabItems>
</SfTab>


@code {
    private SfDialog? _dialog;
    private MeetingRoomRequestDto _request = new();
    public bool IsFormVisible { get; set; } = false;
    public List<OfficeLocationDto> AllLocations { get; set; } = new();
    public List<MeetingRoomDto> AllMeetingRooms { get; set; } = new();
    public int? FilterLocationId { get; set; }
    public List<MeetingRoomDto> AllFilterMeetingRooms { get; set; } = new();
    public int? FilterMeetingRoomId { get; set; }
    public bool IsEvent = false;

    List<MeetingRoomRequestDto> _myBrRequests = new();
    //    List<MeetingRoomRequestDto>
    //{
    //    new MeetingRoomRequestDto { Id = 1, Subject = "Paris", StartTime = new DateTime(2024, 9, 13, 10, 0, 0) , EndTime = new DateTime(2024, 9, 13, 12, 0, 0) },
    //    new MeetingRoomRequestDto { Id = 2, Subject = "Germany", StartTime = new DateTime(2024, 9, 14, 10, 0, 0) , EndTime = new DateTime(2024, 9, 15, 12, 0, 0) }
    //};

    // on initialize
    private string _userId = "";
    private string _userLocation = "";

    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();

        var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        var user = authState.User;
        _userId = "";
        if (user.Identity is { Name: not null })
        {
            _userId = user.Identity.Name;
        }


        AllLocations = await Service.GetLocations();

        var dd = CService.GetEmployeeData(_userId);
        if (dd.Rows.Count > 0)
        {
            _userLocation = (dd.Rows[0]["location_text"].ToString() ?? "").ToLower().Trim(); 
            AllLocations = AllLocations.Where(m => (m.Location??"").ToLower().Trim() == _userLocation).ToList();
        }
        else
        {
            AllLocations = new List<OfficeLocationDto>();
        }

        AllMeetingRooms = new List<MeetingRoomDto>();
        AllFilterMeetingRooms = new List<MeetingRoomDto>();
    }

    

    private async Task SaveRequest()
    {
        // Save the request logic here
        var res = await Service.SaveRequest(_request, _userId);
        if (res == "OK")
        {
            await _dialog!.HideAsync();
            _myBrRequests = await Service.GetUserBookingRequests(_userId, _request.MeetingRoomId ?? 0);
        }
    }

    private async Task OnLocationChange(ChangeEventArgs<int?, OfficeLocationDto> obj)
    {
        //throw new NotImplementedException();
        _request.LocationId = obj.Value;
        _request.MeetingRoomId = null;
        //AllMeetingRooms = obj.Value == null ? [] : await service.GetAllMeetingRooms(obj.Value ?? 0);
        if (obj.Value == null)
        {
            AllMeetingRooms = new List<MeetingRoomDto>();
        }
        else
        {
            AllMeetingRooms = await Service.GetAllMeetingRooms(_request.LocationId ?? 0);
        }
    }

    private async Task OnFilterLocationChange(ChangeEventArgs<int?, OfficeLocationDto> obj)
    {
        AllFilterMeetingRooms = obj.Value == null ? [] : await Service.GetAllMeetingRooms(obj.Value ?? 0);
        _myBrRequests = new List<MeetingRoomRequestDto>();
    }

    private async Task OnFilterMeetingRoomChange(ChangeEventArgs<int?, MeetingRoomDto> obj)
    {
        if (obj.Value != null)
        {
            _myBrRequests = await Service.GetUserBookingRequests(_userId, obj.Value ?? 0);
        }
        else
        {
            _myBrRequests = new List<MeetingRoomRequestDto>();
        }
    }

    private async Task OpenCreateForm()
    {
        var y = DateTime.Today.Year;
        var m = DateTime.Now.Month;
        var d = DateTime.Now.Day;
        var h = DateTime.Now.Hour;

        _request = new MeetingRoomRequestDto
        {
            StartTime = new DateTime(y, m, d, h, 0, 0).AddHours(1),
            EndTime = new DateTime(y, m, d, h, 0, 0).AddHours(2)
        };
        await _dialog!.ShowAsync();
    }

    private async Task OpenEditForm(MeetingRoomRequestDto mm)
    {
        _request = await Service.GetRequestById(mm.Id);
        await _dialog!.ShowAsync();
    }

    private async Task CancelRequest(int requestId)
    {
        var conf = await DialogService.ConfirmAsync("Are you sure want to cancel this meeting", "Confirm");
        if (conf)
        {
            await Service.CancelRequest(requestId, _userId);
            _myBrRequests = await Service.GetUserBookingRequests(_userId, FilterMeetingRoomId ?? 0);
        }
    }

    private async Task RefreshList()
    {
        _myBrRequests = await Service.GetUserBookingRequests(_userId, FilterMeetingRoomId ?? 0);
    }

}


<style>
    .myapproved {
        background: #1E90FF !important; /* Dodger Blue */
    }
    .myreject {
        background: #FF4500 !important; /* Orange Red */
    }
    .mypending {
        background: #FFD700 !important; /* Gold */
    }

    .otherapproved {
        background: #8A2BE2 !important; /* Blue Violet */
    }
    .e-schedule .e-vertical-view .e-all-day-appointment-wrapper .e-appointment.progress,
    .e-schedule .e-vertical-view .e-day-wrapper .e-appointment.progress,
    .e-schedule .e-month-view .e-appointment.myapproved {
        background: #1E90FF; /* Dodger Blue */
    }

    .e-schedule .e-vertical-view .e-all-day-appointment-wrapper .e-appointment.delayed,
    .e-schedule .e-vertical-view .e-day-wrapper .e-appointment.delayed,
    .e-schedule .e-month-view .e-appointment.myreject {
        background: #FF4500; /* Orange Red */
    }

    .e-schedule .e-vertical-view .e-all-day-appointment-wrapper .e-appointment.delayed,
    .e-schedule .e-vertical-view .e-day-wrapper .e-appointment.delayed,
    .e-schedule .e-month-view .e-appointment.mypending {
        background: #FFD700; /* Gold */
    }

    .e-schedule .e-vertical-view .e-all-day-appointment-wrapper .e-appointment.delayed,
    .e-schedule .e-vertical-view .e-day-wrapper .e-appointment.delayed,
    .e-schedule .e-month-view .e-appointment.otherapproved {
        background: #8A2BE2; /* Blue Violet */
    }

    .e-schedule .e-vertical-view .e-all-day-appointment-wrapper .e-appointment.delayed,
    .e-schedule .e-vertical-view .e-day-wrapper .e-appointment.delayed,
    .e-schedule .e-month-view .e-appointment.otherapproved {
        background: #FF8C00; /* Dark Orange */
    }

</style>