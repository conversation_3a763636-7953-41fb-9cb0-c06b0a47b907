﻿{
   "CodeGenerationMode": 4,
   "ContextClassName": "ApplicationDbContext",
   "ContextNamespace": null,
   "FilterSchemas": false,
   "IncludeConnectionString": false,
   "ModelNamespace": null,
   "OutputContextPath": null,
   "OutputPath": "Model",
   "PreserveCasingWithRegex": true,
   "ProjectRootNamespace": "PakSuzuki",
   "Schemas": null,
   "SelectedHandlebarsLanguage": 2,
   "SelectedToBeGenerated": 0,
   "T4TemplatePath": null,
   "Tables": [
      {
         "Name": "[dbo].[BIDashboardLink]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[CanteenMenu]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[Categories]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[Departments]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[Designations]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[Divisions]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[EmpLocationTiming]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[EmployeeSelfServiceLinks]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[JobCities]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[JobDepartment]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[JobTypes]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[Locations]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[MeetingRoomApprovars]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[MeetingRoomRequests]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[MeetingRooms]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[MeetingRoomStatuses]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[Menus]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[Modules]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[OfficeLocation]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[PageAccessLogs]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[PostAttachments]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[PostAttachmentTags]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[PostComments]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[PostEmotions]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[PostPollResults]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[PostPolls]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[PostRoles]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[Posts]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[PostSearchTags]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[PostTypes]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[PostVacancies]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[QuickAnnouncement]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[RoleMenus]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[Roles]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[SectionRoles]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[Sections]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[ShiftLocations]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[Stations]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[SubCategories]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[UserQuickLinks]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[UserRoles]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[Users]",
         "ObjectType": 0
      }
   ],
   "UiHint": null,
   "UncountableWords": null,
   "UseAsyncStoredProcedureCalls": true,
   "UseBoolPropertiesWithoutDefaultSql": false,
   "UseDatabaseNames": false,
   "UseDatabaseNamesForRoutines": true,
   "UseDateOnlyTimeOnly": true,
   "UseDbContextSplitting": false,
   "UseDecimalDataAnnotationForSprocResult": true,
   "UseFluentApiOnly": true,
   "UseHandleBars": false,
   "UseHierarchyId": false,
   "UseInflector": true,
   "UseInternalAccessModifiersForSprocsAndFunctions": false,
   "UseLegacyPluralizer": false,
   "UseManyToManyEntity": true,
   "UseNoDefaultConstructor": false,
   "UseNoNavigations": false,
   "UseNoObjectFilter": false,
   "UseNodaTime": false,
   "UseNullableReferences": false,
   "UsePrefixNavigationNaming": false,
   "UseSchemaFolders": false,
   "UseSchemaNamespaces": false,
   "UseSpatial": false,
   "UseT4": false,
   "UseT4Split": false
}