@page "/"
@inject PageLogService pageLogService

@using Section = PakSuzuki.DTO.Section
@inject AuthenticationStateProvider AuthenticationStateProvider
@inject IJSRuntime JSRuntime

@inject NavigationManager NavMgr

@inject AppDataService service
<style>
    .c-container {
        padding: unset !important;
    }

    .m-link {
        color: #013582;
        text-decoration: none;
        cursor: pointer;
    }

        .m-link:hover {
            text-decoration: underline;
        }

    .truncate-text {
        max-height: 60px;
        overflow: hidden;
        text-overflow: ellipsis;
        /* only show max 3 lines */
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
    }

        .truncate-text ul {
            list-style: none;
            padding-left: 0;
        }

            .truncate-text ul li {
                position: relative;
                padding-left: 1em;
                margin-bottom: 0;
                font-size: 12px;
            }

                .truncate-text ul li::before {
                    content: '';
                    position: absolute;
                    left: 0;
                    top: 0.5em;
                    width: 0.5em;
                    height: 0.5em;
                    background-color: #3498db;
                    border-radius: 50%;
                }


    .msg ul {
        list-style: none;
        padding-left: 0;
    }

    .msg ul, .msg ol {
        margin-left: 1rem;
        padding-left: 1rem;
    }

        .msg ul li {
            position: relative;
            padding-left: 1em;
            margin-bottom: 0;
            font-size: 12px;
        }

            .msg ul li::before {
                content: '';
                position: absolute;
                left: 0;
                top: 0.5em;
                width: 0.5em;
                height: 0.5em;
                background-color: #3498db;
                border-radius: 50%;
            }
</style>
<PageTitle>Home</PageTitle>
@* if userSection contains wellness then display ticker div *@

@if (userSection.Any(x => x.Name == "wellness"))
{
    <div class="ticker">
        <div class="head">Food for Thought:</div>
        @* if @wellnessText exceeded withing visible area then display more link *@
        <div class="text">
            @if (wellnessText!.Length > 90)
            {
                @wellnessText.Substring(0, 90)<span>...</span>
                <span @onclick="() => ShowWellnessMsg(wellnessText)" class="m-link">more</span>
            }
            else
            {
                @wellnessText
            }

        </div>
    </div>
}
@if (userSection.Any(x => x.Name.ToLower() == "quick announcement"))
{
    <div class="container">
        <div class="row">
            <div class="col mb-2 mt-3">
                <CompQuickAnnouncement></CompQuickAnnouncement>
            </div>
        </div>
    </div>
}

@if (userSection.Any(x => x.Name.ToLower() == "home banner") && homeBannersList.Any())
    {
        var firstBanner = homeBannersList.First();
        var height = firstBanner.PostHeight??"";
        if (height == "")
            height = "200px";
    <div class="container">
        <div class="row">
            <div class="col mb-2 mt-3">
                @if (string.IsNullOrEmpty(firstBanner.PostExternalLink))
{
                    <img src="@homeBannersList.First().postHeadImagePath"
                         alt="@homeBannersList.First().postName"
                         style="width: 100%;height: @height;object-fit: cover;border-radius: 10px;box-shadow: var(--mud-elevation-1);" />
                }
                else
                {
                    <a href="@firstBanner.PostExternalLink" target="_blank">
                        <img src="@homeBannersList.First().postHeadImagePath"
                             alt="@homeBannersList.First().postName"
                             style="width: 100%;height: @height;object-fit: cover;border-radius: 10px;box-shadow: var(--mud-elevation-1);" />
                    </a>
                }
            </div>
        </div>
    </div>
}


<SfDialog @ref="dlgMission" ShowCloseIcon="true" IsModal="true" Width="800px" Visible="IsDlgMissionVisible" Height="90vh">
    <DialogTemplates>
        <Header>Mission Statement</Header>
        <Content>
            <img src="@missionDetailImg" style="width:100%" />
        </Content>
    </DialogTemplates>
</SfDialog>
<SfDialog @ref="dlgContent" ShowCloseIcon="true" IsModal="true" Width="500px" Visible="IsDlgContent">
    <DialogTemplates>
        <Header>
            <div style="display: flex;align-items:center; gap:10px">
                <img src="@_dialogIcon" style="height:40px" alt="" /> @_dialogTitle
            </div>
        </Header>
        <Content>
            <div class="msg">
                @((MarkupString)msg)
            </div>
        </Content>
    </DialogTemplates>
</SfDialog>
<div class="container">

    <div style="display:flex;gap:10px;margin-top:15px;">
        @if (userSection.Any(x => x.Name == "vision"))
        {
            <div class="col-md mb-2">

                <MudCard>
                    <MudContainer>
                        <div class="vision-card" @onclick="() => ShowVisionMessage(_visionText)">
                            <div>
                                <img src="/images/vision.png" alt="" />
                            </div>
                            <div>
                                <span class="sec-title">Vision</span>
                                <div class="truncate-text"> @((MarkupString)_visionText)</div>
                                <span style="font-size:12px; color:blue;">... more</span>

                                @*<span style="color: #7F7F7F">
                                        @if (_visionText!.Length > 100)
                                        {
                                            @((MarkupString)_visionText!.Substring(0, 100)) <span @onclick="() => ShowMissionMessage(_visionText)" class="m-link">more</span>
                                        }
                                        else
                                        {
                                            @((MarkupString)_visionText)
                                        }

                                    </span>*@

                            </div>
                        </div>
                    </MudContainer>
                </MudCard>
            </div>
        }


        @if (userSection.Any(x => x.Name == "mission") && !string.IsNullOrEmpty(missionThumbImg))
        {
            <div class="col-md mb-2">
                <MudCard>
                    <MudContainer>
                        <div class="vision-card" @onclick="() => ShowMissionMessage(_missionText)">
                            <img src="@missionThumbImg" alt="Alternate Text" style="width:100%;object-fit:contain;min-width:23px" />
                        </div>
                    </MudContainer>
                </MudCard>
            </div>
        }


        @if (userSection.Any(x => x.Name.ToLower() == "qualitystatements"))
        {
            <div class="col-md mb-2">
                <MudCard>
                    <MudContainer>
                        <div class="vision-card" @onclick="() => ShowQualityStatementMessage(_qStatementText)">
                            <div>
                                <img src="/images/policy.png" alt="" />
                            </div>
                            <div>
                                <span class="sec-title">Basic Policy</span>
                                <div class="truncate-text">

                                    @((MarkupString)_qStatementText)
                                </div>
                                <span style="font-size:12px; color:blue;">... more</span>

                                @*<span style="color: #7F7F7F">
                                        @if (_missionText!.Length > 100)
                                        {
                                            @((MarkupString)_missionText!.Substring(0, 100)) <span @onclick="() => ShowMissionMessage(_missionText)" class="m-link">more</span>
                                        }
                                        else
                                        {
                                            @((MarkupString)_missionText)
                                        }

                                    </span>*@

                            </div>
                        </div>
                    </MudContainer>
                </MudCard>
            </div>
        }


        @*@if (userSection.Any(x => x.Name == "policy"))
            {
                <div class="col-md mb-2">
                    <MudCard>
                        <MudContainer>
                            <div class="vision-card" @onclick="() => OpenPolicyPage()">
                                <div>
                                    <img src="/images/policy.png" alt="" />
                                </div>
                                <div>
                                    <h3 x class="sec-title">Quality Policy</h3>

                                </div>
                            </div>
                        </MudContainer>
                    </MudCard>
                </div>
            }*@

    </div>

    <CompCatalog Title="Announcements" CatalogType="@CompCatalogType.Card" Items="AnnouncementsList" DetailPageUrl="/view/announcements" />
    @*NewsAndEvents*@
    @if (userSection.Any(x => x.Name == "newsandevents"))
    {
        <CompNews Title="News &amp; Events" Items="NewsAndEventsList" DetailPageUrl="/view/newsanevents" />
    }

    @if (userSection.Any(x => x.Name.ToLower() == "internal vacancies"))
    {
        <CompInternalVacancies Title="Internal Vacanices" Items="InternalVacanciesList"></CompInternalVacancies>
    }
    @if (userSection.Any(x => x.Name == "bi dashboard"))
    {
        <CompChart />
    }
    @if (userSection.Any(x => x.Name == "DigitalLearning".ToLower()))
    {
        <CompNews Title="Digital Learning" Items="DigitalLearningList" DetailPageUrl="/view/digitalearning" />
    }

    <div class="row mb-2">
        @if (userSection.Any(x => x.Name == "calender"))
        {
            <div class="col-md-5">
                <CompCalender />
            </div>
        }

        @if (userSection.Any(x => x.Name == "employee directory"))
        {
            <div class="col-md-7">
                <h3 class="sec-title">&nbsp;</h3>
                <CompEmployeeDirectory />
            </div>
        }

    </div>

    @if (userSection.Any(x => x.Name.ToLower() == "employeeepotlight")
      || userSection.Any(x => x.Name.ToLower() == "employeespotlight"))
    {
        <CompCatalog Title="Employee Spotlight" CatalogType="@CompCatalogType.Image" Items="SpotlightList" />
    }
    @if (userSection.Any(x => x.Name == "collaboration"))
    {
        <CompCatalog Title="Employee Ideas and Collaboration" Items="IdeasList" CatalogType="@CompCatalogType.Idea" />
    }

    @if (userSection.Any(x => x.Name == "poll"))
    {
        <CompPolls />
    }

    @if (userSection.Any(x => x.Name == "newsletter"))
    {
        <CompNewsletter NewsLettersList="@NewsLettersList" DetailPageLink="/view/Newsletters" />
    }
    @if (userSection.Any(x => x.Name == "canteenmenu"))
    {
        <CompCanteenMenu />
    }

</div>


@code {
    private SfDialog dlgContent;
    private SfDialog dlgMission;
    private readonly bool IsDlgContent = false;
    private readonly bool IsDlgMissionVisible = false;
    private string msg = "";
    private List<PostDTO> AnnouncementsList { get; set; } = new();
    private List<PostDTO> homeBannersList { get; set; } = new();
    private List<PostDTO> SpotlightList { get; set; } = new();
    private List<PostDTO> NewsAndEventsList { get; set; } = new();
    private List<PostDTO> DigitalLearningList { get; set; } = new();
    private List<PostDTO> IdeasList { get; set; } = new();
    private List<PostDTO> NewsLettersList { get; set; } = new();
    private List<PostDTO> InternalVacanciesList { get; set; } = new();
    private List<SectionDto> userSection { get; set; } = new();
    private string? wellnessText = "";
    private string? wellnessIcon = "";
    private string? wellnessDeteil = "";
    private string? _dialogTitle = "";
    private string? _visionText;
    private string? _visionIcon;
    private string? _visionDetail;
    private string? _missionText;
    private string? _missionIcon;
    private string? _missionDetail;

    private string? _policyText;
    private string? _policyIcon;
    private string? _policyDetail;
    private string _qStatementText;
    private string _qStatementIcon;
    private string _qStatementDetail;
    private string missionThumbImg = "";
    private string missionDetailImg = "";

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            await JSRuntime.InvokeVoidAsync("setScrollPosition", 0, 0);
        }
    }

    protected override async Task OnInitializedAsync()
    {
        // create Page Log Entity
        


        await base.OnInitializedAsync();

        var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        var user = authState.User;

        if (user.Identity is { Name: not null })
        {
            AnnouncementsList = await service.GetUserPosts(user.Identity.Name, Section.Announcements);
            SpotlightList = await service.GetUserPosts(user.Identity.Name, Section.EmployeeSpotLight);
            NewsAndEventsList = await service.GetUserPosts(user.Identity.Name, Section.NewsAneEvents);
            DigitalLearningList = await service.GetUserPosts(user.Identity.Name, Section.DigitalLearning);
            IdeasList = await service.GetUserPosts(user.Identity.Name, Section.Announcements);
            NewsLettersList = await service.GetUserPosts(user.Identity.Name, Section.Newsletter);
            InternalVacanciesList = await service.GetUserPosts(user.Identity.Name, Section.InternalVacancies);
            homeBannersList = await service.GetUserPosts(user.Identity.Name, 33);
            (wellnessText, wellnessIcon, wellnessDeteil) = await service.GetSectionText(Section.Wellness, user.Identity.Name);
            (_missionText, _missionIcon, _missionDetail) = await service.GetSectionText(Section.mission, user.Identity.Name);
            (_visionText, _visionIcon, _visionDetail) = await service.GetSectionText(Section.vision, user.Identity.Name);
            (_policyText, _policyIcon, _policyDetail) = await service.GetSectionText(Section.policy, user.Identity.Name);
            (_qStatementText, _qStatementIcon, _qStatementDetail) = await service.GetSectionText(Section.QualityStatements, user.Identity.Name);
            (missionDetailImg, missionThumbImg) = await service.GetMissionImages(user.Identity.Name);
            userSection = await service.GetUserSections(user.Identity.Name);
        }
    }

    private string _dialogIcon = "";

    private async Task ShowVisionMessage(string? message)
    {
        if (!string.IsNullOrEmpty(message))
        {
            _dialogIcon = "/images/vision.png";
            _dialogTitle = "Vision";
            msg = message;
            await dlgContent.ShowAsync();
        }
    }

    private async Task ShowWellnessMsg(string? message)
    {
        if (!string.IsNullOrEmpty(message))
        {
            _dialogTitle = "Wellness";
            msg = message;
            await dlgContent.ShowAsync();
        }
    }

    private async Task ShowMissionMessage(string? message)
    {
        await dlgMission.ShowAsync();
    }

    private async Task ShowQualityStatementMessage(string? message)
    {
        if (!string.IsNullOrEmpty(message))
        {
            _dialogIcon = "/images/policy.png";
            _dialogTitle = "Suzuki Basic Policy";
            msg = message;
            await dlgContent.ShowAsync();
        }
    }

    private void OpenPolicyPage()
    {
        NavMgr.NavigateTo("/view/policies");
    }

}