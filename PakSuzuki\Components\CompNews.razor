﻿@inject AppDataService service

<link href="/styles/news-card.css" rel="stylesheet"/>


<SfDialog @ref="detailForm" ShowCloseIcon="true" IsModal="true" Width="900px" CloseOnEscape="true"
          Visible="IsDetailVisible">
    <DialogTemplates>
        <Header>@Title</Header>
        <Content>
            <div class="popup">

                <div class="row">
                    <div class="col">
                        <img src="@_postDetail!.postHeadImagePath" width="100%" height="340px" style="object-fit: cover;object-position:top" alt=""/>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md">
                        <h2 class="card-title" style="font-size: 18px;">@_postDetail.postName</h2>
                        <p>@_postDetail.postPublishDateStr</p>
                        @((MarkupString)_postDetail.postContentHTML)
                    </div>
                </div>
                <div class="row">
                    <div class="col-md">
                        <h4 style="font-size:17px">Tags</h4>
                        <p></p>
                        @foreach (var tag in _postDetail.Tags)
                        {
                            <span class="tag">@tag</span>
                        }
                    </div>
                </div>

                @if (_postDetail.Attachments.Any())
                {
                    <div class="row mt-2">
                        <div class="col-md">
                            <h4 style="font-size:17px;margin-bottom:15px">Attachments</h4>
                            <p></p>
                            @foreach (var tag in _postDetail.Attachments)
                            {
                                <a target="_blank" style="margin:5px;" class="tag" href="@tag.Url">@tag.Name</a>
                            }
                        </div>
                    </div>
                }
            </div>
        </Content>
    </DialogTemplates>

</SfDialog>
@if (Items.Any())
{
    @if (!string.IsNullOrEmpty(Title))
    {
        <div class="view-all-container mb-2">
            <h3 class="sec-title">@Title</h3>
            @if (!string.IsNullOrEmpty(DetailPageUrl))
            {
                <a class="view-all" href="@DetailPageUrl">View All</a>
            }
        </div>
    }

    <div class="row">
        <div class="col-md">
            <div class="newscard">
                @foreach (var k in Items)
                {
                    if(string.IsNullOrEmpty(k.PostExternalLink))
                    {
                    
                    <div class="news" @onclick="() => ShowDetail(k.postId)">
                        <div>

                            <img src="@k.postThumbnailPath" alt="" onerror="this.onerror=null;this.src='/images/newsandevents.png'"/>
                        </div>
                        <div class="news-content">
                            <h3>@k.postName</h3>

                            @if ((k.postRemarks ?? "").Length > 250)
                            {
                                <p>
                                    @k.postRemarks!.Substring(0, 250)<span>...</span> <span>more</span>
                                </p>
                            }
                            else
                            {
                                <p>@(k.postRemarks ?? "")</p>
                            }

                            <p>@k.postPublishDateStr</p>
                        </div>
                    </div>
                    }
                    else
                    {
                        <a href="@k.PostExternalLink" target="_blank" class="news" >
                        <div>

                            <img src="@k.postThumbnailPath" alt="" onerror="this.onerror=null;this.src='/images/newsandevents.png'"/>
                        </div>
                        <div class="news-content">
                            <h3>@k.postName</h3>

                            @if ((k.postRemarks ?? "").Length > 250)
                            {
                                <p>
                                    @k.postRemarks!.Substring(0, 250)<span>...</span> <span>more</span>
                                </p>
                            }
                            else
                            {
                                <p>@(k.postRemarks ?? "")</p>
                            }

                            <p>@k.postPublishDateStr</p>
                        </div>
                    </a>
                    }
                    <div class="separator"></div>
                }


            </div>
        </div>
    </div>
}


@code {

    [Parameter] public string? Title { get; set; }

    [Parameter] public string? DetailPageUrl { get; set; }
    [Parameter] public List<PostDTO> Items { get; set; } = new();

    public bool IsDetailVisible { get; set; }
    private PostDTO? _postDetail;

    private SfDialog detailForm;

    private async Task ShowDetail(int postId)
    {
        _postDetail = await service.GetPostDetail(postId);
        if (_postDetail != null)
        {
            await detailForm.ShowAsync();
        }
    }

}