﻿using Section = PakSuzuki.DTO.Section;

namespace PakSuzuki.Services;

public class PostDataService(ApplicationDbContext dc)
{
    public Task<string> checkPublishExpiryDate2(DateTime publishDate, DateTime expiryDate, int postId)
    {
        // if publish date is less then current date then return error message
        if (postId == 0)
            if (publishDate < DateTime.Today)
                return Task.FromResult("Publish date must be greater than or equal to Current date");

        // if expiry date is less then current date then return error message
        if (expiryDate < DateTime.Today) return Task.FromResult("Expiry date must be greater than Current date");

        if (publishDate > expiryDate)
            return Task.FromResult("Expiry date must be greater than or equal to publish date");
        return Task.FromResult("");
    }

    public Task<string> checkPublishExpiryDate(DateTime publishDate, DateTime expiryDate, int postId)
    {
        var pDate = Convert.ToDateTime(publishDate.ToShortDateString() + " 00:00:00");
        var eDate = Convert.ToDateTime(expiryDate.ToShortDateString() + " 00:00:00");
        var currentDate = Convert.ToDateTime(DateTime.Now.ToShortDateString() + " 00:00:00");

        var msg = "";
        if (eDate < pDate) msg = "Expiry date must be greater than or equal to Publish date";
        if (pDate > eDate) msg = "Publish date must be less than or equal to Expiry date";
        if (postId == 0)
            if (pDate < currentDate)
                msg = "Publish date must be greater than or equal to Current date";

        return Task.FromResult(msg);
    }

    public async Task<List<Category>> GetCategories(int sectionId)
    {
        var q = (from c in dc.Categories
            where c.SectionId == sectionId
            select c).ToList();
        return await Task.FromResult(q);
    }

    public Task<List<JobDepartmentDto>> GetJobsDepartments()
    {
        var q = (from a in dc.JobDepartments
            orderby a.Name
            select new JobDepartmentDto
            {
                Id = a.Id,
                Name = a.Name
            }).ToList();
        return Task.FromResult(q);
    }


    public Task<List<JobCityDto>> GetJobCities()
    {
        var q = (from a in dc.JobCities
            orderby a.Name
            select new JobCityDto
            {
                Id = a.Id,
                Name = a.Name
            }).ToList();
        return Task.FromResult(q);
    }

    public Task<List<JobTypeDto>> GetJobTypes()
    {
        var q = (from a in dc.JobTypes
            orderby a.Name
            select new JobTypeDto
            {
                Id = a.Id,
                Name = a.Name
            }).ToList();
        return Task.FromResult(q);
    }

    // GetAllSectionsAsync(); get all thise section which are active and whose module is "Digital Transformation"
    public Task<List<SectionDto>> GetAllSectionsAsync(int moduleId)
    {
        var q = (from a in dc.Sections
            where a.ModuleId != null && a.IsActive == true
            orderby a.Module.ModuleTitle, a.SortOrder
            select new SectionDto
            {
                Id = a.SectionId,
                Name = $"{a.SectionTitle} - ({a.Module.ModuleTitle} - {a.PostType.PostTypeTitle})"
            }).ToList();
        return Task.FromResult(q);
    }

    // GetSectionPostType return int post type id
    public Task<int?> GetSectionPostType(int sectionId)
    {
        var q = (from a in dc.Sections
            where a.SectionId == sectionId
            select a.PostTypeId).FirstOrDefault();
        return Task.FromResult(q);
    }

    public async Task<List<PostDTO>> getAllPosts(int sectionId)
    {
        var q = (from p in dc.Posts
            //join uCreated in dc.Users on p.PostCreatedBy equals uCreated.Id
            where p.PostSectionId == sectionId && p.PostIsActive == "ACTIVE" && p.PostIsDel == "NO"
            orderby p.PostModifiedDate ?? p.PostCreatedDate descending
            select new PostDTO
            {
                postId = p.PostId,
                postName = p.PostName,
                postRemarks = p.PostRemarks,
                postPublishDate = p.PostPublishDate,
                postExpiryDate = p.PostExpiryDate,
                CreatedBy = dc.Users.SingleOrDefault(x => x.Id == p.PostCreatedBy)!.Name ?? "",
                CreatedDate = p.PostCreatedDate,
                ModifiedBy = dc.Users.SingleOrDefault(x => x.Id == p.PostModifiedBy)!.Name ?? "",
                ModifiedDate = p.PostModifiedDate ?? DateTime.Now,
                postSubCategoryId = p.PostSubCategoryId,
                postCategoryId = p.PostCategoryId ?? 0,
                PostExternalLink = p.PostExternalLink??"",
                PostWidth = p.PostWidth ?? "",
                PostHeight = p.PostHeight ?? "",
                //postStatus = getPostStatus(p.PostPublishDate ?? DateTime.Now, p.PostExpiryDate ?? DateTime.Now)
            }).ToList();
        foreach (var i in q)
        {
            var mm = (from a in dc.Categories where a.CategoryId == i.postCategoryId select a).FirstOrDefault();
            if (mm != null)
                i.PostCategory = mm.CategoryTitle;
            var nn = (from a in dc.SubCategories where a.Id == i.postSubCategoryId select a).FirstOrDefault();
            if (nn != null)
                i.PostSubCategory = nn.Title;
        }

        return await Task.FromResult(q);
    }

    public static string getPostStatus(DateTime publishDate, DateTime expiryDate)
    {
        string str;
        if (DateTime.Now >= publishDate && DateTime.Now <= expiryDate)
            str = "Published";
        else if (expiryDate < DateTime.Now)
            str = "Expired";
        else
            str = "Saved";
        return str;
    }

    public async Task<List<PostSearchTagDTO>> getPostSrhTagsWithPostId(int postId)
    {
        var q = (from pSrh in dc.PostSearchTags
            where pSrh.PostId == postId
            select new PostSearchTagDTO
            {
                postId = postId,
                postSearchTagName = pSrh.PostSearchTagName,
                postSearchTagID = pSrh.PostSearchTagId
            }).ToList();
        return await Task.FromResult(q);
    }

    public async Task<List<PostAttachmentDTO>> getPostAttachmentWithPostId(int postId)
    {
        var q = (from pA in dc.PostAttachments
            where pA.PostId == postId
            select new PostAttachmentDTO
            {
                postAttachmentName = pA.PostAttachmentName,
                postAttachmentDocType = pA.PostAttachmentDocType,
                postAttachmentDocExt = pA.PostAttachmentDocExt,
                postAttachmentDocSize = pA.PostAttachmentDocSize,
                postAttachmentDocURL = pA.PostAttachmentUrl
            }).ToList();
        return await Task.FromResult(q);
    }


    public async Task<List<PostPollDTO>> getpostPollAnswerWithPostId(int postId)
    {
        var q = (from pp in dc.PostPolls
            where pp.PostId == postId
                  && pp.PostPollIsActive == "ACTIVE"
            select new PostPollDTO
            {
                PostPollId = pp.PostPollId,
                PostPollName = pp.PostPollName
            }).ToList();

        return await Task.FromResult(q);
    }

    public async Task<PostDTO> getPostWithId(int postId)
    {
        var q = (from p in dc.Posts
            where p.PostId == postId
            select new PostDTO
            {
                postId = p.PostId,
                postName = p.PostName,
                postContentHTML = p.PostContentHtml,
                postRemarks = p.PostRemarks,
                postCategoryId = p.PostCategoryId ?? 0,
                postPublishDate = p.PostPublishDate,
                postExpiryDate = p.PostExpiryDate,
                postContentText = p.PostContentText,
                postHeadImagePath = p.PostHeadImagePath,
                postThumbnailPath = p.PostThumbnailImagePath,
                postIconImagePath = p.PostIconImagePath,
                postSubCategoryId = p.PostSubCategoryId,
                JobDepartmentId = p.JobDepartmentId,
                JobTypeId = p.JobTypeId,
                JobCityId = p.JobCityId,
                JobLastDateToApply = p.JobLastDateToApply,
                JobDivisionName = p.JobDivisionName,
                JobDepartmentName = p.JobDepartmentName,
                PostExternalLink = p.PostExternalLink,
                PostWidth = p.PostWidth ?? "",
                PostHeight = p.PostHeight ?? ""
            }).SingleOrDefault();
        return await Task.FromResult(q);
    }

    public async void deletePost(int postId)
    {
        if (dc.Posts.Any(x => x.PostId == postId))
        {
            var q = dc.Posts.SingleOrDefault(x => x.PostId.Equals(postId));
            q.PostIsDel = "YES";
            q.PostIsActive = "IN-ACTIVE";
            await dc.SaveChangesAsync();
        }
    }

    public async Task<bool> savePostWellness(PostDTO postDTO, int postId, string userId)
    {
        var uId = dc.Users.SingleOrDefault(x => x.UserId == userId)!.Id;
        if (dc.Posts.Any(x => x.PostId == postDTO.postId) == false)
        {
            var p = new Post
            {
                PostName = postDTO.postName,
                PostRemarks = postDTO.postContentText,
                PostContentText = postDTO.postContentText,
                PostContentHtml = "",
                PostThumbnailImagePath = "",
                PostHeadImagePath = "",
                PostIconImagePath = "",
                PostSectionId = (int)Section.Wellness,
                PostCategoryId = 1,
                PostCreatedBy = uId,
                PostCreatedDate = DateTime.Now,
                PostPublishDate = postDTO.postPublishDate,
                PostExpiryDate = postDTO.postExpiryDate,
                PostIsActive = "ACTIVE",
                PostIsDel = "NO"
            };
            dc.Posts.Add(p);
            await dc.SaveChangesAsync();
            postId = p.PostId;
        }
        else /// UPDATE Record Code
        {
            var p = dc.Posts.First(x => x.PostId == postDTO.postId);
            p.PostName = postDTO.postName;
            p.PostRemarks = postDTO.postContentText;
            p.PostContentText = postDTO.postContentText;
            p.PostPublishDate = postDTO.postPublishDate;
            p.PostExpiryDate = postDTO.postExpiryDate;
            p.PostModifiedBy = uId;
            p.PostModifiedDate = DateTime.Now;
            await dc.SaveChangesAsync();
        }

        return await Task.FromResult(true);
    }

    public async Task<bool> savePostVision(PostDTO postDTO, int postId, string userId)
    {
        var uId = dc.Users.SingleOrDefault(x => x.UserId == userId)!.Id;
        if (dc.Posts.Any(x => x.PostId == postDTO.postId) == false)
        {
            var p = new Post
            {
                PostName = postDTO.postName,
                PostRemarks = postDTO.postContentText,
                PostContentText = postDTO.postContentText,
                PostContentHtml = "",
                PostThumbnailImagePath = postDTO.postThumbnailPath,
                PostHeadImagePath = postDTO.postThumbnailPath,
                PostIconImagePath = postDTO.postThumbnailPath,
                PostSectionId = (int)Section.vision,
                PostCategoryId = 1,
                PostCreatedBy = uId,
                PostCreatedDate = DateTime.Now,
                PostPublishDate = postDTO.postPublishDate,
                PostExpiryDate = postDTO.postExpiryDate,
                PostIsActive = "ACTIVE",
                PostIsDel = "NO"
            };
            dc.Posts.Add(p);
            await dc.SaveChangesAsync();
            postId = p.PostId;
        }
        else /// UPDATE Record Code
        {
            var p = dc.Posts.First(x => x.PostId == postDTO.postId);
            p.PostName = postDTO.postName;
            p.PostRemarks = postDTO.postContentText;
            p.PostThumbnailImagePath = postDTO.postThumbnailPath;
            p.PostHeadImagePath = postDTO.postThumbnailPath;
            p.PostIconImagePath = postDTO.postThumbnailPath;
            p.PostContentText = postDTO.postContentText;
            p.PostPublishDate = postDTO.postPublishDate;
            p.PostExpiryDate = postDTO.postExpiryDate;
            p.PostModifiedBy = uId;
            p.PostModifiedDate = DateTime.Now;
            await dc.SaveChangesAsync();
        }

        return await Task.FromResult(true);
    }

    public async Task<bool> savePostQualityStatement(PostDTO post, int postId, string userId)
    {
        var uId = dc.Users.SingleOrDefault(x => x.UserId == userId)!.Id;
        if (dc.Posts.Any(x => x.PostId == post.postId) == false)
        {
            var p = new Post
            {
                PostName = post.postName,
                PostRemarks = post.postContentText,
                PostContentText = post.postContentText,
                PostContentHtml = "",
                PostThumbnailImagePath = post.postThumbnailPath,
                PostHeadImagePath = post.postThumbnailPath,
                PostIconImagePath = post.postThumbnailPath,
                PostSectionId = (int)Section.QualityStatements,
                PostCategoryId = 1,
                PostCreatedBy = uId,
                PostCreatedDate = DateTime.Now,
                PostPublishDate = post.postPublishDate,
                PostExpiryDate = post.postExpiryDate,
                PostIsActive = "ACTIVE",
                PostIsDel = "NO"
            };
            dc.Posts.Add(p);
            await dc.SaveChangesAsync();
            postId = p.PostId;
        }
        else /// UPDATE Record Code
        {
            var p = dc.Posts.First(x => x.PostId == post.postId);
            p.PostName = post.postName;
            p.PostRemarks = post.postContentText;
            p.PostThumbnailImagePath = post.postThumbnailPath;
            p.PostHeadImagePath = post.postThumbnailPath;
            p.PostIconImagePath = post.postThumbnailPath;
            p.PostContentText = post.postContentText;
            p.PostPublishDate = post.postPublishDate;
            p.PostExpiryDate = post.postExpiryDate;
            p.PostModifiedBy = uId;
            p.PostModifiedDate = DateTime.Now;
            await dc.SaveChangesAsync();
        }

        return await Task.FromResult(true);
    }

    public async Task<bool> savePostMission(PostDTO postDTO, int postId, string userId)
    {
        var uId = dc.Users.SingleOrDefault(x => x.UserId == userId)!.Id;
        if (dc.Posts.Any(x => x.PostId == postDTO.postId) == false)
        {
            var p = new Post
            {
                PostName = postDTO.postName,
                PostRemarks = postDTO.postContentText,
                PostContentText = postDTO.postContentText,
                PostContentHtml = "",
                PostThumbnailImagePath = postDTO.postThumbnailPath,
                PostHeadImagePath = postDTO.postThumbnailPath,
                PostIconImagePath = postDTO.postThumbnailPath,
                PostSectionId = (int)Section.mission,
                PostCategoryId = 1,
                PostCreatedBy = uId,
                PostCreatedDate = DateTime.Now,
                PostPublishDate = postDTO.postPublishDate,
                PostExpiryDate = postDTO.postExpiryDate,
                PostIsActive = "ACTIVE",
                PostIsDel = "NO"
            };
            dc.Posts.Add(p);
            await dc.SaveChangesAsync();
            postId = p.PostId;
        }
        else /// UPDATE Record Code
        {
            var p = dc.Posts.First(x => x.PostId == postDTO.postId);
            p.PostName = postDTO.postName;
            p.PostRemarks = postDTO.postContentText;
            p.PostThumbnailImagePath = postDTO.postThumbnailPath;
            p.PostHeadImagePath = postDTO.postThumbnailPath;
            p.PostIconImagePath = postDTO.postThumbnailPath;
            p.PostContentText = postDTO.postContentText;
            p.PostPublishDate = postDTO.postPublishDate;
            p.PostExpiryDate = postDTO.postExpiryDate;
            p.PostModifiedBy = uId;
            p.PostModifiedDate = DateTime.Now;
            await dc.SaveChangesAsync();
        }

        return await Task.FromResult(true);
    }

    public async Task<bool> savePostpolicy(PostDTO postDTO, int postId, string userId)
    {
        var uId = dc.Users.SingleOrDefault(x => x.UserId == userId)!.Id;
        if (dc.Posts.Any(x => x.PostId == postDTO.postId) == false)
        {
            var p = new Post
            {
                PostName = postDTO.postName,
                PostRemarks = postDTO.postContentText,
                PostContentText = postDTO.postContentText,
                PostContentHtml = "",
                PostThumbnailImagePath = postDTO.postThumbnailPath,
                PostHeadImagePath = postDTO.postThumbnailPath,
                PostIconImagePath = postDTO.postThumbnailPath,
                PostSectionId = (int)Section.policy,
                PostCategoryId = 1,
                PostCreatedBy = uId,
                PostCreatedDate = DateTime.Now,
                PostPublishDate = postDTO.postPublishDate,
                PostExpiryDate = postDTO.postExpiryDate,
                PostIsActive = "ACTIVE",
                PostIsDel = "NO"
            };
            dc.Posts.Add(p);
            await dc.SaveChangesAsync();
            postId = p.PostId;
        }
        else /// UPDATE Record Code
        {
            var p = dc.Posts.First(x => x.PostId == postDTO.postId);
            p.PostName = postDTO.postName;
            p.PostRemarks = postDTO.postContentText;
            p.PostThumbnailImagePath = postDTO.postThumbnailPath;
            p.PostHeadImagePath = postDTO.postThumbnailPath;
            p.PostIconImagePath = postDTO.postThumbnailPath;
            p.PostContentText = postDTO.postContentText;
            p.PostPublishDate = postDTO.postPublishDate;
            p.PostExpiryDate = postDTO.postExpiryDate;
            p.PostModifiedBy = uId;
            p.PostModifiedDate = DateTime.Now;
            await dc.SaveChangesAsync();
        }

        return await Task.FromResult(true);
    }

    public async Task<bool> saveSpotlight(PostDTO postDTO, int postId, string userId)
    {
        var uId = dc.Users.SingleOrDefault(x => x.UserId == userId)!.Id;
        if (dc.Posts.Any(x => x.PostId == postDTO.postId) == false)
        {
            var p = new Post
            {
                PostName = postDTO.postName,
                PostRemarks = postDTO.postContentText,
                PostContentText = postDTO.postContentText,
                PostContentHtml = "",
                PostThumbnailImagePath = postDTO.postThumbnailPath,
                PostHeadImagePath = postDTO.postThumbnailPath,
                PostIconImagePath = postDTO.postThumbnailPath,
                PostSectionId = (int)Section.EmployeeSpotLight,
                PostCategoryId = 1,
                PostCreatedBy = uId,
                PostCreatedDate = DateTime.Now,
                PostPublishDate = postDTO.postPublishDate,
                PostExpiryDate = postDTO.postExpiryDate,
                PostIsActive = "ACTIVE",
                PostIsDel = "NO"
            };
            dc.Posts.Add(p);
            await dc.SaveChangesAsync();
            postId = p.PostId;
        }
        else /// UPDATE Record Code
        {
            var p = dc.Posts.First(x => x.PostId == postDTO.postId);
            p.PostName = postDTO.postName;
            p.PostRemarks = postDTO.postContentText;
            p.PostThumbnailImagePath = postDTO.postThumbnailPath;
            p.PostHeadImagePath = postDTO.postThumbnailPath;
            p.PostIconImagePath = postDTO.postThumbnailPath;
            p.PostContentText = postDTO.postContentText;
            p.PostPublishDate = postDTO.postPublishDate;
            p.PostExpiryDate = postDTO.postExpiryDate;
            p.PostModifiedBy = uId;
            p.PostModifiedDate = DateTime.Now;
            await dc.SaveChangesAsync();
        }

        return await Task.FromResult(true);
    }

    public async Task<bool> savePost(PostDTO postObj, List<PostSearchTagDTO> postSrhDTO,
        List<PostAttachmentDTO> postAttachmentDTO, int postId, string postContentText, string userId, int sectionId)
    {
        var uId = dc.Users.SingleOrDefault(x => x.UserId.ToLower() == userId.ToLower())!.Id;
        if (dc.Posts.Any(x => x.PostId == postObj.postId) == false)
        {
            var p = new Post
            {
                PostName = postObj.postName,
                PostRemarks = postObj.postRemarks,
                PostContentText = postContentText,
                PostContentHtml = postObj.postContentText,
                PostThumbnailImagePath = postObj.postThumbnailPath,
                PostHeadImagePath = postObj.postHeadImagePath,
                PostIconImagePath = postObj.postIconImagePath,
                PostSectionId = sectionId,
                PostCategoryId = postObj.postCategoryId,
                PostCreatedBy = uId,
                PostCreatedDate = DateTime.Now,
                PostPublishDate = postObj.postPublishDate,
                PostExpiryDate = postObj.postExpiryDate,
                JobLastDateToApply = postObj.JobLastDateToApply,
                JobCityId = postObj.JobCityId,
                JobDepartmentId = postObj.JobDepartmentId,
                JobTypeId = postObj.JobTypeId,
                PostIsActive = "ACTIVE",
                PostIsDel = "NO",
                PostSubCategoryId = postObj.postSubCategoryId,
                JobDivisionName = postObj.JobDivisionName ?? "",
                JobDepartmentName = postObj.JobDepartmentName ?? "",
                PostExternalLink = (postObj.PostExternalLink ?? "").Trim(),
                PostWidth = postObj.PostWidth,
                PostHeight = postObj.PostHeight
            };
            dc.Posts.Add(p);
            await dc.SaveChangesAsync();
            postId = p.PostId;

            foreach (var item in postSrhDTO)
            {
                PostSearchTag pSrhTag = new()
                {
                    PostId = postId,
                    PostSearchTagName = item.postSearchTagName,
                    PostSearchTagCreatedBy = uId,
                    PostSearchTagCreatedDate = DateTime.Now
                };
                dc.PostSearchTags.Add(pSrhTag);
                await dc.SaveChangesAsync();
            }

            foreach (var item in postAttachmentDTO)
            {
                PostAttachment pAttachfile = new()
                {
                    PostId = postId,
                    PostAttachmentName = item.postAttachmentName,
                    PostAttachmentDocType = item.postAttachmentDocType,
                    PostAttachmentDocExt = item.postAttachmentDocExt,
                    PostAttachmentDocSize = item.postAttachmentDocSize,
                    PostAttachmentUrl = item.postAttachmentDocURL,
                    PostAttachmentCreatedBy = uId,
                    PostAttachmentCreatedDate = DateTime.Now
                };
                dc.PostAttachments.Add(pAttachfile);
                await dc.SaveChangesAsync();
                pAttachfile.PostAttachmentCode = $"{pAttachfile.PostAttachmentId:000}";
                await dc.SaveChangesAsync();
            }
        }
        else /// UPDATE Record Code
        {
            var p = dc.Posts.First(x => x.PostId == postObj.postId);
            p.PostName = postObj.postName;
            p.PostRemarks = postObj.postRemarks;
            p.PostContentText = postContentText;
            p.PostContentHtml = postObj.postContentText;
            p.PostThumbnailImagePath = postObj.postThumbnailPath;
            p.PostHeadImagePath = postObj.postHeadImagePath;
            p.PostIconImagePath = postObj.postIconImagePath;
            p.PostCategoryId = postObj.postCategoryId;
            p.PostPublishDate = postObj.postPublishDate;
            p.PostExpiryDate = postObj.postExpiryDate;
            p.PostModifiedBy = uId;
            p.PostModifiedDate = DateTime.Now;
            p.PostSubCategoryId = postObj.postSubCategoryId;
            p.JobDivisionName = postObj.JobDivisionName ?? "";
            p.JobDepartmentName = postObj.JobDepartmentName ?? "";
            p.PostExternalLink = (postObj.PostExternalLink ?? "").Trim();
            p.PostWidth = postObj.PostWidth;
            p.PostHeight = postObj.PostHeight;
            await dc.SaveChangesAsync();

            var srhDel = (from pSrh in dc.PostSearchTags where pSrh.PostId == postObj.postId select pSrh).ToList();
            dc.PostSearchTags.RemoveRange(srhDel);
            await dc.SaveChangesAsync();
            //foreach (var item in srhDel)
            //{
            //    dc.PostSearchTags.Remove(dc.PostSearchTags.SingleOrDefault(x => x.PostSearchTagId == item.PostSearchTagId)!);
            //}
            foreach (var item in postSrhDTO)
            {
                PostSearchTag pSrhTag = new()
                {
                    PostId = postObj.postId,
                    PostSearchTagName = item.postSearchTagName,
                    PostSearchTagCreatedBy = uId,
                    PostSearchTagCreatedDate = DateTime.Now
                };
                dc.PostSearchTags.Add(pSrhTag);
                await dc.SaveChangesAsync();
            }

            var attDel = (from pAtt in dc.PostAttachments where pAtt.PostId == postObj.postId select pAtt).ToList();
            dc.PostAttachments.RemoveRange(attDel);
            await dc.SaveChangesAsync();

            foreach (var item in postAttachmentDTO)
            {
                PostAttachment pAttachfile = new()
                {
                    PostId = postObj.postId,
                    PostAttachmentName = item.postAttachmentName,
                    PostAttachmentDocType = item.postAttachmentDocType,
                    PostAttachmentDocExt = item.postAttachmentDocExt,
                    PostAttachmentDocSize = item.postAttachmentDocSize,
                    PostAttachmentUrl = item.postAttachmentDocURL,
                    PostAttachmentCreatedBy = uId,
                    PostAttachmentCreatedDate = DateTime.Now
                };
                dc.PostAttachments.Add(pAttachfile);
                await dc.SaveChangesAsync();
                pAttachfile.PostAttachmentCode = $"{pAttachfile.PostAttachmentId:000}";
                await dc.SaveChangesAsync();
            }
        }

        return await Task.FromResult(true);
    }

    public async Task<bool> savePostForPoll(PostDTO postDTO, List<PostPollDTO> postPollDTO, int postId, string userId)
    {
        var uId = dc.Users.SingleOrDefault(x => x.UserId == userId)!.Id;
        if (dc.Posts.Any(x => x.PostId == postDTO.postId) == false)
        {
            var p = new Post
            {
                PostName = postDTO.postName,
                PostRemarks = postDTO.postRemarks,
                PostContentText = "",
                PostContentHtml = "",
                PostThumbnailImagePath = "",
                PostHeadImagePath = "",
                PostIconImagePath = "",
                PostSectionId = (int)Section.Poll,
                PostCategoryId = postDTO.postCategoryId,
                PostCreatedBy = uId,
                PostCreatedDate = DateTime.Now,
                PostPublishDate = postDTO.postPublishDate,
                PostExpiryDate = postDTO.postExpiryDate,
                PostIsActive = "ACTIVE",
                PostIsDel = "NO"
            };
            dc.Posts.Add(p);
            await dc.SaveChangesAsync();
            postId = p.PostId;

            foreach (var item in postPollDTO)
            {
                PostPoll pPoll = new()
                {
                    PostPollName = item.PostPollName,
                    PostPollCreatedBy = uId,
                    PostPollCreatedDate = DateTime.Now,
                    PostPollIsActive = "ACTIVE",
                    PostPollIsDel = "NO",
                    PostId = postId
                };
                dc.PostPolls.Add(pPoll);
                await dc.SaveChangesAsync();
            }
        }
        else /// UPDATE Record Code
        {
            var p = dc.Posts.First(x => x.PostId == postDTO.postId);
            p.PostName = postDTO.postName;
            p.PostRemarks = postDTO.postRemarks;
            p.PostContentText = "";
            p.PostContentHtml = "";
            p.PostThumbnailImagePath = "";
            p.PostHeadImagePath = "";
            p.PostIconImagePath = "";
            p.PostSectionId = (int)Section.Poll;
            p.PostCategoryId = postDTO.postCategoryId;
            p.PostModifiedBy = uId;
            p.PostModifiedDate = DateTime.Now;
            p.PostPublishDate = postDTO.postPublishDate;
            p.PostExpiryDate = postDTO.postExpiryDate;
            await dc.SaveChangesAsync();
            postId = p.PostId;

            var attDel = (from pAtt in dc.PostPolls where pAtt.PostId == postDTO.postId select pAtt).ToList();
            dc.PostPolls.RemoveRange(attDel);
            await dc.SaveChangesAsync();

            foreach (var item in postPollDTO)
            {
                PostPoll pPoll = new()
                {
                    PostPollName = item.PostPollName,
                    PostPollCreatedBy = uId,
                    PostPollCreatedDate = DateTime.Now,
                    PostPollIsActive = "ACTIVE",
                    PostPollIsDel = "NO",
                    PostId = postId
                };
                dc.PostPolls.Add(pPoll);
                await dc.SaveChangesAsync();
            }
        }

        return await Task.FromResult(true);
    }

    public Task<List<RoleDTO>> GetAllRolesAsyncWithSelectedPost(int postId)
    {
        var q = (from x in dc.Roles
            where x.IsActive == true
            orderby x.Name
            select new RoleDTO
            {
                IsActive = x.IsActive,
                Notes = x.Notes ?? "",
                Name = x.Name,
                Code = $"{x.Id:000}", // x.Code,
                Id = x.Id,
                isSelect = false
            }).ToList();

        List<RoleDTO> g = (from x in dc.Roles
            join ur in dc.PostRoles on x.Id equals ur.RoleId
            join u in dc.Posts on ur.PostId equals u.PostId
            where u.PostId == postId
            select new RoleDTO
            {
                Id = x.Id
            }).ToList();

        foreach (var item in q)
        {
            var role = g.FirstOrDefault(g => g.Id == item.Id);
            item.isSelect = role != null;
        }

        return Task.FromResult(q);
    }

    public Task<string> savePostRolesInPostScreen(List<RoleDTO> roles, int postId)
    {
        var q = dc.PostRoles.Where(ur => ur.PostId == postId).ToList();
        dc.PostRoles.RemoveRange(q);
        dc.SaveChanges();

        var saveObj = roles.Where(r => r.isSelect).ToList();
        foreach (var item in saveObj)
        {
            var ur = new PostRole
            {
                PostId = postId,
                RoleId = item.Id
            };
            dc.PostRoles.Add(ur);
            dc.SaveChanges();
        }

        return Task.FromResult("Ok");
    }


    public Task<List<SubCategoryDto>> GetSubCategories(int? categoryId)
    {
        if (categoryId == null)
            return Task.FromResult(new List<SubCategoryDto>());
        var q = (from a in dc.SubCategories
            where a.CategoryId == (categoryId ?? 0)
            orderby a.Title
            select new SubCategoryDto
            {
                Id = a.Id,
                Title = a.Title
            }).ToList();
        return Task.FromResult(q);
    }
}