﻿@inject AppDataService service
@if (!string.IsNullOrEmpty(QuickAnnoucementUrl))
{
    <style>
        .chartcard2 {
            width: 100%;
            heigh: calc(100vh - 150px) !important;
            padding: 10px 15px;
            display: flex;
            overflow-y: auto;
            flex-direction: column;
        }

            .chartcard2 img {
                margin: auto;
                height: 450px;
            }
    </style>
    <div class="card chartcard2" style="max-height:unset !important">

        <div class="row">
            <div class="col" style="display:flex;height:calc(100vh - 180px)">
                @*<img src="/media/charts/1.png" />*@
                <iframe title="Quick Announcement" style="display: inline-block;width: 100%; height: calc(100vh - 175px)" frameborder="0" src='@QuickAnnoucementUrl'></iframe>


            </div>
        </div>
    </div>
}

@code {
    private string QuickAnnoucementUrl = "";

    protected override async Task OnInitializedAsync()
    {
        QuickAnnoucementUrl = await service.GetQuickAnnouncementUrl();
    }

}