﻿@page "/epage"
@using Microsoft.AspNetCore.Components.Web
@inject NavigationManager navigationManager
@implements IDisposable
@rendermode InteractiveServer

<iframe src="@url" style="height: calc(100vh - 90px); width: 100%" ></iframe>

@code {
    private string url { get; set; } = "";

    protected override void OnInitialized()
    {
        navigationManager.LocationChanged += LocationChanged;
        SetUrl();
    }

    private void LocationChanged(object? sender, LocationChangedEventArgs e)
    {
        SetUrl();
        StateHasChanged();
    }

    private void SetUrl()
    {
        var uri = new Uri(navigationManager.Uri);
        url = Microsoft.AspNetCore.WebUtilities.QueryHelpers.ParseQuery(uri.Query).TryGetValue("url", out var urlValue) ? urlValue.First() : "";
    }

    public void Dispose()
    {
        navigationManager.LocationChanged -= LocationChanged;
    }
}