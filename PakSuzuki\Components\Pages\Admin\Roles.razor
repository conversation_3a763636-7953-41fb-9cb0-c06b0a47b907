﻿@page "/admin/roles"
@using ButtonType = MudBlazor.ButtonType
@using ChangeEventArgs = Microsoft.AspNetCore.Components.ChangeEventArgs
@using FilterType = Syncfusion.Blazor.Grids.FilterType
@using SelectionType = Syncfusion.Blazor.Grids.SelectionType
@inject AdminDataService Service
@inject SfDialogService DialogService
@inject CorporateService CpService
@* @rendermode InteractiveServer *@

<SfToast @ref="toastObj"></SfToast>

<SfDialog @ref="dlgForm" Visible="false" Width="95vw" ShowCloseIcon="true" CloseOnEscape="true" IsModal="true">
    <DialogTemplates>
        <Header>@formTitle</Header>
        <Content>
            <EditForm Model="@obj" OnValidSubmit="SaveData">
                <DataAnnotationsValidator/>
                <div class="row" style="align-items:center;">
                    @*<div class="col-md-2 mb-2">
                        <MudTextField Label="Code" ReadOnly="true" @bind-Value="obj.Code" />
                        </div>*@
                    <div class="col-md mb-2">
                        <SfTextBox Placeholder="Title" FloatLabelType="FloatLabelType.Auto" @bind-Value="obj.Name"/>
                        <ValidationMessage For="@(() => obj.Name)"/>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md mb-2">
                        <SfTextBox Placeholder="Notes" FloatLabelType="FloatLabelType.Auto" @bind-Value="obj.Notes"
                                   Multiline="true"/>
                    </div>
                </div>
                <div style="height:12px;"></div>
                <div class="row mb-2">
                    <div class="col-md">
                        <div class="mb-2">Active</div>
                        <SfSwitch @bind-Checked="obj.IsActive"></SfSwitch> @obj.Active
                    </div>
                </div>

                <div class="row">
                    <div class="col-md mb-2">
                        <MudButton ButtonType="ButtonType.Submit" Color="Color.Primary"
                                   Variant="Variant.Filled" Size="Size.Small" StartIcon="@Icons.Material.Filled.Save">
                            Save
                        </MudButton>
                        <MudButton ButtonType="ButtonType.Button" Color="Color.Success"
                                   Variant="Variant.Filled" Size="Size.Small"
                                   OnClick="@(async () => { await dlgForm!.HideAsync(); })">Cancel
                        </MudButton>
                    </div>
                </div>
            </EditForm>
        </Content>
    </DialogTemplates>
</SfDialog>


<SfDialog @ref="dlgFormUser" Visible="false" Width="95%" ShowCloseIcon="true" CloseOnEscape="true" IsModal="true">
    <DialogTemplates>
        <Header>Manage User on @selectedRole Roles</Header>
        <Content>
            <div style="display:flex;gap:10px;">
                <div style="flex:1">
                    <MudText Typo="Typo.h5" Align="Align.Center">Available Users</MudText>
                    <SfGrid @ref="dgMainAllUser" Width="100%" Height="400px" AllowTextWrap="true" DataSource="allUsers"
                            AllowSelection="true" AllowFiltering="true" AllowSorting="false">
                        <GridFilterSettings Type="FilterType.Excel"></GridFilterSettings>
                        <GridSelectionSettings
                            Type="SelectionType.Multiple"></GridSelectionSettings>
                        <GridColumns>
                            <GridColumn Width="80px" HeaderText="Code" Field="@nameof(UserDTO.UserId)"></GridColumn>
                            <GridColumn Width="100px" HeaderText="Name" Field="@nameof(UserDTO.Name)"></GridColumn>
                            <GridColumn Width="100px" HeaderText="Email" Field="@nameof(UserDTO.Email)"></GridColumn>
                            <GridColumn Width="100px" HeaderText="Department"
                                        Field="@nameof(UserDTO.Department)"></GridColumn>
                            <GridColumn Width="100px" HeaderText="Position"
                                        Field="@nameof(UserDTO.PosText)"></GridColumn>
                        </GridColumns>
                    </SfGrid>
                </div>
                <div class="mid-btn-panel" style="align-content: center !important;width:45px">
                    <MudFab Size="Size.Small" Color="Color.Primary" SrartIcon="@Icons.Material.Filled.ArrowRight"
                            OnClick="addUser"></MudFab>
                    &nbsp;
                    <MudFab Size="Size.Small" Color="Color.Secondary" StartIcon="@Icons.Material.Filled.ArrowLeft"
                            OnClick="delUser"></MudFab>
                </div>
                <div style="flex:1">
                    <MudText Typo="Typo.h5" Align="Align.Center">Assigned Users</MudText>
                    <SfGrid @ref="dgMainSelUser" Width="100%" Height="400px" AllowTextWrap="true" DataSource="selUsers"
                            AllowSelection="true" AllowFiltering="true" AllowSorting="false">
                        <GridFilterSettings Type="FilterType.Excel"></GridFilterSettings>
                        <GridSelectionSettings
                            Type="SelectionType.Multiple"></GridSelectionSettings>
                        <GridColumns>
                            <GridColumn Width="80px" HeaderText="Code" Field="@nameof(UserDTO.UserId)"></GridColumn>
                            <GridColumn Width="100px" HeaderText="Name" Field="@nameof(UserDTO.Name)"></GridColumn>
                            <GridColumn Width="100px" HeaderText="Email" Field="@nameof(UserDTO.Email)"></GridColumn>
                            <GridColumn Width="100px" HeaderText="Department"
                                        Field="@nameof(UserDTO.Department)"></GridColumn>
                            <GridColumn Width="100px" HeaderText="Position"
                                        Field="@nameof(UserDTO.PosText)"></GridColumn>
                        </GridColumns>
                    </SfGrid>
                </div>
            </div>
        </Content>
    </DialogTemplates>
</SfDialog>

<SfDialog @ref="dlgFormMenu" Visible="false" Width="1100px" ShowCloseIcon="true" CloseOnEscape="true" IsModal="true">
    <DialogTemplates>
        <Header>Assigned Menu on @selectedRole</Header>
        <Content>

            <div style="display:flex">
                <div>
                    <MudText Typo="Typo.h6" Align="Align.Center">Available Menu</MudText>
                    @*                     <SfGrid @ref="dgMainAllMenu" Height="400px" DataSource="allMenus" AllowSelection="true" AllowFiltering="true" AllowSorting="false">
                        <GridFilterSettings Type="Syncfusion.Blazor.Grids.FilterType.Excel"></GridFilterSettings>
                        <GridColumns>
                        <GridColumn AutoFit="true" HeaderText="Module" Field="@nameof(MenuDTO.ModuleName)"></GridColumn>
                        <GridColumn HeaderText="Menu" Field="@nameof(MenuDTO.Name)"></GridColumn>
                        <GridColumn AutoFit="true" HeaderText="Device" Field="@nameof(MenuDTO.Device)"></GridColumn>
                        </GridColumns>
                        </SfGrid> *@
                    <SfTreeGrid DataSource="allMenus" @ref="dgMainAllMenutrv" Height="400px"
                                IdMapping="@nameof(MenuDTO.Id)" ParentIdMapping="@nameof(MenuDTO.ParentId)"
                                TreeColumnIndex="0" AllowFiltering="true" AllowSorting="true">
                        <TreeGridFilterSettings Type="Syncfusion.Blazor.TreeGrid.FilterType.Excel"/>
                        <TreeGridSelectionSettings
                            Type="SelectionType.Multiple"></TreeGridSelectionSettings>
                        <TreeGridColumns>
                            <TreeGridColumn Width="300px" HeaderText="Name"
                                            Field="@nameof(MenuDTO.Name)"></TreeGridColumn>
                            <TreeGridColumn Width="100px" HeaderText="Code" Format="000"
                                            Field="@nameof(MenuDTO.Code)"></TreeGridColumn>
                            <TreeGridColumn Width="100px" HeaderText="Module"
                                            Field="@nameof(MenuDTO.ModuleName)"></TreeGridColumn>
                        </TreeGridColumns>
                    </SfTreeGrid>
                </div>
                <div class="mid-btn-panel" style="align-content: center !important;">
                    <MudFab Size="Size.Small" Color="Color.Primary" StartIcon="@Icons.Material.Filled.ArrowRight"
                            OnClick="addMenu"></MudFab>
                    &nbsp;
                    <MudFab Size="Size.Small" Color="Color.Secondary" StartIcon="@Icons.Material.Filled.ArrowLeft"
                            OnClick="delMenu"></MudFab>
                </div>
                <div>
                    <MudText Typo="Typo.h6" Align="Align.Center">Assigned Menu</MudText>
                    @* <SfGrid @ref="dgMainSelMenu" Height="400px" DataSource="selMenus" AllowSelection="true" AllowFiltering="true" AllowSorting="false">
                        <GridFilterSettings Type="Syncfusion.Blazor.Grids.FilterType.Excel"></GridFilterSettings>
                        <GridColumns>
                        <GridColumn AutoFit="true" HeaderText="Module" Field="@nameof(MenuDTO.ModuleName)"></GridColumn>
                        <GridColumn HeaderText="Menu" Field="@nameof(MenuDTO.Name)"></GridColumn>
                        <GridColumn AutoFit="true" HeaderText="Device" Field="@nameof(MenuDTO.Device)"></GridColumn>
                        </GridColumns>
                        </SfGrid> *@
                    <SfTreeGrid DataSource="selMenus" @ref="dgMainSelMenutrv" Height="400px"
                                IdMapping="@nameof(MenuDTO.Id)" ParentIdMapping="@nameof(MenuDTO.ParentId)"
                                TreeColumnIndex="0" AllowFiltering="true" AllowSorting="true">
                        <TreeGridFilterSettings Type="Syncfusion.Blazor.TreeGrid.FilterType.Excel"/>
                        <TreeGridSelectionSettings
                            Type="SelectionType.Multiple"></TreeGridSelectionSettings>

                        <TreeGridColumns>
                            <TreeGridColumn Width="300px" HeaderText="Name"
                                            Field="@nameof(MenuDTO.Name)"></TreeGridColumn>
                            <TreeGridColumn Width="100px" HeaderText="Code" Format="000"
                                            Field="@nameof(MenuDTO.Code)"></TreeGridColumn>
                            <TreeGridColumn Width="100px" HeaderText="Module"
                                            Field="@nameof(MenuDTO.ModuleName)"></TreeGridColumn>
                        </TreeGridColumns>
                    </SfTreeGrid>
                </div>
            </div>
        </Content>
    </DialogTemplates>
</SfDialog>

<SfDialog @ref="dlgFormSection" Visible="false" Width="500px" ShowCloseIcon="true" CloseOnEscape="true" IsModal="true">
    <DialogTemplates>
        <Header>Assign Section</Header>
        <Content>
            <div class="row" style="display:flex">
                <MudText Typo="Typo.caption">Selected Role: <b>@selectedRole</b></MudText>
            </div>
            <div style="height:10px;"></div>
            <div class="row" style="text-align:right;">
                <SfCheckBox @bind-Checked="isChecked" Label="Select All" @onchange="selectAll">Select All</SfCheckBox>
            </div>
            <div style="height:10px;"></div>
            <div class="row">
                <SfGrid @ref="dgMainSection" Height="400px" DataSource="allSections" AllowSelection="true"
                        AllowFiltering="true" AllowSorting="true" Width="500px">
                    <GridFilterSettings Type="FilterType.Excel"></GridFilterSettings>
                    <GridColumns>
                        <GridColumn AutoFit="true" Visible="false" HeaderText="Id"
                                    Field="@nameof(SectionDto.Id)"></GridColumn>
                        <GridColumn Width="350" HeaderText="Roles" Field="@nameof(SectionDto.Name)"></GridColumn>
                        <GridColumn Field="@nameof(SectionDto.isSelect)" HeaderTextAlign="TextAlign.Center"
                                    HeaderText="Selected" Width="120" ClipMode="ClipMode.EllipsisWithTooltip">
                            <Template>
                                @{
                                    if (context is SectionDto ctx)
                                    {


                                        <div>
                                            <div style="text-align: center;">
                                                <SfCheckBox @bind-Checked="@ctx.isSelect"></SfCheckBox>
                                            </div>
                                        </div>
                                    }
                                }
                            </Template>
                        </GridColumn>
                    </GridColumns>
                </SfGrid>

            </div>
            <div class="page-panel">
                <div>
                    <MudButton Size="Size.Small" Color="Color.Primary" StartIcon="@Icons.Material.Filled.Save"
                               Variant="Variant.Filled" OnClick="SaveRoleSection">Save
                    </MudButton>
                    <MudButton Size="Size.Small" Color="Color.Success" StartIcon="@Icons.Material.Filled.Cancel"
                               Variant="Variant.Filled" OnClick="() => dlgFormSection!.HideAsync()">Cancel
                    </MudButton>
                </div>
            </div>
        </Content>
    </DialogTemplates>
</SfDialog>

<div class="row mb-2">
    <div class="col-md">
        <MudText Typo="Typo.h5">Application Roles Management</MudText>
    </div>
</div>

<div class="row mb-2">
    <div class="col-md">
        <MudButton Size="Size.Small" Color="Color.Primary" StartIcon="@Icons.Material.Filled.Add"
                   Variant="Variant.Filled" OnClick="OpenCreateForm">Add
        </MudButton>
        <MudButton Size="Size.Small" Color="Color.Success" StartIcon="@Icons.Material.Filled.Edit"
                   Variant="Variant.Filled" OnClick="OpenEditForm">Edit
        </MudButton>
        <MudButton Size="Size.Small" Color="Color.Warning" StartIcon="@Icons.Material.Filled.Delete"
                   Variant="Variant.Filled" OnClick="ConfirmDelete">Delete
        </MudButton>
        <MudButton Size="Size.Small" Color="Color.Secondary" StartIcon="@Icons.Material.Filled.Person"
                   Variant="Variant.Filled" OnClick="ManageUser">Users
        </MudButton>
        <MudButton Size="Size.Small" Color="Color.Info" StartIcon="@Icons.Material.Filled.Menu" Variant="Variant.Filled"
                   OnClick="ManageMenu">Menus
        </MudButton>
        <MudButton Size="Size.Small" Color="Color.Tertiary" StartIcon="@Icons.Material.Filled.Settings"
                   Variant="Variant.Filled" OnClick="openAssignRole">Sections
        </MudButton>
        @* <MudButton Size="Size.Small" Color="Color.Tertiary" StartIcon="@Icons.Material.Filled.PostAdd" Variant="Variant.Filled" OnClick="ManageMenu">Posts</MudButton>  *@
    </div>

</div>
<div class="row">
    <div class="col-md">
        <SfGrid Height="calc(100vh - 220px)" @ref="dgMain" DataSource="allRoles" AllowSelection="true"
                AllowFiltering="true" AllowSorting="true" Width="100%">
            <GridFilterSettings Type="FilterType.Excel"></GridFilterSettings>
            <GridColumns>
                <GridColumn AutoFit="true" HeaderText="Code" Field="@nameof(RoleDTO.Code)"></GridColumn>
                <GridColumn AutoFit="true" HeaderText="Name" Field="@nameof(RoleDTO.Name)"></GridColumn>
                <GridColumn AutoFit="true" HeaderText="Active" Field="@nameof(RoleDTO.Active)"></GridColumn>
                <GridColumn CustomAttributes="@(new Dictionary<string, object> { { "class", "e-notecol" } })"
                            HeaderText="Notes" Field="@nameof(RoleDTO.Notes)"></GridColumn>
            </GridColumns>
        </SfGrid>
    </div>
</div>
<style>
    .e-notecol {
        width: 400px !important;
    }
</style>

@code {
    private string searchText = "";
    private List<RoleDTO> allRoles = new();
    private List<UserDTO> allUsers = new();
    private List<UserDTO> selUsers = new();
    private List<MenuDTO> allMenus = new();
    private List<MenuDTO> selMenus = new();
    private List<SectionDto> allSections = new();
    private RoleDTO obj = new();
    private int selectedRoleId;

    private string selectedRole = "";
    //[CascadingParameter] public MainLayout Layout { get; set; }
    //[CascadingParameter] private Task<AuthenticationState> authenticationStateTask { get; set; }

    private string formTitle = "Add Role";


    SfDialog? dlgForm;
    SfDialog? dlgFormUser, dlgFormMenu, dlgFormSection;
    SfGrid<RoleDTO>? dgMain;
    SfGrid<SectionDto>? dgMainSection;
    SfGrid<UserDTO>? dgMainAllUser;
    SfGrid<UserDTO>? dgMainSelUser;
    SfTreeGrid<MenuDTO>? dgMainAllMenutrv;
    SfTreeGrid<MenuDTO>? dgMainSelMenutrv;
    private SfToast? toastObj;

    bool isChecked;
    bool IsFormEditable = true;

    private async void selectAll(ChangeEventArgs args)
    {
        try
        {
            foreach (var item in allSections)
            {
                item.isSelect = isChecked;
            }

            StateHasChanged();
        }
        catch (Exception ex)
        {
            var tm = new ToastModel { Content = ex.Message, Title = "Error", ShowCloseButton = true, Timeout = 0 };
            await toastObj!.ShowAsync(tm);
        }
    }

    private async Task SaveRoleSection()
    {
        try
        {
            await Service.savePostSectionInRoleScreen(allSections, selectedRoleId);
            var tm = new ToastModel { Content = "Section assigned successfully.", Title = "Info", ShowCloseButton = true, Timeout = 0 };
            await toastObj!.ShowAsync(tm);
            await dlgFormSection!.HideAsync();
            selectedRoleId = 0;
            selectedRole = "";
        }
        catch (Exception ex)
        {
            var tm = new ToastModel { Content = ex.Message, Title = "Error", ShowCloseButton = true, Timeout = 0 };
            await toastObj!.ShowAsync(tm);
        }
    }

    private async Task openAssignRole()
    {
        try
        {
            if (dgMain!.SelectedRecords is { Count: > 0 })
            {
                var pId = dgMain.SelectedRecords[0].Id;
                selectedRoleId = pId;
                selectedRole = dgMain.SelectedRecords[0].Name;
                allSections = await Service.GetAllSectionAsyncWithSelectedRole(pId);
                //obj = await service.GetRoleAsync(pId);
                await dlgFormSection!.ShowAsync();
            }
            else
            {
                var tm = new ToastModel { Content = "Please select Role.", Title = "Info", ShowCloseButton = true, Timeout = 0 };
                await toastObj!.ShowAsync(tm);
            }
        }
        catch (Exception ex)
        {
            var tm = new ToastModel { Content = ex.Message, Title = "Error", ShowCloseButton = true, Timeout = 0 };
            await toastObj!.ShowAsync(tm);
        }
    }

    private async Task addUser()
    {
        try
        {
            if (dgMainAllUser!.SelectedRecords is { Count: > 0 })
            {
                // Save all selected records
                var uId = dgMainAllUser.SelectedRecords[0].Id;
                foreach (var item in dgMainAllUser.SelectedRecords)
                {
                    var result = await Service.SaveUserRoleAsync(item.Id, selectedRoleId);
                }

                //var result = await service.SaveUserRoleAsync(uId, selectedRoleId);
                await BindUsers(selectedRoleId);
            }
        }
        catch (Exception ex)
        {
            var tm = new ToastModel { Content = ex.Message, Title = "Error", ShowCloseButton = true, Timeout = 0 };
            await toastObj!.ShowAsync(tm);
        }
    }

    private async Task delUser()
    {
        try
        {
            if (dgMainSelUser!.SelectedRecords is { Count: > 0 })
            {
                //int uId = dgMainSelUser.SelectedRecords[0].Id;
                // save all selected records
                foreach (var item in dgMainSelUser.SelectedRecords)
                {
                    var result = await Service.DeleteUserRoleAsync(item.Id, selectedRoleId);
                }

                //var result = await service.DeleteUserRoleAsync(uId, selectedRoleId);
                await BindUsers(selectedRoleId);
            }
        }
        catch (Exception ex)
        {
            var tm = new ToastModel { Content = ex.Message, Title = "Error", ShowCloseButton = true, Timeout = 0 };
            await toastObj!.ShowAsync(tm);
        }
    }

    private async Task BindUsers(int roleId)
    {
        //AllEmployees = await cpService.GetAllEmployeeData();
        allUsers = await Service.GetAllActiveAsync();
        selUsers = await Service.GetUserRoleAsync(roleId);

        // selected users Id list
        var selUserIds = selUsers.Select(x => x.Id).ToList();
        // remove selected users from all users
        allUsers = allUsers.Where(x => !selUserIds.Contains(x.Id)).OrderBy(c => c.Name).ToList();
        selUsers = selUsers.OrderBy(c => c.Name).ToList();
        //foreach (UserDTO u in selUsers)
        //{
        //    allUsers.Remove(allUsers.FirstOrDefault(f => f.Id == u.Id)!);
        //}

        foreach (var au in allUsers)
        {
            var m = AllEmployees.FirstOrDefault(m => au.UserId.EndsWith(m.EmpCode));
            if (m != null)
            {
                au.Department = m.DepartmentName;
                au.PosText = m.PosText;
            }
        }

        foreach (var au in selUsers)
        {
            var m = AllEmployees.FirstOrDefault(m => au.UserId.EndsWith(m.EmpCode));
            if (m != null)
            {
                au.Department = m.DepartmentName;
                au.PosText = m.PosText;
            }
        }
    }

    private async Task ManageUser()
    {
        try
        {
            if (dgMain!.SelectedRecords is { Count: > 0 })
            {
                var id = dgMain.SelectedRecords[0].Id;
                selectedRoleId = id;
                selectedRole = dgMain.SelectedRecords[0].Name;
                await BindUsers(id);
                await dlgFormUser!.ShowAsync();
            }
            else
            {
                var tm = new ToastModel { Content = "Please select a role.", Title = "Info", ShowCloseButton = true, Timeout = 0 };
                await toastObj!.ShowAsync(tm);
            }
        }
        catch (Exception ex)
        {
            var tm = new ToastModel { Content = ex.Message, Title = "Error", ShowCloseButton = true, Timeout = 0 };
            await toastObj!.ShowAsync(tm);
        }
    }

    private async Task ManageMenu()
    {
        try
        {
            if (dgMain!.SelectedRecords is { Count: > 0 })
            {
                var id = dgMain.SelectedRecords[0].Id;
                selectedRoleId = id;
                selectedRole = dgMain.SelectedRecords[0].Name;
                BindMenus(id);
                await dlgFormMenu!.ShowAsync();
            }
            else
            {
                var tm = new ToastModel { Content = "Please select a role.", Title = "Info", ShowCloseButton = true, Timeout = 0 };
                await toastObj!.ShowAsync(tm);
            }
        }
        catch (Exception ex)
        {
            var tm = new ToastModel { Content = ex.Message, Title = "Error", ShowCloseButton = true, Timeout = 0 };
            await toastObj!.ShowAsync(tm);
        }
    }

    private async Task addMenu()
    {
        try
        {
            List<MenuDTO> selectedRec = await dgMainAllMenutrv!.GetSelectedRecordsAsync();
            if (selectedRec is { Count: > 0 })
            {
                foreach (var mi in selectedRec)
                {
                    var result = await Service.SaveRoleMenuAsync(mi.Id, selectedRoleId);
                }

                await BindMenus(selectedRoleId);

                //int mId = selectedRec[0].Id;
                ////int mParentId = (int)selectedRec[0].ParentId!;
                ////if (mParentId != 0) { }
                //if (selMenus.Any(x => x.Id == mId) == false)
                //{
                //    var result = await service.SaveRoleMenuAsync(mId, selectedRoleId);
                //}
                //await BindMenus(selectedRoleId);
            }
        }
        catch (Exception ex)
        {
            var tm = new ToastModel { Content = ex.Message, Title = "Error", ShowCloseButton = true, Timeout = 0 };
            await toastObj!.ShowAsync(tm);
        }
    }

    private async Task delMenu()
    {
        try
        {
            List<MenuDTO> selectedRec = await dgMainSelMenutrv!.GetSelectedRecordsAsync();
            foreach (var mi in selectedRec)
            {
                var result = await Service.DeleteRoleMenuAsync(mi.Id, selectedRoleId);
            }

            await BindMenus(selectedRoleId);
            //if (selectedRec is { Count: > 0 })
            //{
            //    int mId = selectedRec[0].Id;
            //    var result = await service.DeleteRoleMenuAsync(mId, selectedRoleId);
            //    await BindMenus(selectedRoleId);
            //}
        }
        catch (Exception ex)
        {
            var tm = new ToastModel { Content = ex.Message, Title = "Error", ShowCloseButton = true, Timeout = 0 };
            await toastObj!.ShowAsync(tm);
        }
    }

    private async Task BindMenus(int roleId)
    {
        allMenus = await Service.GetAllActiveMenusAsync();
        selMenus = await Service.GetRoleMenuAsync(roleId);

        //foreach (MenuDTO u in selMenus)
        //{
        //  allMenus.Remove(allMenus.FirstOrDefault(f => f.Id == u.Id)!);
        //}
    }

    //DataTable allUserInfo = new DataTable();
    private List<Employee2Dto> AllEmployees = new();

    protected override async Task OnInitializedAsync()
    {
        try
        {
            allRoles = await Service.GetAllRolesAsync();
            AllEmployees = await CpService.GetAllEmployeeData();
            var res = await CpService.UpdateUsers(AllEmployees);
            //Layout.PageCaption = "Roles";
        }
        catch (Exception ex)
        {
            var tm = new ToastModel { Content = ex.Message, Title = "Error", ShowCloseButton = true, Timeout = 0 };
            await toastObj!.ShowAsync(tm);
        }
    }

    private async Task OpenCreateForm()
    {
        IsFormEditable = true;
        formTitle = "Add Role";
        try
        {
            obj = new RoleDTO { IsActive = false };
            await dlgForm!.ShowAsync();
        }
        catch (Exception ex)
        {
            var tm = new ToastModel { Content = ex.Message, Title = "Error", ShowCloseButton = true, Timeout = 0 };
            await toastObj!.ShowAsync(tm);
        }
    }


    public async Task SaveData()
    {
        // var user = (await authenticationStateTask).User;
        // var op = "";
        // int loginUserId = 0;
        // try
        // {
        //    loginUserId = int.Parse(user.Claims.FirstOrDefault(c => c.Type == "Id").Value);

        // }
        // catch (Exception ex)
        // {
        //    op = ex.Message;
        // }

        // if (op != "")
        // {
        //    var tm = new ToastModel { Content = op, Title = "Error", ShowCloseButton = true, Timeout = 0 };
        //    await toastObj.ShowAsync(tm);
        //    return;
        // }


        try
        {
            var vm = await Service.IsRoleValid(obj);
            if (vm == "OK")
            {
                var res = await Service.SaveRoleAsync(obj, 1);
                allRoles = await Service.GetAllRolesAsync();
                await dlgForm!.HideAsync();
            }
            else
            {
                var tm = new ToastModel { Content = vm, Title = "Error", ShowCloseButton = true, Timeout = 0 };
                await toastObj!.ShowAsync(tm);
            }
        }
        catch (Exception ex)
        {
            var tm = new ToastModel { Content = ex.Message, Title = "Error", ShowCloseButton = true, Timeout = 0 };
            await toastObj!.ShowAsync(tm);
        }
    }

    private async Task OpenEditForm()
    {
        formTitle = "Edit Role";
        try
        {
            if (dgMain!.SelectedRecords is { Count: > 0 })
            {
                var id = dgMain.SelectedRecords[0].Id;
                obj = await Service.GetRoleAsync(id);
                await dlgForm!.ShowAsync();
            }
            else
            {
                var tm = new ToastModel { Content = "Please select a role.", Title = "Info", ShowCloseButton = true, Timeout = 0 };
                await toastObj!.ShowAsync(tm);
            }
        }
        catch (Exception ex)
        {
            var tm = new ToastModel { Content = ex.Message, Title = "Error", ShowCloseButton = true, Timeout = 0 };
            await toastObj!.ShowAsync(tm);
        }
    }

    private async Task ConfirmDelete()
    {
        try
        {
            if (dgMain!.SelectedRecords is { Count: > 0 })
            {
                var conf = await DialogService.ConfirmAsync("Are you sure want to delete this record", "Confirm");
                var id = dgMain.SelectedRecords[0].Id;
                if (conf)
                {
                    var res = await Service.DeleteRoleAsync(id);
                    if (res == "OK")
                    {
                        allRoles = await Service.GetAllRolesAsync();
                    }
                    else
                    {
                        var mm = new ToastModel { Content = "The entity cannot be deleted because it is being used by other entities.", Title = "Error", ShowCloseButton = true, Timeout = 0 };
                        await toastObj!.ShowAsync(mm);
                    }
                }
            }
            else
            {
                var tm = new ToastModel { Content = "Please select a role.", Title = "Info", ShowCloseButton = true, Timeout = 0 };
                await toastObj!.ShowAsync(tm);
            }
        }
        catch (Exception ex)
        {
            var tm = new ToastModel { Content = ex.Message, Title = "Error", ShowCloseButton = true, Timeout = 0 };
            await toastObj!.ShowAsync(tm);
        }
    }

}

<style>
    .e-grid td.e-active {
        background: #faa601 !important;
    }
</style>